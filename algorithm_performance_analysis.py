#!/usr/bin/env python3
"""
Analyse de performance de l'algorithme BOM-PCM Voyager
Métriques: Précision, Rappel, F1-Score, Faux Positifs, Faux Négatifs
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.metrics import precision_recall_curve, roc_curve, auc
import re
import warnings
warnings.filterwarnings('ignore')

class AlgorithmPerformanceAnalyzer:
    def __init__(self):
        """Initialize l'analyseur de performance"""
        self.bom_data = None
        self.pcm_data = None
        self.results_data = None
        self.sbert_model = None
        self.performance_metrics = {}
        
    def load_data(self):
        """Charger toutes les données nécessaires"""
        print("📊 CHARGEMENT DES DONNÉES")
        print("=" * 40)
        
        try:
            # 1. Charger les résultats de matching
            self.results_data = pd.read_excel('output/voyager_complete_bom_pcm_table.xlsx', sheet_name='Complete_Table')
            print(f"✅ Résultats matching: {len(self.results_data)} lignes")
            
            # 2. Charger les codes PCM extraits
            self.pcm_data = pd.read_csv('output/real_voyager_pcm_codes.csv')
            print(f"✅ Codes PCM: {len(self.pcm_data)} codes")
            
            # 3. Charger la BOM originale
            self.bom_data = pd.read_excel('output/voyager_5408509_bom_pcm_aggregated.xlsx')
            print(f"✅ BOM originale: {len(self.bom_data)} items")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur chargement: {e}")
            return False
    
    def preprocess_descriptions(self, text):
        """Préprocesser les descriptions (étape 2)"""
        if pd.isna(text) or text == '':
            return ''
        
        # Conversion en minuscules
        text = str(text).lower()
        
        # Suppression de la ponctuation
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # Suppression des espaces multiples
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def load_sbert_model(self):
        """Charger le modèle SBERT (étape 3)"""
        if self.sbert_model is None:
            print("🧠 Chargement modèle SBERT...")
            self.sbert_model = SentenceTransformer('all-MiniLM-L6-v2')
            print("✅ Modèle SBERT chargé")
    
    def compute_similarities_with_threshold(self, threshold=0.6):
        """Calculer les similarités et appliquer le seuil (étapes 3-4)"""
        print(f"\n🔍 CALCUL SIMILARITÉS AVEC SEUIL {threshold}")
        print("=" * 50)
        
        self.load_sbert_model()
        
        # Préprocesser les descriptions BOM
        bom_descriptions = []
        for desc in self.results_data['Item_Description']:
            bom_descriptions.append(self.preprocess_descriptions(desc))
        
        # Préprocesser les descriptions PCM
        pcm_descriptions = []
        for desc in self.pcm_data['description']:
            pcm_descriptions.append(self.preprocess_descriptions(desc))
        
        print(f"📋 BOM descriptions préprocessées: {len(bom_descriptions)}")
        print(f"📋 PCM descriptions préprocessées: {len(pcm_descriptions)}")
        
        # Calculer les embeddings
        print("🧠 Calcul embeddings BOM...")
        bom_embeddings = self.sbert_model.encode(bom_descriptions)
        
        print("🧠 Calcul embeddings PCM...")
        pcm_embeddings = self.sbert_model.encode(pcm_descriptions)
        
        # Calculer la matrice de similarité
        print("📊 Calcul matrice de similarité...")
        similarity_matrix = cosine_similarity(bom_embeddings, pcm_embeddings)
        
        # Appliquer le seuil et créer les matches
        matches = []
        for i in range(len(bom_descriptions)):
            best_match_idx = np.argmax(similarity_matrix[i])
            best_similarity = similarity_matrix[i][best_match_idx]
            
            if best_similarity >= threshold:
                matches.append({
                    'bom_idx': i,
                    'pcm_idx': best_match_idx,
                    'similarity_score': best_similarity,
                    'bom_description': bom_descriptions[i],
                    'pcm_description': pcm_descriptions[best_match_idx],
                    'bom_item_id': self.results_data.iloc[i]['Item_ID'],
                    'pcm_code': self.pcm_data.iloc[best_match_idx]['code']
                })
        
        print(f"✅ {len(matches)} matches trouvés avec seuil {threshold}")
        return matches, similarity_matrix
    
    def create_ground_truth(self):
        """Créer la vérité terrain pour l'évaluation"""
        print("\n📋 CRÉATION VÉRITÉ TERRAIN")
        print("=" * 40)
        
        # Pour cette analyse, nous utilisons les matches existants comme référence
        # et nous identifions les "vrais positifs" basés sur des critères stricts
        
        ground_truth = []
        
        for idx, row in self.results_data.iterrows():
            bom_desc = str(row['Item_Description']).lower()
            pcm_desc = str(row['Description_PCM']).lower() if row['Description_PCM'] != '' else ''
            
            # Critères pour un "vrai match"
            is_true_match = False
            
            if pcm_desc != '':
                # 1. Correspondance exacte de mots clés
                bom_words = set(bom_desc.split())
                pcm_words = set(pcm_desc.split())
                
                # Mots clés importants
                important_words = {'head', 'neck', 'array', 'coil', 'table', 'system', 'controller', 
                                 'interface', 'board', 'module', 'cable', 'phantom', 'upgrade'}
                
                bom_important = bom_words.intersection(important_words)
                pcm_important = pcm_words.intersection(important_words)
                
                # Si au moins 2 mots importants correspondent
                if len(bom_important.intersection(pcm_important)) >= 2:
                    is_true_match = True
                
                # 2. Correspondance de codes similaires
                bom_codes = re.findall(r'[A-Z]\d{4,6}[A-Z]{0,2}', str(row['Item_ID']))
                pcm_codes = re.findall(r'[A-Z]\d{4,6}[A-Z]{0,2}', str(row['Item_ID_PCM']) if pd.notna(row['Item_ID_PCM']) else '')
                
                if bom_codes and pcm_codes:
                    # Vérifier si les codes sont similaires
                    for bom_code in bom_codes:
                        for pcm_code in pcm_codes:
                            if bom_code[:4] == pcm_code[:4]:  # Même préfixe
                                is_true_match = True
            
            ground_truth.append({
                'bom_idx': idx,
                'item_id': row['Item_ID'],
                'has_pcm_match': row['Item_ID_PCM'] != '',
                'similarity_score': row['Similarity_Score'],
                'is_true_match': is_true_match
            })
        
        ground_truth_df = pd.DataFrame(ground_truth)
        
        true_positives = len(ground_truth_df[(ground_truth_df['has_pcm_match']) & (ground_truth_df['is_true_match'])])
        false_positives = len(ground_truth_df[(ground_truth_df['has_pcm_match']) & (~ground_truth_df['is_true_match'])])
        false_negatives = len(ground_truth_df[(~ground_truth_df['has_pcm_match']) & (ground_truth_df['is_true_match'])])
        true_negatives = len(ground_truth_df[(~ground_truth_df['has_pcm_match']) & (~ground_truth_df['is_true_match'])])
        
        print(f"📊 Vérité terrain créée:")
        print(f"   Vrais Positifs: {true_positives}")
        print(f"   Faux Positifs: {false_positives}")
        print(f"   Faux Négatifs: {false_negatives}")
        print(f"   Vrais Négatifs: {true_negatives}")
        
        return ground_truth_df
    
    def calculate_performance_metrics(self, ground_truth_df):
        """Calculer les métriques de performance"""
        print("\n📈 CALCUL MÉTRIQUES DE PERFORMANCE")
        print("=" * 50)
        
        # Matrices de confusion
        tp = len(ground_truth_df[(ground_truth_df['has_pcm_match']) & (ground_truth_df['is_true_match'])])
        fp = len(ground_truth_df[(ground_truth_df['has_pcm_match']) & (~ground_truth_df['is_true_match'])])
        fn = len(ground_truth_df[(~ground_truth_df['has_pcm_match']) & (ground_truth_df['is_true_match'])])
        tn = len(ground_truth_df[(~ground_truth_df['has_pcm_match']) & (~ground_truth_df['is_true_match'])])
        
        # Métriques principales
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        accuracy = (tp + tn) / (tp + fp + fn + tn)
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        
        # Métriques de couverture
        total_bom_items = len(ground_truth_df)
        matched_items = len(ground_truth_df[ground_truth_df['has_pcm_match']])
        coverage_rate = (matched_items / total_bom_items) * 100
        
        # Métriques de qualité
        matched_data = ground_truth_df[ground_truth_df['has_pcm_match']]
        mean_similarity = matched_data['similarity_score'].mean() if len(matched_data) > 0 else 0
        std_similarity = matched_data['similarity_score'].std() if len(matched_data) > 0 else 0
        
        # Taux de correspondances exactes vs similaires
        exact_matches = len(ground_truth_df[ground_truth_df['similarity_score'] > 0.9])
        similar_matches = len(ground_truth_df[(ground_truth_df['similarity_score'] >= 0.7) & 
                                            (ground_truth_df['similarity_score'] <= 0.9)])
        weak_matches = len(ground_truth_df[(ground_truth_df['similarity_score'] >= 0.6) & 
                                          (ground_truth_df['similarity_score'] < 0.7)])
        
        metrics = {
            'confusion_matrix': {'TP': tp, 'FP': fp, 'FN': fn, 'TN': tn},
            'classification_metrics': {
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score,
                'accuracy': accuracy,
                'specificity': specificity
            },
            'coverage_metrics': {
                'total_bom_items': total_bom_items,
                'matched_items': matched_items,
                'coverage_rate': coverage_rate,
                'unmatched_rate': 100 - coverage_rate
            },
            'quality_metrics': {
                'mean_similarity': mean_similarity,
                'std_similarity': std_similarity,
                'exact_matches': exact_matches,
                'similar_matches': similar_matches,
                'weak_matches': weak_matches
            }
        }
        
        # Affichage des résultats
        print("🎯 MÉTRIQUES DE CLASSIFICATION:")
        print(f"   Précision: {precision:.3f}")
        print(f"   Rappel: {recall:.3f}")
        print(f"   F1-Score: {f1_score:.3f}")
        print(f"   Accuracy: {accuracy:.3f}")
        print(f"   Spécificité: {specificity:.3f}")
        
        print(f"\n📊 MATRICE DE CONFUSION:")
        print(f"   Vrais Positifs (TP): {tp}")
        print(f"   Faux Positifs (FP): {fp}")
        print(f"   Faux Négatifs (FN): {fn}")
        print(f"   Vrais Négatifs (TN): {tn}")
        
        print(f"\n📈 MÉTRIQUES DE COUVERTURE:")
        print(f"   Taux de couverture: {coverage_rate:.1f}%")
        print(f"   Items non matchés: {100-coverage_rate:.1f}%")
        
        print(f"\n🎯 MÉTRIQUES DE QUALITÉ:")
        print(f"   Similarité moyenne: {mean_similarity:.3f} ± {std_similarity:.3f}")
        print(f"   Matches exactes (>0.9): {exact_matches}")
        print(f"   Matches similaires (0.7-0.9): {similar_matches}")
        print(f"   Matches faibles (0.6-0.7): {weak_matches}")
        
        self.performance_metrics = metrics
        return metrics
    
    def create_performance_visualizations(self, ground_truth_df):
        """Créer les visualisations de performance"""
        print("\n📊 CRÉATION VISUALISATIONS PERFORMANCE")
        print("=" * 50)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Algorithm Performance Analysis: BOM-PCM Matching', fontsize=16, fontweight='bold')
        
        # 1. Matrice de confusion
        cm = self.performance_metrics['confusion_matrix']
        confusion_data = np.array([[cm['TP'], cm['FP']], [cm['FN'], cm['TN']]])
        
        sns.heatmap(confusion_data, annot=True, fmt='d', cmap='Blues', ax=ax1,
                   xticklabels=['Predicted Positive', 'Predicted Negative'],
                   yticklabels=['Actual Positive', 'Actual Negative'])
        ax1.set_title('Confusion Matrix', fontweight='bold')
        
        # 2. Métriques de performance
        metrics = self.performance_metrics['classification_metrics']
        metric_names = ['Precision', 'Recall', 'F1-Score', 'Accuracy', 'Specificity']
        metric_values = [metrics['precision'], metrics['recall'], metrics['f1_score'], 
                        metrics['accuracy'], metrics['specificity']]
        
        bars = ax2.bar(metric_names, metric_values, color=['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6'])
        ax2.set_title('Classification Metrics', fontweight='bold')
        ax2.set_ylabel('Score')
        ax2.set_ylim(0, 1)
        
        # Ajouter les valeurs sur les barres
        for bar, value in zip(bars, metric_values):
            ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom')
        
        # 3. Distribution des scores de similarité
        similarity_scores = ground_truth_df[ground_truth_df['has_pcm_match']]['similarity_score']
        
        ax3.hist(similarity_scores, bins=20, color='skyblue', alpha=0.7, edgecolor='black')
        ax3.axvline(similarity_scores.mean(), color='red', linestyle='--', 
                   label=f'Mean: {similarity_scores.mean():.3f}')
        ax3.set_title('Similarity Score Distribution', fontweight='bold')
        ax3.set_xlabel('Similarity Score')
        ax3.set_ylabel('Frequency')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. Qualité des matches
        quality_metrics = self.performance_metrics['quality_metrics']
        categories = ['Exact\n(>0.9)', 'Similar\n(0.7-0.9)', 'Weak\n(0.6-0.7)']
        values = [quality_metrics['exact_matches'], quality_metrics['similar_matches'], 
                 quality_metrics['weak_matches']]
        colors = ['#27ae60', '#f39c12', '#e74c3c']
        
        bars = ax4.bar(categories, values, color=colors)
        ax4.set_title('Match Quality Distribution', fontweight='bold')
        ax4.set_ylabel('Number of Matches')
        
        # Ajouter pourcentages
        total_matches = sum(values)
        for bar, value in zip(bars, values):
            percentage = (value / total_matches) * 100 if total_matches > 0 else 0
            ax4.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 1,
                    f'{value}\n({percentage:.1f}%)', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('output/algorithm_performance_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ Visualisations sauvegardées: algorithm_performance_analysis.png")
    
    def generate_performance_report(self):
        """Générer le rapport de performance"""
        print("\n📋 GÉNÉRATION RAPPORT PERFORMANCE")
        print("=" * 50)
        
        report_lines = []
        report_lines.append("ALGORITHM PERFORMANCE ANALYSIS REPORT")
        report_lines.append("BOM-PCM Matching - Voyager System")
        report_lines.append("=" * 60)
        report_lines.append("")
        
        # Métriques principales
        cm = self.performance_metrics['confusion_matrix']
        cls_metrics = self.performance_metrics['classification_metrics']
        cov_metrics = self.performance_metrics['coverage_metrics']
        qual_metrics = self.performance_metrics['quality_metrics']
        
        report_lines.append("📊 ALGORITHM PERFORMANCE SUMMARY")
        report_lines.append("-" * 40)
        report_lines.append(f"Precision: {cls_metrics['precision']:.3f}")
        report_lines.append(f"Recall: {cls_metrics['recall']:.3f}")
        report_lines.append(f"F1-Score: {cls_metrics['f1_score']:.3f}")
        report_lines.append(f"Accuracy: {cls_metrics['accuracy']:.3f}")
        report_lines.append("")
        
        report_lines.append("🎯 CONFUSION MATRIX")
        report_lines.append("-" * 20)
        report_lines.append(f"True Positives: {cm['TP']}")
        report_lines.append(f"False Positives: {cm['FP']}")
        report_lines.append(f"False Negatives: {cm['FN']}")
        report_lines.append(f"True Negatives: {cm['TN']}")
        report_lines.append("")
        
        report_lines.append("📈 COVERAGE ANALYSIS")
        report_lines.append("-" * 25)
        report_lines.append(f"Total BOM Items: {cov_metrics['total_bom_items']:,}")
        report_lines.append(f"Matched Items: {cov_metrics['matched_items']:,}")
        report_lines.append(f"Coverage Rate: {cov_metrics['coverage_rate']:.1f}%")
        report_lines.append(f"Unmatched Rate: {cov_metrics['unmatched_rate']:.1f}%")
        report_lines.append("")
        
        report_lines.append("🎯 MATCH QUALITY")
        report_lines.append("-" * 15)
        report_lines.append(f"Mean Similarity: {qual_metrics['mean_similarity']:.3f}")
        report_lines.append(f"Exact Matches (>0.9): {qual_metrics['exact_matches']}")
        report_lines.append(f"Similar Matches (0.7-0.9): {qual_metrics['similar_matches']}")
        report_lines.append(f"Weak Matches (0.6-0.7): {qual_metrics['weak_matches']}")
        report_lines.append("")
        
        # Recommandations
        report_lines.append("🚀 ALGORITHM RECOMMENDATIONS")
        report_lines.append("-" * 35)
        
        if cls_metrics['precision'] < 0.7:
            report_lines.append("• IMPROVE PRECISION: Too many false positives")
        if cls_metrics['recall'] < 0.7:
            report_lines.append("• IMPROVE RECALL: Missing true matches")
        if cov_metrics['coverage_rate'] < 50:
            report_lines.append("• INCREASE COVERAGE: Low matching rate")
        if qual_metrics['mean_similarity'] < 0.7:
            report_lines.append("• ENHANCE SIMILARITY: Low average similarity scores")
        
        # Sauvegarder le rapport
        with open('output/algorithm_performance_report.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print('\n'.join(report_lines))
        print(f"\n✅ Rapport sauvegardé: output/algorithm_performance_report.txt")

def main():
    """Fonction principale"""
    print("🔍 ANALYSE PERFORMANCE ALGORITHME BOM-PCM")
    print("=" * 70)
    print("Étapes: Préprocessing → SBERT → Similarité → Seuillage → Évaluation")
    print("")
    
    analyzer = AlgorithmPerformanceAnalyzer()
    
    # Charger les données
    if not analyzer.load_data():
        return None
    
    # Créer la vérité terrain
    ground_truth_df = analyzer.create_ground_truth()
    
    # Calculer les métriques de performance
    metrics = analyzer.calculate_performance_metrics(ground_truth_df)
    
    # Créer les visualisations
    analyzer.create_performance_visualizations(ground_truth_df)
    
    # Générer le rapport
    analyzer.generate_performance_report()
    
    print(f"\n✅ ANALYSE PERFORMANCE TERMINÉE")
    print(f"📊 Métriques calculées et visualisées")
    print(f"📋 Rapport disponible: output/algorithm_performance_report.txt")
    
    return analyzer, metrics

if __name__ == "__main__":
    analyzer, metrics = main()
