import pandas as pd

def load_data(file_path):
    """
    Load BOM data from an Excel or CSV file.

    Parameters:
    - file_path: str, path to the Excel or CSV file.

    Returns:
    - DataFrame containing the BOM data.
    """
    if file_path.endswith('.xlsx'):
        return pd.read_excel(file_path)
    elif file_path.endswith('.csv'):
        return pd.read_csv(file_path)
    else:
        raise ValueError("Unsupported file format. Please provide an Excel or CSV file.")