#!/usr/bin/env python3
"""
PIM Simple Extractor - Création d'un socle de vocabulaire technique
Sans dépendances OCR complexes - utilise les métadonnées et structure PDF
"""

import fitz  # PyMuPDF
import pandas as pd
import json
import re
from collections import defaultdict
import os

class PIMSimpleExtractor:
    def __init__(self):
        """
        Extracteur simple pour créer un socle de vocabulaire technique
        """
        # Vocabulaire technique médical prédéfini (basé sur l'expérience)
        self.medical_vocabulary = {
            'imaging_system': [
                'mri', 'magnetic', 'resonance', 'imaging', 'scan', 'scanner',
                'tesla', '1.5t', '3.0t', 'bore', 'gantry', 'magnet', 'superconducting'
            ],
            'coils_rf': [
                'coil', 'array', 'head', 'neck', 'spine', 'body', 'cardiac',
                'rf', 'transmit', 'receive', 'quadrature', 'phased', 'surface'
            ],
            'gradients_shims': [
                'gradient', 'shim', 'shimming', 'linearity', 'eddy', 'current',
                'x-gradient', 'y-gradient', 'z-gradient', 'slew', 'rate'
            ],
            'electronics': [
                'amplifier', 'receiver', 'transmitter', 'synthesizer', 'adc',
                'dac', 'processor', 'controller', 'interface', 'board', 'module'
            ],
            'patient_table': [
                'table', 'patient', 'positioning', 'cradle', 'headrest',
                'immobilization', 'comfort', 'pad', 'strap', 'support'
            ],
            'software_protocols': [
                'software', 'protocol', 'sequence', 'pulse', 'acquisition',
                'reconstruction', 'processing', 'analysis', 'workflow'
            ],
            'maintenance_service': [
                'maintenance', 'service', 'calibration', 'adjustment', 'repair',
                'replacement', 'installation', 'removal', 'inspection', 'test'
            ],
            'safety_compliance': [
                'safety', 'warning', 'caution', 'danger', 'hazard', 'protection',
                'compliance', 'regulation', 'standard', 'certification'
            ],
            'accessories': [
                'accessory', 'option', 'upgrade', 'kit', 'package', 'bundle',
                'phantom', 'test', 'quality', 'assurance', 'qa', 'qc'
            ]
        }
        
        # Patterns de codes techniques
        self.code_patterns = [
            r'\b[A-Z]\d{4,6}[A-Z]{0,2}\b',  # M7010FC, R4390JA
            r'\b\d{6,10}\b',  # Codes numériques longs
            r'\b[A-Z]{2,4}-\d{3,6}\b',  # ABC-1234
            r'\bP/N\s*[:\-]?\s*([A-Z0-9\-]+)\b',  # Part numbers
            r'\bS/N\s*[:\-]?\s*([A-Z0-9\-]+)\b',  # Serial numbers
            r'\b\d{4}-\d{4}-\d{4}\b',  # 1234-5678-9012
        ]
        
        # Vocabulaire technique accumulé
        self.vocabulary_base = {}
    
    def analyze_pdf_structure(self, pdf_path):
        """
        Analyse la structure du PDF pour extraire des informations
        """
        print("🔍 ANALYSE STRUCTURE PDF")
        print("=" * 40)
        
        doc = fitz.open(pdf_path)
        
        structure_info = {
            'total_pages': len(doc),
            'page_sizes': [],
            'has_text_layers': 0,
            'has_images': 0,
            'metadata': doc.metadata,
            'outline': []
        }
        
        # Analyser chaque page
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            
            # Taille de page
            structure_info['page_sizes'].append({
                'page': page_num + 1,
                'width': page.rect.width,
                'height': page.rect.height
            })
            
            # Vérifier présence de texte
            text = page.get_text()
            if text.strip():
                structure_info['has_text_layers'] += 1
            
            # Vérifier présence d'images
            images = page.get_images()
            if images:
                structure_info['has_images'] += 1
        
        # Extraire la table des matières si disponible
        try:
            toc = doc.get_toc()
            structure_info['outline'] = toc
        except:
            structure_info['outline'] = []
        
        doc.close()
        
        print(f"📊 Pages totales: {structure_info['total_pages']}")
        print(f"📝 Pages avec texte: {structure_info['has_text_layers']}")
        print(f"🖼️ Pages avec images: {structure_info['has_images']}")
        print(f"📋 Sections TOC: {len(structure_info['outline'])}")
        
        return structure_info
    
    def extract_available_text(self, pdf_path):
        """
        Extrait tout le texte disponible du PDF
        """
        print("\n📝 EXTRACTION TEXTE DISPONIBLE")
        print("=" * 40)
        
        doc = fitz.open(pdf_path)
        all_text = ""
        page_texts = []
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            
            page_info = {
                'page_number': page_num + 1,
                'text': text,
                'word_count': len(text.split()) if text else 0,
                'char_count': len(text) if text else 0
            }
            
            page_texts.append(page_info)
            all_text += text + "\n"
            
            if page_info['word_count'] > 0:
                print(f"   Page {page_num + 1}: {page_info['word_count']} mots")
        
        doc.close()
        
        total_words = len(all_text.split())
        print(f"\n✅ Total mots extraits: {total_words:,}")
        
        return all_text, page_texts
    
    def create_technical_vocabulary_base(self, pdf_path):
        """
        Crée un socle de vocabulaire technique basé sur les connaissances prédéfinies
        """
        print("\n🧠 CRÉATION SOCLE VOCABULAIRE TECHNIQUE")
        print("=" * 50)
        
        # Analyser la structure
        structure_info = self.analyze_pdf_structure(pdf_path)
        
        # Extraire le texte disponible
        all_text, page_texts = self.extract_available_text(pdf_path)
        
        # Créer le socle basé sur le vocabulaire prédéfini
        vocabulary_base = self.build_vocabulary_base(all_text, structure_info, page_texts, pdf_path)
        
        # Enrichir avec les patterns trouvés
        vocabulary_base = self.enrich_with_patterns(vocabulary_base, all_text)
        
        self.vocabulary_base = vocabulary_base
        return vocabulary_base
    
    def build_vocabulary_base(self, text, structure_info, page_texts, pdf_path):
        """
        Construit le socle de vocabulaire
        """
        text_lower = text.lower()

        # Analyser la présence des termes prédéfinis
        found_terms = {}
        for category, terms in self.medical_vocabulary.items():
            found_in_category = {}
            for term in terms:
                count = text_lower.count(term.lower())
                if count > 0:
                    found_in_category[term] = count

            if found_in_category:
                found_terms[category] = found_in_category

        # Créer le socle
        vocabulary_base = {
            'document_info': {
                'pdf_path': pdf_path,
                'total_pages': structure_info['total_pages'],
                'pages_with_text': structure_info['has_text_layers'],
                'pages_with_images': structure_info['has_images'],
                'total_words': len(text.split()),
                'unique_words': len(set(text.lower().split())),
                'metadata': structure_info['metadata']
            },
            'predefined_vocabulary': self.medical_vocabulary,
            'found_terms': found_terms,
            'page_analysis': page_texts,
            'structure_info': structure_info,
            'extraction_method': 'predefined_vocabulary_matching'
        }
        
        return vocabulary_base
    
    def enrich_with_patterns(self, vocabulary_base, text):
        """
        Enrichit le socle avec les patterns trouvés
        """
        # Rechercher les codes techniques
        found_codes = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            for pattern in self.code_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    found_codes.append({
                        'code': match.group(0),
                        'line_number': line_num + 1,
                        'line_text': line.strip(),
                        'pattern': pattern
                    })
        
        vocabulary_base['extracted_codes'] = found_codes
        
        # Analyser les mots fréquents
        words = text.lower().split()
        word_freq = defaultdict(int)
        
        for word in words:
            clean_word = re.sub(r'[^\w]', '', word)
            if len(clean_word) > 3:  # Mots de plus de 3 caractères
                word_freq[clean_word] += 1
        
        # Top mots fréquents
        top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:100]
        vocabulary_base['frequent_words'] = top_words
        
        return vocabulary_base
    
    def generate_search_queries(self):
        """
        Génère des requêtes de recherche basées sur le vocabulaire
        """
        search_queries = []
        
        for category, terms in self.medical_vocabulary.items():
            # Requêtes par catégorie
            search_queries.append({
                'category': category,
                'query': ' OR '.join(terms[:5]),  # Top 5 termes
                'terms': terms
            })
        
        # Requêtes combinées
        combined_queries = [
            'coil AND head',
            'gradient AND shim',
            'patient AND table',
            'rf AND transmit',
            'maintenance AND service',
            'safety AND warning'
        ]
        
        for query in combined_queries:
            search_queries.append({
                'category': 'combined',
                'query': query,
                'terms': query.split(' AND ')
            })
        
        return search_queries
    
    def save_vocabulary_base(self, output_folder='output'):
        """
        Sauvegarde le socle de vocabulaire
        """
        os.makedirs(output_folder, exist_ok=True)
        
        # JSON complet
        json_file = f'{output_folder}/pim_vocabulary_base_simple.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.vocabulary_base, f, indent=2, ensure_ascii=False, default=str)
        
        # CSV des termes trouvés
        terms_data = []
        for category, terms in self.vocabulary_base['found_terms'].items():
            for term, count in terms.items():
                terms_data.append({
                    'category': category,
                    'term': term,
                    'count': count
                })
        
        if terms_data:
            terms_df = pd.DataFrame(terms_data)
            terms_df.to_csv(f'{output_folder}/pim_found_terms.csv', index=False)
        
        # CSV des codes extraits
        if self.vocabulary_base['extracted_codes']:
            codes_df = pd.DataFrame(self.vocabulary_base['extracted_codes'])
            codes_df.to_csv(f'{output_folder}/pim_extracted_codes_simple.csv', index=False)
        
        # Vocabulaire prédéfini en CSV
        vocab_data = []
        for category, terms in self.vocabulary_base['predefined_vocabulary'].items():
            for term in terms:
                vocab_data.append({
                    'category': category,
                    'term': term,
                    'predefined': True
                })
        
        vocab_df = pd.DataFrame(vocab_data)
        vocab_df.to_csv(f'{output_folder}/pim_predefined_vocabulary.csv', index=False)
        
        # Requêtes de recherche
        search_queries = self.generate_search_queries()
        queries_df = pd.DataFrame(search_queries)
        queries_df.to_csv(f'{output_folder}/pim_search_queries.csv', index=False)
        
        # Rapport de synthèse
        self.generate_summary_report(output_folder)
        
        print(f"\n✅ SOCLE DE VOCABULAIRE SAUVEGARDÉ:")
        print(f"   📄 JSON: {json_file}")
        print(f"   📊 Termes trouvés: {output_folder}/pim_found_terms.csv")
        print(f"   🏷️ Codes: {output_folder}/pim_extracted_codes_simple.csv")
        print(f"   📚 Vocabulaire: {output_folder}/pim_predefined_vocabulary.csv")
        print(f"   🔍 Requêtes: {output_folder}/pim_search_queries.csv")
        print(f"   📋 Rapport: {output_folder}/pim_simple_report.txt")
    
    def generate_summary_report(self, output_folder):
        """
        Génère un rapport de synthèse
        """
        report_file = f'{output_folder}/pim_simple_report.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("SOCLE VOCABULAIRE TECHNIQUE PIM - RAPPORT SIMPLE\n")
            f.write("=" * 60 + "\n\n")
            
            # Informations document
            doc_info = self.vocabulary_base['document_info']
            f.write("INFORMATIONS DOCUMENT\n")
            f.write("-" * 25 + "\n")
            f.write(f"Fichier: {doc_info['pdf_path']}\n")
            f.write(f"Pages totales: {doc_info['total_pages']}\n")
            f.write(f"Pages avec texte: {doc_info['pages_with_text']}\n")
            f.write(f"Pages avec images: {doc_info['pages_with_images']}\n")
            f.write(f"Mots totaux: {doc_info['total_words']:,}\n")
            f.write(f"Mots uniques: {doc_info['unique_words']:,}\n\n")
            
            # Vocabulaire prédéfini
            f.write("VOCABULAIRE TECHNIQUE PRÉDÉFINI\n")
            f.write("-" * 35 + "\n")
            for category, terms in self.vocabulary_base['predefined_vocabulary'].items():
                f.write(f"{category}: {len(terms)} termes\n")
            f.write("\n")
            
            # Termes trouvés
            f.write("TERMES TROUVÉS DANS LE DOCUMENT\n")
            f.write("-" * 35 + "\n")
            total_found = 0
            for category, terms in self.vocabulary_base['found_terms'].items():
                category_total = sum(terms.values())
                total_found += category_total
                f.write(f"{category}: {len(terms)} termes, {category_total} occurrences\n")
            
            f.write(f"\nTotal occurrences: {total_found}\n\n")
            
            # Codes extraits
            f.write("CODES TECHNIQUES EXTRAITS\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total codes: {len(self.vocabulary_base['extracted_codes'])}\n")
            
            if self.vocabulary_base['extracted_codes']:
                f.write("Exemples:\n")
                for i, code in enumerate(self.vocabulary_base['extracted_codes'][:10]):
                    f.write(f"  {i+1}. {code['code']}\n")
            
            f.write("\n")
            
            # Recommandations
            f.write("RECOMMANDATIONS POUR UTILISATION\n")
            f.write("-" * 40 + "\n")
            f.write("1. Utilisez les requêtes prédéfinies pour rechercher dans d'autres documents\n")
            f.write("2. Enrichissez le vocabulaire avec les termes spécifiques trouvés\n")
            f.write("3. Adaptez les patterns de codes selon vos besoins\n")
            f.write("4. Combinez plusieurs termes pour des recherches plus précises\n")

def main():
    """
    Fonction principale - Création socle vocabulaire simple
    """
    print("🧠 PIM SIMPLE EXTRACTOR - SOCLE VOCABULAIRE TECHNIQUE")
    print("=" * 70)
    print("Approche: Vocabulaire prédéfini + Analyse structure")
    print("")
    
    # Initialiser l'extracteur
    extractor = PIMSimpleExtractor()
    
    # Chemin vers le PDF
    pdf_path = r"input\PIM\3.0T Signa PET MR.pdf"
    
    try:
        # Vérifier que le fichier existe
        if not os.path.exists(pdf_path):
            print(f"❌ Fichier non trouvé: {pdf_path}")
            print("📁 Fichiers PIM disponibles:")
            pim_folder = r"input\PIM"
            if os.path.exists(pim_folder):
                for file in os.listdir(pim_folder):
                    if file.endswith('.pdf'):
                        print(f"   - {file}")
            return
        
        # Création du socle
        vocabulary_base = extractor.create_technical_vocabulary_base(pdf_path)
        
        # Affichage des résultats
        print(f"\n📊 SOCLE DE VOCABULAIRE CRÉÉ:")
        doc_info = vocabulary_base['document_info']
        print(f"   📄 Pages analysées: {doc_info['total_pages']}")
        print(f"   📝 Pages avec texte: {doc_info['pages_with_text']}")
        print(f"   🔤 Mots extraits: {doc_info['total_words']:,}")
        
        # Termes trouvés par catégorie
        print(f"\n🔍 TERMES TROUVÉS PAR CATÉGORIE:")
        total_found = 0
        for category, terms in vocabulary_base['found_terms'].items():
            category_total = sum(terms.values())
            total_found += category_total
            print(f"   {category}: {len(terms)} termes, {category_total} occurrences")
        
        print(f"\n📊 Total occurrences: {total_found}")
        print(f"🏷️ Codes extraits: {len(vocabulary_base['extracted_codes'])}")
        
        # Sauvegarder le socle
        extractor.save_vocabulary_base()
        
        print(f"\n🚀 SOCLE DE VOCABULAIRE TECHNIQUE CRÉÉ!")
        print(f"📚 Vocabulaire prédéfini: {sum(len(terms) for terms in extractor.medical_vocabulary.values())} termes")
        print(f"🔍 Prêt pour recherches dans d'autres documents PIM")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
