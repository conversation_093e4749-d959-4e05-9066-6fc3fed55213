import cv2
import numpy as np
import networkx as nx

# OCR import with fallback
try:
    import pytesseract
    # Set Tesseract path for Windows
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("Warning: pytesseract not available. Text masking disabled.")

def mask_text(img):
    """
    Finds text via Tesseract, paints over it in white.
    """
    if not OCR_AVAILABLE:
        return img
    
    try:
        from pytesseract import image_to_data
        data = image_to_data(img, output_type='dict')
        masked_img = img.copy()
        
        for i, text in enumerate(data['text']):
            if text.strip():
                x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                cv2.rectangle(masked_img, (x, y), (x+w, y+h), (255,255,255), -1)
        
        return masked_img
    except Exception as e:
        print(f"Text masking error: {e}")
        return img

def compute_centroids(detections):
    """
    For each 'box' or 'satellite', compute its center point.
    """
    items = []
    for det in detections:
        cx = (det['xmin'] + det['xmax']) / 2
        cy = (det['ymin'] + det['ymax']) / 2
        items.append({
            'label': det['label'], 
            'centroid': (cx, cy),
            'bbox': (det['xmin'], det['ymin'], det['xmax'], det['ymax']),
            'confidence': det.get('confidence', 0.0)
        })
    return items

def build_hierarchy(items, threshold=100):
    """
    Simple nearest-neighbor: if 'satellite' within threshold of 'box',
    assign as child; else top-level.
    """
    G = nx.DiGraph()
    boxes = [i for i in items if i['label']=='box']
    sats = [i for i in items if i['label']=='satellite']
    
    # Add all boxes as nodes
    for i, b in enumerate(boxes):
        node_id = f"box_{i}"
        G.add_node(node_id, 
                  label='box', 
                  centroid=b['centroid'],
                  bbox=b['bbox'],
                  confidence=b['confidence'])
    
    # Add satellites and connect to nearest boxes
    for i, s in enumerate(sats):
        node_id = f"satellite_{i}"
        G.add_node(node_id, 
                  label='satellite', 
                  centroid=s['centroid'],
                  bbox=s['bbox'],
                  confidence=s['confidence'])
        
        # Find nearest box
        if boxes:
            dists = [np.linalg.norm(np.array(s['centroid'])-np.array(b['centroid'])) for b in boxes]
            idx = np.argmin(dists)
            if dists[idx] < threshold:
                parent_id = f"box_{idx}"
                G.add_edge(parent_id, node_id, distance=dists[idx])
    
    return G

def analyze_spatial_relationships(items):
    """
    Analyze spatial relationships between detected items
    """
    relationships = []
    
    for i, item1 in enumerate(items):
        for j, item2 in enumerate(items):
            if i >= j:
                continue
            
            # Calculate distance
            dist = np.linalg.norm(np.array(item1['centroid']) - np.array(item2['centroid']))
            
            # Check for containment
            bbox1 = item1['bbox']
            bbox2 = item2['bbox']
            
            contained = is_contained(bbox1, bbox2) or is_contained(bbox2, bbox1)
            overlapping = boxes_overlap(bbox1, bbox2)
            
            relationships.append({
                'item1_idx': i,
                'item2_idx': j,
                'distance': dist,
                'contained': contained,
                'overlapping': overlapping,
                'spatial_relation': classify_spatial_relation(dist, contained, overlapping)
            })
    
    return relationships

def is_contained(bbox1, bbox2):
    """
    Check if bbox1 is contained within bbox2
    """
    x1_min, y1_min, x1_max, y1_max = bbox1
    x2_min, y2_min, x2_max, y2_max = bbox2
    
    return (x2_min <= x1_min and y2_min <= y1_min and 
            x1_max <= x2_max and y1_max <= y2_max)

def boxes_overlap(bbox1, bbox2):
    """
    Check if two bounding boxes overlap
    """
    x1_min, y1_min, x1_max, y1_max = bbox1
    x2_min, y2_min, x2_max, y2_max = bbox2
    
    return not (x1_max < x2_min or x2_max < x1_min or 
                y1_max < y2_min or y2_max < y1_min)

def classify_spatial_relation(distance, contained, overlapping):
    """
    Classify spatial relationship between items
    """
    if contained:
        return 'contained'
    elif overlapping:
        return 'overlapping'
    elif distance < 50:
        return 'adjacent'
    elif distance < 150:
        return 'nearby'
    else:
        return 'distant'
