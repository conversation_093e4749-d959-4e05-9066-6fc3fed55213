#!/usr/bin/env python3
"""
Full Dataset Similarity Analysis and Two-Stage Clustering
Stage 1: Similarity-based clustering on all 244 components
Stage 2: Compatibility filtering using PCM data
"""

import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.cluster import DBSCAN, KMeans, AgglomerativeClustering
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
import matplotlib.pyplot as plt
import seaborn as sns
import re
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class FullDatasetSimilarityAnalyzer:
    def __init__(self, colpali_path='output/comprehensive_colpali_extraction.csv'):
        """Initialize with full ColPali dataset"""
        self.df = pd.read_csv(colpali_path)
        self.sbert_model = None
        self.similarity_results = {}
        self.stage1_clusters = {}
        self.stage2_clusters = {}
        self.pcm_data = {}
        
        print(f"📊 Loaded {len(self.df)} components from {self.df['page_number'].nunique()} pages")
        
    def load_sbert_model(self):
        """Load SBERT model for semantic similarity"""
        if self.sbert_model is None:
            print("🔄 Loading SBERT model...")
            self.sbert_model = SentenceTransformer('all-MiniLM-L6-v2')
            print("✅ SBERT model loaded successfully")
    
    def stage1_comprehensive_similarity_analysis(self):
        """Stage 1: Comprehensive similarity analysis on full dataset"""
        print("\n🎯 STAGE 1: COMPREHENSIVE SIMILARITY ANALYSIS")
        print("=" * 60)
        print(f"📊 Analyzing {len(self.df)} components from pages {sorted(self.df['page_number'].unique())}")
        
        # 1. Exact Match Analysis
        exact_matches = self._exact_match_analysis()
        
        # 2. Terminology Analysis
        terminology_results = self._terminology_analysis()
        
        # 3. SBERT Semantic Similarity
        semantic_results = self._sbert_semantic_similarity()
        
        # 4. Spatial Clustering by Page
        spatial_results = self._spatial_clustering_analysis()
        
        # 5. Color-based Grouping
        color_results = self._color_based_grouping()
        
        # 6. Multi-dimensional Feature Engineering
        feature_matrix = self._create_feature_matrix()
        
        # 7. Multiple Clustering Approaches
        clustering_results = self._apply_multiple_clustering_methods(feature_matrix)
        
        # Store Stage 1 results
        self.stage1_clusters = {
            'exact_matches': exact_matches,
            'terminology': terminology_results,
            'semantic': semantic_results,
            'spatial': spatial_results,
            'color': color_results,
            'feature_matrix': feature_matrix,
            'clustering': clustering_results
        }
        
        print(f"\n✅ Stage 1 Complete: {len(clustering_results)} clustering methods applied")
        return self.stage1_clusters
    
    def _exact_match_analysis(self):
        """Exact description and ID matching"""
        print("🔍 Exact Match Analysis...")
        
        exact_matches = []
        
        # Group by description
        desc_groups = self.df.groupby('component_text')
        for desc, group in desc_groups:
            if len(group) > 1:
                component_ids = group['product_id'].tolist()
                for i in range(len(component_ids)):
                    for j in range(i+1, len(component_ids)):
                        exact_matches.append({
                            'component1': component_ids[i],
                            'component2': component_ids[j],
                            'match_type': 'exact_description',
                            'shared_description': desc,
                            'similarity_score': 1.0
                        })
        
        print(f"   Found {len(exact_matches)} exact description matches")
        return exact_matches
    
    def _terminology_analysis(self):
        """Analyze terminology patterns and component families"""
        print("🔍 Terminology Analysis...")
        
        # Component categories
        categories = defaultdict(list)
        category_keywords = {
            'System': ['System'],
            'Interface': ['Interface'],
            'Controller': ['Controller', 'Control'],
            'Board': ['Board'],
            'Module': ['Module'],
            'Processor': ['Processor', 'Processing'],
            'Communication': ['Communication'],
            'Data': ['Data'],
            'Sensor': ['Sensor'],
            'Memory': ['Memory'],
            'Signal': ['Signal'],
            'Network': ['Network'],
            'Power': ['Power'],
            'Display': ['Display'],
            'Storage': ['Storage']
        }
        
        for idx, row in self.df.iterrows():
            text = row['component_text']
            for category, keywords in category_keywords.items():
                if any(keyword in text for keyword in keywords):
                    categories[category].append(row.to_dict())
        
        # ID families
        id_families = defaultdict(list)
        for idx, row in self.df.iterrows():
            prod_id = row['product_id']
            prefix_match = re.match(r'^([A-Z]+)', prod_id)
            if prefix_match:
                prefix = prefix_match.group(1)
                id_families[prefix].append(row.to_dict())
        
        print(f"   Found {len(categories)} component categories")
        print(f"   Found {len(id_families)} ID families")
        
        return {'categories': dict(categories), 'id_families': dict(id_families)}
    
    def _sbert_semantic_similarity(self, threshold=0.7):
        """SBERT semantic similarity analysis"""
        print("🧠 SBERT Semantic Similarity Analysis...")
        
        self.load_sbert_model()
        
        descriptions = self.df['component_text'].tolist()
        embeddings = self.sbert_model.encode(descriptions)
        similarity_matrix = cosine_similarity(embeddings)
        
        # Find high similarity pairs
        semantic_matches = []
        n_components = len(self.df)
        
        for i in range(n_components):
            for j in range(i+1, n_components):
                similarity = similarity_matrix[i][j]
                if similarity >= threshold:
                    row1 = self.df.iloc[i]
                    row2 = self.df.iloc[j]
                    semantic_matches.append({
                        'component1': row1['product_id'],
                        'component2': row2['product_id'],
                        'component1_text': row1['component_text'],
                        'component2_text': row2['component_text'],
                        'semantic_similarity': similarity,
                        'match_type': 'semantic_similarity'
                    })
        
        print(f"   Found {len(semantic_matches)} semantic matches (≥{threshold})")
        
        return {
            'matches': semantic_matches,
            'similarity_matrix': similarity_matrix,
            'embeddings': embeddings
        }
    
    def _spatial_clustering_analysis(self, distance_threshold=200):
        """Spatial clustering analysis by page"""
        print("📍 Spatial Clustering Analysis...")
        
        spatial_clusters = {}
        
        for page_num in self.df['page_number'].unique():
            page_df = self.df[self.df['page_number'] == page_num].copy()
            
            if len(page_df) < 2:
                continue
                
            coordinates = page_df[['center_x', 'center_y']].values
            clustering = DBSCAN(eps=distance_threshold, min_samples=2).fit(coordinates)
            page_df['spatial_cluster'] = clustering.labels_
            
            clusters = {}
            for cluster_id in set(clustering.labels_):
                if cluster_id != -1:
                    cluster_components = page_df[page_df['spatial_cluster'] == cluster_id]
                    clusters[cluster_id] = cluster_components.to_dict('records')
            
            spatial_clusters[page_num] = clusters
        
        total_clusters = sum(len(clusters) for clusters in spatial_clusters.values())
        print(f"   Found {total_clusters} spatial clusters across {len(spatial_clusters)} pages")
        
        return spatial_clusters
    
    def _color_based_grouping(self):
        """Color-based grouping and dependency analysis"""
        print("🎨 Color-based Grouping Analysis...")
        
        color_groups = self.df.groupby('color_detected')
        color_distribution = {color: len(group) for color, group in color_groups}
        
        # Analyze color chains by page
        color_chains = []
        for page_num in self.df['page_number'].unique():
            page_df = self.df[self.df['page_number'] == page_num].copy()
            page_df = page_df.sort_values('center_y')
            
            # Look for dependency patterns
            for i in range(len(page_df)-2):
                comp1 = page_df.iloc[i]
                comp2 = page_df.iloc[i+1]
                comp3 = page_df.iloc[i+2]
                
                colors = [comp1['color_detected'], comp2['color_detected'], comp3['color_detected']]
                if colors == ['Blue', 'Purple', 'Red']:
                    color_chains.append({
                        'page': page_num,
                        'chain': [comp1['product_id'], comp2['product_id'], comp3['product_id']],
                        'pattern': 'Blue→Purple→Red'
                    })
        
        print(f"   Color distribution: {color_distribution}")
        print(f"   Found {len(color_chains)} dependency chains")
        
        return {
            'distribution': color_distribution,
            'groups': {color: group.to_dict('records') for color, group in color_groups},
            'chains': color_chains
        }
    
    def _create_feature_matrix(self):
        """Create multi-dimensional feature matrix for clustering"""
        print("🔧 Creating Multi-dimensional Feature Matrix...")
        
        features = []
        
        for idx, row in self.df.iterrows():
            feature_vector = []
            
            # 1. Semantic features (from SBERT embeddings)
            if 'semantic' in self.stage1_clusters and 'embeddings' in self.stage1_clusters['semantic']:
                semantic_features = self.stage1_clusters['semantic']['embeddings'][idx]
                feature_vector.extend(semantic_features[:50])  # Use first 50 dimensions
            
            # 2. Spatial features (normalized)
            feature_vector.extend([
                row['center_x'] / 1000,  # Normalize coordinates
                row['center_y'] / 1000,
                row['width'] / 1000,
                row['height'] / 1000,
                row['area'] / 1000000
            ])
            
            # 3. Color features (one-hot encoded)
            color_features = [0, 0, 0]  # Blue, Purple, Red
            if row['color_detected'] == 'Blue':
                color_features[0] = 1
            elif row['color_detected'] == 'Purple':
                color_features[1] = 1
            elif row['color_detected'] == 'Red':
                color_features[2] = 1
            feature_vector.extend(color_features)
            
            # 4. Component type features
            type_features = [0, 0, 0]  # Based on component_type
            if row['component_type'] == 'mandatory':
                type_features[0] = 1
            elif row['component_type'] == 'mandatory_selectable':
                type_features[1] = 1
            elif row['component_type'] == 'optional':
                type_features[2] = 1
            feature_vector.extend(type_features)
            
            # 5. Page features
            feature_vector.append(row['page_number'] / 20)  # Normalize page number
            
            features.append(feature_vector)
        
        feature_matrix = np.array(features)
        
        # Standardize features
        scaler = StandardScaler()
        feature_matrix_scaled = scaler.fit_transform(feature_matrix)
        
        print(f"   Created feature matrix: {feature_matrix_scaled.shape}")
        
        return {
            'raw': feature_matrix,
            'scaled': feature_matrix_scaled,
            'scaler': scaler
        }
    
    def _apply_multiple_clustering_methods(self, feature_matrix):
        """Apply multiple clustering methods"""
        print("🔬 Applying Multiple Clustering Methods...")
        
        X = feature_matrix['scaled']
        clustering_results = {}
        
        # 1. K-Means clustering (multiple k values)
        for k in [5, 10, 15, 20]:
            kmeans = KMeans(n_clusters=k, random_state=42)
            labels = kmeans.fit_predict(X)
            clustering_results[f'kmeans_k{k}'] = {
                'labels': labels,
                'method': 'kmeans',
                'n_clusters': k,
                'inertia': kmeans.inertia_
            }
        
        # 2. DBSCAN clustering (multiple eps values)
        for eps in [0.5, 1.0, 1.5, 2.0]:
            dbscan = DBSCAN(eps=eps, min_samples=3)
            labels = dbscan.fit_predict(X)
            n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
            clustering_results[f'dbscan_eps{eps}'] = {
                'labels': labels,
                'method': 'dbscan',
                'eps': eps,
                'n_clusters': n_clusters
            }
        
        # 3. Agglomerative clustering (multiple n_clusters)
        for n in [5, 10, 15, 20]:
            agg = AgglomerativeClustering(n_clusters=n)
            labels = agg.fit_predict(X)
            clustering_results[f'agglomerative_n{n}'] = {
                'labels': labels,
                'method': 'agglomerative',
                'n_clusters': n
            }
        
        print(f"   Applied {len(clustering_results)} clustering configurations")
        
        return clustering_results
    
    def load_pcm_data(self, pcm_files):
        """Load PCM data for compatibility analysis"""
        print("\n📋 LOADING PCM DATA FOR COMPATIBILITY ANALYSIS")
        print("=" * 55)
        
        for system_name, file_path in pcm_files.items():
            try:
                pcm_df = pd.read_excel(file_path)
                self.pcm_data[system_name] = pcm_df
                print(f"✅ Loaded {system_name}: {len(pcm_df)} items")
            except Exception as e:
                print(f"⚠️ Could not load {system_name}: {e}")
        
        print(f"📊 Total PCM systems loaded: {len(self.pcm_data)}")
    
    def stage2_compatibility_filtering(self):
        """Stage 2: Filter clusters by compatibility using PCM data"""
        print("\n🎯 STAGE 2: COMPATIBILITY FILTERING")
        print("=" * 45)
        
        if not self.pcm_data:
            print("⚠️ No PCM data loaded. Skipping compatibility filtering.")
            return {}
        
        compatible_clusters = {}
        
        for system_name, pcm_df in self.pcm_data.items():
            print(f"\n🔍 Processing {system_name}...")
            
            # Extract compatible component IDs from PCM
            compatible_ids = set()
            if 'product_id' in pcm_df.columns:
                compatible_ids = set(pcm_df['product_id'].dropna())
            elif 'item_id' in pcm_df.columns:
                compatible_ids = set(pcm_df['item_id'].dropna())
            
            system_compatible_clusters = {}
            
            # Filter each clustering result
            for cluster_name, cluster_result in self.stage1_clusters['clustering'].items():
                labels = cluster_result['labels']
                filtered_clusters = {}
                
                for cluster_id in set(labels):
                    if cluster_id == -1:  # Skip noise points
                        continue
                    
                    # Get components in this cluster
                    cluster_indices = np.where(labels == cluster_id)[0]
                    cluster_components = self.df.iloc[cluster_indices]
                    
                    # Filter for compatible components
                    compatible_components = cluster_components[
                        cluster_components['product_id'].isin(compatible_ids)
                    ]
                    
                    if len(compatible_components) > 0:
                        filtered_clusters[cluster_id] = {
                            'components': compatible_components.to_dict('records'),
                            'count': len(compatible_components),
                            'compatibility_ratio': len(compatible_components) / len(cluster_components)
                        }
                
                system_compatible_clusters[cluster_name] = filtered_clusters
            
            compatible_clusters[system_name] = system_compatible_clusters
            
            total_compatible = sum(
                sum(len(cluster['components']) for cluster in clusters.values())
                for clusters in system_compatible_clusters.values()
            )
            print(f"   Found {total_compatible} compatible components across all clustering methods")
        
        self.stage2_clusters = compatible_clusters
        return compatible_clusters
    
    def export_results_to_excel(self, output_path='output/full_similarity_clustering_results.xlsx'):
        """Export all results to Excel with multiple sheets"""
        print(f"\n💾 EXPORTING RESULTS TO EXCEL: {output_path}")
        print("=" * 50)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            
            # Sheet 1: Full Dataset Overview
            overview_data = []
            for idx, row in self.df.iterrows():
                overview_data.append({
                    'product_id': row['product_id'],
                    'component_text': row['component_text'],
                    'page_number': row['page_number'],
                    'color_detected': row['color_detected'],
                    'component_type': row['component_type'],
                    'center_x': row['center_x'],
                    'center_y': row['center_y']
                })
            
            overview_df = pd.DataFrame(overview_data)
            overview_df.to_excel(writer, sheet_name='Full_Dataset_Overview', index=False)
            print("✅ Full Dataset Overview sheet created")
            
            # Sheet 2: Stage 1 Clustering Results
            if self.stage1_clusters and 'clustering' in self.stage1_clusters:
                for cluster_name, cluster_result in self.stage1_clusters['clustering'].items():
                    cluster_data = []
                    labels = cluster_result['labels']
                    
                    for idx, label in enumerate(labels):
                        row = self.df.iloc[idx]
                        cluster_data.append({
                            'product_id': row['product_id'],
                            'component_text': row['component_text'],
                            'cluster_id': label,
                            'page_number': row['page_number'],
                            'color_detected': row['color_detected']
                        })
                    
                    cluster_df = pd.DataFrame(cluster_data)
                    sheet_name = f"Stage1_{cluster_name}"[:31]  # Excel sheet name limit
                    cluster_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                print(f"✅ {len(self.stage1_clusters['clustering'])} Stage 1 clustering sheets created")
            
            # Sheet 3+: Stage 2 Compatible Clusters (one sheet per system)
            if self.stage2_clusters:
                for system_name, system_clusters in self.stage2_clusters.items():
                    system_data = []
                    
                    for cluster_method, clusters in system_clusters.items():
                        for cluster_id, cluster_info in clusters.items():
                            for component in cluster_info['components']:
                                system_data.append({
                                    'product_id': component['product_id'],
                                    'component_text': component['component_text'],
                                    'clustering_method': cluster_method,
                                    'cluster_id': cluster_id,
                                    'page_number': component['page_number'],
                                    'color_detected': component['color_detected'],
                                    'compatibility_ratio': cluster_info['compatibility_ratio']
                                })
                    
                    if system_data:
                        system_df = pd.DataFrame(system_data)
                        sheet_name = f"Compatible_{system_name}"[:31]
                        system_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                print(f"✅ {len(self.stage2_clusters)} Compatible system sheets created")
        
        print(f"📊 Excel file saved: {output_path}")

def main():
    """Main execution function"""
    print("🚀 FULL DATASET SIMILARITY ANALYSIS AND CLUSTERING")
    print("=" * 65)
    
    # Initialize analyzer
    analyzer = FullDatasetSimilarityAnalyzer()
    
    # Stage 1: Comprehensive similarity analysis
    stage1_results = analyzer.stage1_comprehensive_similarity_analysis()
    
    # Note: PCM data will be loaded when provided
    print("\n⏳ WAITING FOR PCM DATA...")
    print("Ready to load PCM files for Stage 2 compatibility filtering")
    print("Use: analyzer.load_pcm_data({'system1': 'path1.xlsx', 'system2': 'path2.xlsx'})")
    print("Then: analyzer.stage2_compatibility_filtering()")
    print("Finally: analyzer.export_results_to_excel()")
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
