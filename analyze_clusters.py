import pandas as pd
import re

def analyze_material_clusters():
    """Analyze material clusters to understand what each cluster represents"""
    
    # Load the material clusters data
    df = pd.read_excel('output/voyager_bom_quick_clustering_results.xlsx', sheet_name='Material_Clusters')
    
    print("MATERIAL CLUSTER ANALYSIS")
    print("=" * 50)
    print(f"Total items analyzed: {len(df)}")
    print()
    
    # Analyze each cluster
    for cluster_id in sorted(df['material_cluster'].unique()):
        cluster_items = df[df['material_cluster'] == cluster_id]
        print(f"CLUSTER {cluster_id} ({len(cluster_items)} items - {len(cluster_items)/len(df)*100:.1f}%)")
        print("-" * 30)
        
        # Sample items from this cluster
        print("Sample items:")
        sample_items = cluster_items[['Item Number', 'Item Description']].head(5)
        for idx, row in sample_items.iterrows():
            print(f"  • {row['Item Number']}: {row['Item Description'][:80]}...")
        
        # Analyze common materials mentioned
        descriptions = ' '.join(cluster_items['Item Description'].astype(str).str.lower())
        
        # Look for material keywords
        materials = re.findall(r'\b(?:steel|aluminum|plastic|rubber|ceramic|glass|carbon|titanium|brass|copper|bronze|iron|stainless|galvanized|anodized|coated|treated|hardened|tempered|reinforced)\b', descriptions)
        
        if materials:
            material_counts = {}
            for material in materials:
                material_counts[material] = material_counts.get(material, 0) + 1
            
            print("Common materials/properties:")
            for material, count in sorted(material_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"  - {material}: {count} mentions")
        
        # Look for component types
        component_types = re.findall(r'\b(?:resistor|capacitor|connector|cable|shield|filter|sensor|motor|valve|pump|bracket|plate|housing|assembly|module|unit|system)\b', descriptions)
        
        if component_types:
            type_counts = {}
            for comp_type in component_types:
                type_counts[comp_type] = type_counts.get(comp_type, 0) + 1
            
            print("Common component types:")
            for comp_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"  - {comp_type}: {count} mentions")
        
        print()

def create_cluster_names():
    """Create meaningful names for clusters based on analysis"""
    
    df = pd.read_excel('output/voyager_bom_quick_clustering_results.xlsx', sheet_name='Material_Clusters')
    
    cluster_names = {}
    cluster_descriptions = {}
    
    for cluster_id in sorted(df['material_cluster'].unique()):
        cluster_items = df[df['material_cluster'] == cluster_id]
        descriptions = ' '.join(cluster_items['Item Description'].astype(str).str.lower())
        
        # Analyze cluster characteristics
        size = len(cluster_items)
        percentage = size / len(df) * 100
        
        # Look for dominant patterns
        if size > 1000:  # Large cluster
            if 'resistor' in descriptions and 'capacitor' in descriptions:
                cluster_names[cluster_id] = "Electronic Components (Mixed)"
                cluster_descriptions[cluster_id] = f"Standard electronic components and mixed materials ({size} items, {percentage:.1f}%)"
            else:
                cluster_names[cluster_id] = "Standard Components"
                cluster_descriptions[cluster_id] = f"General purpose components with standard materials ({size} items, {percentage:.1f}%)"
        
        elif 'steel' in descriptions or 'stainless' in descriptions:
            cluster_names[cluster_id] = "Steel Components"
            cluster_descriptions[cluster_id] = f"Steel and stainless steel components ({size} items, {percentage:.1f}%)"
        
        elif 'aluminum' in descriptions or 'anodized' in descriptions:
            cluster_names[cluster_id] = "Aluminum Components"
            cluster_descriptions[cluster_id] = f"Aluminum and anodized components ({size} items, {percentage:.1f}%)"
        
        elif 'plastic' in descriptions or 'polymer' in descriptions:
            cluster_names[cluster_id] = "Plastic Components"
            cluster_descriptions[cluster_id] = f"Plastic and polymer components ({size} items, {percentage:.1f}%)"
        
        elif 'copper' in descriptions or 'brass' in descriptions:
            cluster_names[cluster_id] = "Copper/Brass Components"
            cluster_descriptions[cluster_id] = f"Copper, brass, and conductive materials ({size} items, {percentage:.1f}%)"
        
        elif 'ceramic' in descriptions or 'glass' in descriptions:
            cluster_names[cluster_id] = "Ceramic/Glass Components"
            cluster_descriptions[cluster_id] = f"Ceramic, glass, and insulating materials ({size} items, {percentage:.1f}%)"
        
        elif 'carbon' in descriptions or 'composite' in descriptions:
            cluster_names[cluster_id] = "Carbon/Composite Components"
            cluster_descriptions[cluster_id] = f"Carbon fiber and composite materials ({size} items, {percentage:.1f}%)"
        
        else:
            # Look at component types for naming
            if 'connector' in descriptions:
                cluster_names[cluster_id] = "Specialized Connectors"
                cluster_descriptions[cluster_id] = f"Specialized connector components ({size} items, {percentage:.1f}%)"
            elif 'filter' in descriptions:
                cluster_names[cluster_id] = "Filter Components"
                cluster_descriptions[cluster_id] = f"Filter and signal processing components ({size} items, {percentage:.1f}%)"
            elif 'sensor' in descriptions:
                cluster_names[cluster_id] = "Sensor Components"
                cluster_descriptions[cluster_id] = f"Sensor and measurement components ({size} items, {percentage:.1f}%)"
            else:
                cluster_names[cluster_id] = f"Specialized Group {cluster_id}"
                cluster_descriptions[cluster_id] = f"Specialized components with unique material properties ({size} items, {percentage:.1f}%)"
    
    return cluster_names, cluster_descriptions

def save_cluster_mapping():
    """Save cluster mapping with names and descriptions"""
    
    df = pd.read_excel('output/voyager_bom_quick_clustering_results.xlsx', sheet_name='Material_Clusters')
    cluster_names, cluster_descriptions = create_cluster_names()
    
    # Create cluster mapping
    cluster_mapping = []
    for cluster_id in sorted(df['material_cluster'].unique()):
        cluster_items = df[df['material_cluster'] == cluster_id]
        cluster_mapping.append({
            'Cluster_ID': cluster_id,
            'Cluster_Name': cluster_names.get(cluster_id, f"Cluster {cluster_id}"),
            'Description': cluster_descriptions.get(cluster_id, ""),
            'Item_Count': len(cluster_items),
            'Percentage': len(cluster_items) / len(df) * 100
        })
    
    cluster_df = pd.DataFrame(cluster_mapping)
    
    # Add cluster names to the main data
    df_with_names = df.copy()
    df_with_names['Cluster_Name'] = df_with_names['material_cluster'].map(cluster_names)
    df_with_names['Cluster_Description'] = df_with_names['material_cluster'].map(cluster_descriptions)
    
    # Save to Excel
    with pd.ExcelWriter('output/voyager_bom_clusters_with_names.xlsx', engine='openpyxl') as writer:
        cluster_df.to_excel(writer, sheet_name='Cluster_Names', index=False)
        df_with_names.to_excel(writer, sheet_name='Items_with_Cluster_Names', index=False)
    
    print("CLUSTER NAMES AND DESCRIPTIONS")
    print("=" * 50)
    for _, row in cluster_df.iterrows():
        print(f"CLUSTER {row['Cluster_ID']}: {row['Cluster_Name']}")
        print(f"  Description: {row['Description']}")
        print(f"  Items: {row['Item_Count']} ({row['Percentage']:.1f}%)")
        print()

if __name__ == "__main__":
    analyze_material_clusters()
    print("\n" + "="*50 + "\n")
    save_cluster_mapping()
    print("\nFiles created:")
    print("- output/voyager_bom_clusters_with_names.xlsx")
