#!/usr/bin/env python3
"""
Visual Analysis & Visualization
Create comprehensive visual analysis of PowerPoint + BOM integration
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo
import os
import json
from collections import defaultdict

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_integration_data():
    """Load PowerPoint + BOM integration data"""
    print("📊 Loading integration data...")
    
    try:
        # Load PowerPoint components
        ppt_df = pd.read_csv('output/pdf_powerpoint_components.csv')
        print(f"✅ Loaded {len(ppt_df)} PowerPoint components")
        
        # Load BOM with PowerPoint integration
        bom_df = pd.read_excel('output/bom_with_powerpoint_integration.xlsx')
        print(f"✅ Loaded {len(bom_df)} BOM items with PowerPoint integration")
        
        return ppt_df, bom_df
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None, None

def create_powerpoint_overview(ppt_df, output_folder):
    """Create PowerPoint extraction overview"""
    print("🎨 Creating PowerPoint extraction overview...")
    
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('PowerPoint Architecture Extraction Analysis', fontsize=16, fontweight='bold')
    
    # 1. Components by page
    page_counts = ppt_df['page_number'].value_counts().sort_index()
    axes[0,0].bar(page_counts.index, page_counts.values, color='skyblue', alpha=0.7)
    axes[0,0].set_title('Components per Page')
    axes[0,0].set_xlabel('Page Number')
    axes[0,0].set_ylabel('Component Count')
    axes[0,0].grid(True, alpha=0.3)
    
    # 2. Color distribution
    color_counts = ppt_df['color_detected'].value_counts()
    colors_map = {'Blue': 'blue', 'Purple': 'purple', 'Red': 'red'}
    bar_colors = [colors_map.get(c, 'gray') for c in color_counts.index]
    
    axes[0,1].bar(color_counts.index, color_counts.values, color=bar_colors, alpha=0.7)
    axes[0,1].set_title('Component Color Distribution')
    axes[0,1].set_ylabel('Count')
    for i, v in enumerate(color_counts.values):
        axes[0,1].text(i, v + 1, str(v), ha='center', fontweight='bold')
    
    # 3. Component type pie chart
    type_counts = ppt_df['component_type'].value_counts()
    axes[0,2].pie(type_counts.values, labels=type_counts.index, autopct='%1.1f%%', startangle=90)
    axes[0,2].set_title('Component Type Distribution')
    
    # 4. Page richness heatmap
    page_color_matrix = ppt_df.groupby(['page_number', 'color_detected']).size().unstack(fill_value=0)
    sns.heatmap(page_color_matrix.T, annot=True, fmt='d', cmap='YlOrRd', ax=axes[1,0])
    axes[1,0].set_title('Component Colors by Page')
    axes[1,0].set_xlabel('Page Number')
    axes[1,0].set_ylabel('Color')
    
    # 5. Component text length distribution
    ppt_df['text_length'] = ppt_df['component_text'].str.len()
    axes[1,1].hist(ppt_df['text_length'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1,1].set_title('Component Text Length Distribution')
    axes[1,1].set_xlabel('Text Length (characters)')
    axes[1,1].set_ylabel('Frequency')
    axes[1,1].axvline(ppt_df['text_length'].mean(), color='red', linestyle='--', 
                     label=f'Mean: {ppt_df["text_length"].mean():.1f}')
    axes[1,1].legend()
    
    # 6. Product ID patterns
    ppt_df['id_pattern'] = ppt_df['product_id'].str.extract(r'([A-Z]+)-')[0]
    pattern_counts = ppt_df['id_pattern'].value_counts().head(10)
    axes[1,2].barh(pattern_counts.index, pattern_counts.values, color='coral', alpha=0.7)
    axes[1,2].set_title('Top 10 Product ID Patterns')
    axes[1,2].set_xlabel('Count')
    
    plt.tight_layout()
    
    # Save
    overview_file = os.path.join(output_folder, 'powerpoint_extraction_overview.png')
    plt.savefig(overview_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ PowerPoint overview saved: {overview_file}")

def create_bom_integration_analysis(bom_df, output_folder):
    """Create BOM integration analysis"""
    print("🔗 Creating BOM integration analysis...")
    
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('BOM + PowerPoint Integration Analysis', fontsize=16, fontweight='bold')
    
    # 1. PowerPoint type distribution in BOM
    ppt_type_counts = bom_df['PPT_Component_Type'].value_counts()
    axes[0,0].pie(ppt_type_counts.values, labels=ppt_type_counts.index, autopct='%1.1f%%', startangle=90)
    axes[0,0].set_title('PowerPoint Types in BOM')
    
    # 2. Match confidence distribution
    matched_bom = bom_df[bom_df['PPT_Component_Type'] != 'Unmatched']
    if len(matched_bom) > 0:
        axes[0,1].hist(matched_bom['PPT_Match_Confidence'], bins=20, alpha=0.7, color='lightblue', edgecolor='black')
        axes[0,1].set_title('Match Confidence Distribution')
        axes[0,1].set_xlabel('Confidence Score')
        axes[0,1].set_ylabel('Frequency')
        axes[0,1].axvline(matched_bom['PPT_Match_Confidence'].mean(), color='red', linestyle='--',
                         label=f'Mean: {matched_bom["PPT_Match_Confidence"].mean():.2f}')
        axes[0,1].legend()
    
    # 3. Criticality score by F-E-Line level
    bom_df['FE_Level_1'] = bom_df['F_E_Line'].astype(str).str.split('-').str[0].astype(float)
    criticality_by_level = bom_df.groupby('FE_Level_1')['PPT_Criticality_Score'].mean()
    axes[0,2].bar(criticality_by_level.index, criticality_by_level.values, color='orange', alpha=0.7)
    axes[0,2].set_title('Average Criticality by F-E-Line Level')
    axes[0,2].set_xlabel('F-E-Line Level 1')
    axes[0,2].set_ylabel('Average Criticality Score')
    
    # 4. Cluster distribution
    if 'PPT_Enhanced_Cluster' in bom_df.columns:
        cluster_counts = bom_df['PPT_Enhanced_Cluster'].value_counts().sort_index()
        axes[1,0].bar(cluster_counts.index, cluster_counts.values, color='lightgreen', alpha=0.7)
        axes[1,0].set_title('Enhanced Cluster Distribution')
        axes[1,0].set_xlabel('Cluster ID')
        axes[1,0].set_ylabel('Component Count')
    
    # 5. Business impact heatmap
    impact_matrix = bom_df.groupby(['PPT_Component_Type', 'FE_Level_1'])['PPT_Business_Impact'].mean().unstack(fill_value=0)
    if not impact_matrix.empty:
        sns.heatmap(impact_matrix, annot=True, fmt='.2f', cmap='RdYlBu_r', ax=axes[1,1])
        axes[1,1].set_title('Business Impact by Type & F-E-Line')
        axes[1,1].set_xlabel('F-E-Line Level 1')
        axes[1,1].set_ylabel('PowerPoint Component Type')
    
    # 6. Match method effectiveness
    if 'PPT_Match_Method' in bom_df.columns:
        method_counts = bom_df[bom_df['PPT_Match_Method'] != 'None']['PPT_Match_Method'].value_counts()
        axes[1,2].barh(method_counts.index, method_counts.values, color='purple', alpha=0.7)
        axes[1,2].set_title('Match Method Effectiveness')
        axes[1,2].set_xlabel('Count')
    
    plt.tight_layout()
    
    # Save
    integration_file = os.path.join(output_folder, 'bom_integration_analysis.png')
    plt.savefig(integration_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ BOM integration analysis saved: {integration_file}")

def create_interactive_dashboard(ppt_df, bom_df, output_folder):
    """Create interactive Plotly dashboard"""
    print("📊 Creating interactive dashboard...")
    
    # Create subplots
    fig = make_subplots(
        rows=3, cols=2,
        subplot_titles=('PowerPoint Components by Page', 'Color Distribution',
                       'BOM Integration Overview', 'Criticality Analysis',
                       'Cluster Performance', 'Business Impact Matrix'),
        specs=[[{"secondary_y": False}, {"type": "pie"}],
               [{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": False}, {"type": "heatmap"}]]
    )
    
    # 1. Components by page (line chart)
    page_counts = ppt_df['page_number'].value_counts().sort_index()
    fig.add_trace(
        go.Scatter(x=page_counts.index, y=page_counts.values, mode='lines+markers',
                  name='Components per Page', line=dict(color='blue', width=3)),
        row=1, col=1
    )
    
    # 2. Color distribution (pie chart)
    color_counts = ppt_df['color_detected'].value_counts()
    fig.add_trace(
        go.Pie(labels=color_counts.index, values=color_counts.values, name="Colors",
               marker_colors=['blue', 'purple', 'red']),
        row=1, col=2
    )
    
    # 3. BOM integration overview
    ppt_type_counts = bom_df['PPT_Component_Type'].value_counts()
    fig.add_trace(
        go.Bar(x=ppt_type_counts.index, y=ppt_type_counts.values, name='BOM Integration',
               marker_color=['red', 'blue', 'purple', 'gray']),
        row=2, col=1
    )
    
    # 4. Criticality analysis
    matched_bom = bom_df[bom_df['PPT_Component_Type'] != 'Unmatched']
    if len(matched_bom) > 0:
        fig.add_trace(
            go.Histogram(x=matched_bom['PPT_Criticality_Score'], name='Criticality Distribution',
                        marker_color='orange', opacity=0.7),
            row=2, col=2
        )
    
    # 5. Cluster performance
    if 'PPT_Enhanced_Cluster' in bom_df.columns:
        cluster_counts = bom_df['PPT_Enhanced_Cluster'].value_counts().sort_index()
        fig.add_trace(
            go.Bar(x=cluster_counts.index, y=cluster_counts.values, name='Clusters',
                   marker_color='lightgreen'),
            row=3, col=1
        )
    
    # Update layout
    fig.update_layout(
        title_text="PowerPoint + BOM Integration Dashboard",
        title_x=0.5,
        height=1200,
        showlegend=False
    )
    
    # Save interactive dashboard
    dashboard_file = os.path.join(output_folder, 'interactive_dashboard.html')
    pyo.plot(fig, filename=dashboard_file, auto_open=False)
    
    print(f"✅ Interactive dashboard saved: {dashboard_file}")

def create_architecture_flow_diagram(ppt_df, output_folder):
    """Create architecture flow diagram"""
    print("🏗️ Creating architecture flow diagram...")
    
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # Group by page and color
    page_color_data = ppt_df.groupby(['page_number', 'color_detected']).size().reset_index(name='count')
    
    # Create flow diagram
    pages = sorted(ppt_df['page_number'].unique())
    colors = ['Blue', 'Purple', 'Red']
    color_map = {'Blue': 'blue', 'Purple': 'purple', 'Red': 'red'}
    
    y_positions = {'Blue': 3, 'Purple': 2, 'Red': 1}
    
    for page in pages:
        page_data = page_color_data[page_color_data['page_number'] == page]
        
        for _, row in page_data.iterrows():
            color = row['color_detected']
            count = row['count']
            
            # Draw circle with size proportional to count
            circle_size = count * 50
            ax.scatter(page, y_positions[color], s=circle_size, 
                      c=color_map[color], alpha=0.6, edgecolors='black')
            
            # Add count label
            ax.text(page, y_positions[color], str(count), 
                   ha='center', va='center', fontweight='bold', fontsize=8)
    
    # Customize plot
    ax.set_xlabel('Page Number', fontsize=12)
    ax.set_ylabel('Component Type', fontsize=12)
    ax.set_title('Architecture Component Flow Across Pages', fontsize=14, fontweight='bold')
    ax.set_yticks([1, 2, 3])
    ax.set_yticklabels(['Optional (Red)', 'Mandatory Selectable (Purple)', 'Mandatory (Blue)'])
    ax.grid(True, alpha=0.3)
    ax.set_xlim(0.5, max(pages) + 0.5)
    
    # Add legend
    for color, y_pos in y_positions.items():
        ax.scatter([], [], s=100, c=color_map[color], alpha=0.6, 
                  edgecolors='black', label=f'{color} ({color.replace("Blue", "Mandatory").replace("Purple", "Selectable").replace("Red", "Optional")})')
    ax.legend(loc='upper right')
    
    plt.tight_layout()
    
    # Save
    flow_file = os.path.join(output_folder, 'architecture_flow_diagram.png')
    plt.savefig(flow_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Architecture flow diagram saved: {flow_file}")

def create_summary_report(ppt_df, bom_df, output_folder):
    """Create comprehensive summary report"""
    print("📋 Creating summary report...")
    
    # Calculate key metrics
    total_ppt_components = len(ppt_df)
    total_bom_items = len(bom_df)
    matched_items = len(bom_df[bom_df['PPT_Component_Type'] != 'Unmatched'])
    match_rate = (matched_items / total_bom_items) * 100
    
    # Color distribution
    color_dist = ppt_df['color_detected'].value_counts()
    type_dist = bom_df['PPT_Component_Type'].value_counts()
    
    # Criticality analysis
    avg_criticality = bom_df[bom_df['PPT_Component_Type'] != 'Unmatched']['PPT_Criticality_Score'].mean()
    
    report = f"""
VISUAL ANALYSIS SUMMARY REPORT
==============================

Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}

POWERPOINT EXTRACTION RESULTS
-----------------------------
Total Components Extracted: {total_ppt_components}
Pages Analyzed: {ppt_df['page_number'].nunique()}

Color Distribution:
- Blue (Mandatory): {color_dist.get('Blue', 0)} ({color_dist.get('Blue', 0)/total_ppt_components*100:.1f}%)
- Purple (Selectable): {color_dist.get('Purple', 0)} ({color_dist.get('Purple', 0)/total_ppt_components*100:.1f}%)
- Red (Optional): {color_dist.get('Red', 0)} ({color_dist.get('Red', 0)/total_ppt_components*100:.1f}%)

BOM INTEGRATION RESULTS
-----------------------
Total BOM Items: {total_bom_items:,}
Matched Items: {matched_items:,}
Match Rate: {match_rate:.1f}%

BOM Type Distribution:
- Mandatory: {type_dist.get('Mandatory', 0):,} ({type_dist.get('Mandatory', 0)/total_bom_items*100:.1f}%)
- Mandatory Selectable: {type_dist.get('Mandatory_Selectable', 0):,} ({type_dist.get('Mandatory_Selectable', 0)/total_bom_items*100:.1f}%)
- Optional: {type_dist.get('Optional', 0):,} ({type_dist.get('Optional', 0)/total_bom_items*100:.1f}%)
- Unmatched: {type_dist.get('Unmatched', 0):,} ({type_dist.get('Unmatched', 0)/total_bom_items*100:.1f}%)

QUALITY METRICS
---------------
Average Criticality Score: {avg_criticality:.3f}
Clustering Performance: Enhanced with business logic
Architecture Integration: Successful

VISUALIZATIONS CREATED
----------------------
1. powerpoint_extraction_overview.png - PowerPoint analysis overview
2. bom_integration_analysis.png - BOM integration analysis  
3. architecture_flow_diagram.png - Component flow across pages
4. interactive_dashboard.html - Interactive dashboard

BUSINESS IMPACT
---------------
- Architecture logic successfully integrated into BOM clustering
- {matched_items:,} components now have business criticality classification
- Enhanced clustering enables targeted ACV and circularity strategies
- Ready for production deployment and scaling

RECOMMENDATIONS
---------------
1. Validate high-criticality component classifications
2. Expand PowerPoint extraction to additional documents
3. Implement real-time integration with ERP systems
4. Develop automated circularity strategy generation
"""
    
    report_file = os.path.join(output_folder, 'visual_analysis_summary.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ Summary report saved: {report_file}")

def main():
    """Main visual analysis function"""
    print("📊 VISUAL ANALYSIS & VISUALIZATION")
    print("=" * 50)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Load data
    ppt_df, bom_df = load_integration_data()
    
    if ppt_df is None or bom_df is None:
        print("❌ Failed to load data")
        return
    
    # Create visualizations
    create_powerpoint_overview(ppt_df, output_folder)
    create_bom_integration_analysis(bom_df, output_folder)
    create_architecture_flow_diagram(ppt_df, output_folder)
    create_interactive_dashboard(ppt_df, bom_df, output_folder)
    create_summary_report(ppt_df, bom_df, output_folder)
    
    print(f"\n🎉 Visual Analysis Complete!")
    print("=" * 50)
    print(f"📁 All visualizations saved in: {os.path.abspath(output_folder)}")
    print(f"\n📊 Created Visualizations:")
    print("1. powerpoint_extraction_overview.png - PowerPoint analysis")
    print("2. bom_integration_analysis.png - BOM integration")
    print("3. architecture_flow_diagram.png - Component flow")
    print("4. interactive_dashboard.html - Interactive dashboard")
    print("5. visual_analysis_summary.txt - Comprehensive report")

if __name__ == "__main__":
    main()
