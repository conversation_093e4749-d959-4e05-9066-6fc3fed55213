#!/usr/bin/env python3
"""
Extraction correcte du PCM Voyager avec les vrais codes format M7010FC, R4390JA, etc.
"""

import pandas as pd
import numpy as np
import re
from collections import defaultdict

class PCMCorrectExtractor:
    def __init__(self):
        """Initialize avec le bon pattern de codes PCM"""
        self.colpali_data = None
        # Pattern correct pour les codes PCM Voyager
        self.pcm_code_pattern = r'[A-Z]\d{4,5}[A-Z]{2}'
        
    def load_colpali_data(self):
        """Charger les données ColPali"""
        try:
            self.colpali_data = pd.read_csv('output/comprehensive_colpali_extraction.csv')
            print(f"✅ Données ColPali chargées: {len(self.colpali_data)} éléments")
            return True
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def find_real_pcm_codes(self):
        """Chercher les vrais codes PCM dans le format M7010FC, R4390JA, etc."""
        print("🔍 RECHERCHE DES VRAIS CODES PCM")
        print("=" * 50)
        print("Format recherché: [Lettre][4-5 chiffres][2 lettres]")
        print("Exemples: M7010FC, R4390JA, M3340BZ, E45801DV")
        print("")
        
        real_codes = []
        
        for idx, row in self.colpali_data.iterrows():
            text = str(row['component_text'])
            product_id = str(row['product_id'])
            
            # Chercher dans le texte de description
            text_matches = re.findall(self.pcm_code_pattern, text)
            
            # Chercher dans le product_id
            id_matches = re.findall(self.pcm_code_pattern, product_id)
            
            # Combiner tous les matches
            all_matches = list(set(text_matches + id_matches))
            
            if all_matches:
                for code in all_matches:
                    real_codes.append({
                        'page': row['page_number'],
                        'pcm_code': code,
                        'original_text': text,
                        'original_id': product_id,
                        'color': row['color_detected'],
                        'position_x': row['center_x'],
                        'position_y': row['center_y']
                    })
        
        print(f"✅ Trouvé {len(real_codes)} vrais codes PCM")
        return real_codes
    
    def extract_descriptions_for_codes(self, real_codes):
        """Extraire les vraies descriptions associées aux codes PCM"""
        print("\n📋 EXTRACTION DES DESCRIPTIONS POUR LES CODES PCM")
        print("=" * 60)
        
        enhanced_codes = []
        
        for code_info in real_codes:
            # Nettoyer la description en retirant le code
            description = code_info['original_text']
            pcm_code = code_info['pcm_code']
            
            # Retirer le code de la description
            clean_description = description.replace(pcm_code, '').strip()
            
            # Nettoyer les caractères spéciaux et espaces multiples
            clean_description = re.sub(r'\s+', ' ', clean_description)
            clean_description = re.sub(r'[^\w\s-]', '', clean_description)
            
            enhanced_info = code_info.copy()
            enhanced_info['clean_description'] = clean_description
            enhanced_info['description_length'] = len(clean_description)
            
            enhanced_codes.append(enhanced_info)
        
        return enhanced_codes
    
    def analyze_by_page(self, enhanced_codes):
        """Analyser les codes par page"""
        print("\n📄 ANALYSE PAR PAGE")
        print("=" * 40)
        
        pages = defaultdict(list)
        for code in enhanced_codes:
            pages[code['page']].append(code)
        
        for page_num in sorted(pages.keys()):
            page_codes = pages[page_num]
            print(f"\n📄 PAGE {page_num} - {len(page_codes)} codes PCM trouvés")
            print("-" * 50)
            
            # Trier par position (gauche à droite, haut en bas)
            sorted_codes = sorted(page_codes, key=lambda x: (x['position_y'], x['position_x']))
            
            for i, code in enumerate(sorted_codes, 1):
                color_info = f"[{code['color']}]" if code['color'] else "[Sans couleur]"
                print(f"{i:2d}. {color_info} {code['pcm_code']}")
                print(f"    Description: {code['clean_description']}")
                print(f"    Position: ({code['position_x']:.0f}, {code['position_y']:.0f})")
                print(f"    Texte original: {code['original_text'][:60]}...")
                print("")
    
    def check_against_bom(self, enhanced_codes):
        """Vérifier si ces codes existent dans la BOM"""
        print("\n🔍 VÉRIFICATION CONTRE LA BOM")
        print("=" * 40)
        
        try:
            # Charger les données BOM intégrées
            bom_data = pd.read_csv('output/colpali_bom_integration.csv')
            
            pcm_codes = [code['pcm_code'] for code in enhanced_codes]
            bom_items = bom_data['bom_item_description'].tolist()
            
            print(f"📊 Codes PCM trouvés: {len(pcm_codes)}")
            print(f"📊 Items BOM disponibles: {len(bom_items)}")
            
            # Chercher des correspondances directes
            direct_matches = []
            for code in enhanced_codes:
                pcm_code = code['pcm_code']
                # Chercher le code dans les descriptions BOM
                for bom_item in bom_items:
                    if pcm_code in str(bom_item):
                        direct_matches.append({
                            'pcm_code': pcm_code,
                            'bom_item': bom_item,
                            'pcm_description': code['clean_description']
                        })
            
            print(f"✅ Correspondances directes trouvées: {len(direct_matches)}")
            
            if direct_matches:
                print("\nExemples de correspondances:")
                for match in direct_matches[:5]:
                    print(f"  PCM: {match['pcm_code']} - {match['pcm_description']}")
                    print(f"  BOM: {match['bom_item']}")
                    print("")
            
            return direct_matches
            
        except Exception as e:
            print(f"❌ Erreur vérification BOM: {e}")
            return []
    
    def generate_summary(self, enhanced_codes):
        """Générer un résumé des codes PCM trouvés"""
        print("\n📊 RÉSUMÉ DES CODES PCM VOYAGER")
        print("=" * 50)
        
        # Statistiques générales
        total_codes = len(enhanced_codes)
        unique_codes = len(set(code['pcm_code'] for code in enhanced_codes))
        
        print(f"Total codes trouvés: {total_codes}")
        print(f"Codes uniques: {unique_codes}")
        
        # Par couleur
        color_stats = defaultdict(int)
        for code in enhanced_codes:
            color_stats[code['color']] += 1
        
        print(f"\nRépartition par couleur:")
        for color, count in color_stats.items():
            print(f"  {color}: {count} codes")
        
        # Par page
        page_stats = defaultdict(int)
        for code in enhanced_codes:
            page_stats[code['page']] += 1
        
        print(f"\nRépartition par page:")
        for page, count in sorted(page_stats.items()):
            print(f"  Page {page}: {count} codes")
        
        # Exemples de codes trouvés
        print(f"\nExemples de codes PCM identifiés:")
        unique_codes_list = list(set(code['pcm_code'] for code in enhanced_codes))[:10]
        for code in unique_codes_list:
            print(f"  - {code}")
        
        return {
            'total_codes': total_codes,
            'unique_codes': unique_codes,
            'color_stats': dict(color_stats),
            'page_stats': dict(page_stats)
        }

def main():
    """Fonction principale pour extraction correcte"""
    print("🔍 EXTRACTION CORRECTE DU PCM VOYAGER")
    print("=" * 70)
    print("Recherche des vrais codes: M7010FC, R4390JA, M3340BZ, E45801DV...")
    print("")
    
    extractor = PCMCorrectExtractor()
    
    if not extractor.load_colpali_data():
        return None
    
    # 1. Trouver les vrais codes PCM
    real_codes = extractor.find_real_pcm_codes()
    
    if not real_codes:
        print("❌ Aucun code PCM trouvé avec le bon format")
        return None
    
    # 2. Extraire les descriptions
    enhanced_codes = extractor.extract_descriptions_for_codes(real_codes)
    
    # 3. Analyser par page
    extractor.analyze_by_page(enhanced_codes)
    
    # 4. Vérifier contre la BOM
    matches = extractor.check_against_bom(enhanced_codes)
    
    # 5. Générer le résumé
    summary = extractor.generate_summary(enhanced_codes)
    
    print(f"\n✅ EXTRACTION TERMINÉE")
    print(f"📊 {summary['total_codes']} codes PCM identifiés")
    print(f"🎯 Prêt pour la similarité avec la BOM")
    
    return extractor, enhanced_codes, matches

if __name__ == "__main__":
    extractor, codes, matches = main()
