#!/usr/bin/env python3
"""
Test the corrected Excel loading function
"""

import os
import pandas as pd

def test_excel_loading():
    print("🧪 Testing Corrected Excel Loading")
    print("=" * 40)
    
    # Import the corrected function
    try:
        from bom_similarity_analysis import load_bom_data
        print("✅ Successfully imported load_bom_data function")
    except Exception as e:
        print(f"❌ Error importing: {e}")
        return
    
    # Test with first Excel file
    input_folder = 'input'
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    
    if not excel_files:
        print("❌ No Excel files found!")
        return
    
    test_file = excel_files[0]
    print(f"\n📄 Testing with: {test_file}")
    
    file_path = os.path.join(input_folder, test_file)
    
    try:
        # Test the loading
        df = load_bom_data(file_path)
        
        if df is not None:
            print(f"\n✅ File loaded successfully!")
            print(f"📊 Final shape: {df.shape}")
            print(f"📋 Columns: {list(df.columns)}")
            
            # Check if we have the expected columns
            expected_cols = ['Item ID', 'Item Description', 'Level', 'F-E-Line']
            for col in expected_cols:
                if col in df.columns:
                    print(f"  ✅ {col}: Found")
                else:
                    print(f"  ❌ {col}: Missing")
            
            # Check levels
            if 'Level_Numeric' in df.columns:
                levels = sorted(df['Level_Numeric'].unique())
                print(f"📊 Levels found: {levels}")
                
                # Verify only 0,1,2,3
                expected_levels = [0.0, 1.0, 2.0, 3.0]
                valid_levels = [l for l in levels if l in expected_levels]
                print(f"📊 Valid levels (0,1,2,3): {valid_levels}")
            
            # Show sample data
            print(f"\n📋 Sample data (first 3 rows):")
            sample_cols = ['Item ID', 'Item Description', 'Level']
            if all(col in df.columns for col in sample_cols):
                print(df[sample_cols].head(3).to_string(index=False))
            
            # Check optimization
            if 'Occurrence_Count' in df.columns:
                total_original = df['Occurrence_Count'].sum()
                unique_items = len(df)
                print(f"\n💾 Optimization results:")
                print(f"  Original items: {total_original}")
                print(f"  Unique items: {unique_items}")
                print(f"  Reduction: {((total_original-unique_items)/total_original*100):.1f}%")
            
        else:
            print("❌ Failed to load file")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_excel_loading()
