@echo off
echo 🔧 Enhanced BOM Similarity Analysis
echo ===================================
echo.

REM Find Python
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
) else (
    py --version >nul 2>&1
    if %errorlevel% equ 0 (
        set PYTHON_CMD=py
    ) else (
        echo ❌ Python not found!
        pause
        exit /b 1
    )
)

echo ✅ Using Python: %PYTHON_CMD%

echo.
echo 📦 Installing required packages...
%PYTHON_CMD% -m pip install pandas numpy scikit-learn openpyxl --quiet

echo.
echo 🚀 Running enhanced BOM similarity analysis...
echo.
echo 📋 Enhanced Features:
echo   • Only analyzes rows with complete data in all 4 columns
echo   • Advanced TF-IDF with bigrams and stop words
echo   • Multiple similarity thresholds (Exact, High, Medium)
echo   • Detailed Excel reports with all BOM information
echo.

%PYTHON_CMD% bom_similarity_analysis.py

echo.
echo 🎉 Analysis complete! Check the 'output' folder for results.
echo.
pause
