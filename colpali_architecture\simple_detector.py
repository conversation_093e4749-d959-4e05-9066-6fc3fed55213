import cv2
import numpy as np

class SimpleArchitectureDetector:
    def __init__(self):
        """
        Simple detector for architectural elements using OpenCV
        """
        self.min_box_area = 500  # Minimum area for a box
        self.max_box_area = 50000  # Maximum area for a box
        self.min_satellite_area = 100  # Minimum area for satellite
        self.max_satellite_area = 2000  # Maximum area for satellite
    
    def detect_regions(self, img):
        """
        Detect boxes and satellites using simple computer vision
        """
        detections = []
        
        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img
        
        # Find contours
        contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            # Calculate area
            area = cv2.contourArea(contour)
            
            # Skip very small or very large contours
            if area < self.min_satellite_area or area > self.max_box_area:
                continue
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            
            # Calculate aspect ratio
            aspect_ratio = w / h if h > 0 else 0
            
            # Classify based on size and shape
            if area >= self.min_box_area:
                # Larger objects are likely boxes
                label = 'box'
                confidence = min(0.9, area / self.max_box_area)
            else:
                # Smaller objects are likely satellites
                label = 'satellite'
                confidence = min(0.8, area / self.max_satellite_area)
            
            # Filter by aspect ratio (avoid very thin lines)
            if 0.1 < aspect_ratio < 10:
                detections.append({
                    'label': label,
                    'xmin': x,
                    'ymin': y,
                    'xmax': x + w,
                    'ymax': y + h,
                    'confidence': confidence,
                    'area': area,
                    'aspect_ratio': aspect_ratio
                })
        
        return detections
    
    def detect_text_regions(self, img):
        """
        Detect text regions for masking
        """
        # Create a copy for text detection
        text_img = img.copy()
        
        # Apply morphological operations to find text-like regions
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        
        # Dilate to connect text characters
        dilated = cv2.dilate(text_img, kernel, iterations=2)
        
        # Find contours
        contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        text_regions = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # Filter by size and aspect ratio for text-like regions
            if w > 20 and h > 5 and w/h > 2:  # Text is typically wider than tall
                text_regions.append((x, y, w, h))
        
        return text_regions
    
    def enhance_image_for_detection(self, img):
        """
        Enhance image for better detection
        """
        # Convert to grayscale
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # Apply adaptive threshold
        thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                      cv2.THRESH_BINARY_INV, 11, 2)
        
        # Apply morphological operations to clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
