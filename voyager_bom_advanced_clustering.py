import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import re
import json

# DATA LOADING AND PREPROCESSING

def load_voyager_bom_data():
    """Load Voyager BOM data from available sources"""

    # Try enhanced BOM first
    try:
        df = pd.read_excel("output/voyager_5408509_bom_pcm_aggregated.xlsx")
        print(f"Loaded enhanced BOM data: {len(df)} rows")

        # Standardize column names
        column_mapping = {
            'F_E_Line': 'F-E-Line',
            'Item_Number': 'Item Number',
            'Item_Description': 'Item Description'
        }
        df = df.rename(columns=column_mapping)
        return df
    except Exception as e:
        print(f"Enhanced BOM loading failed: {e}")
        pass

    # Try original BOM
    try:
        df = pd.read_excel("5408509_41c3917a-ff9f-427d-9862-2379b6e85a35_1750170742103.xlsx", sheet_name="Lines")
        print(f"Loaded original BOM data: {len(df)} rows")
        return df
    except Exception as e:
        print(f"Original BOM loading failed: {e}")
        pass

    # Fallback to tree statistics
    with open('output/voyager_bom_tree_stats.json', 'r') as f:
        stats = json.load(f)

    rows = []
    for level_str, count in stats['level_distribution'].items():
        level = int(level_str)
        for i in range(count):
            rows.append({
                'Level': level,
                'Item Number': f'ITEM_{level}_{i+1:06d}',
                'Item Description': f'Component Level {level} Item {i+1}',
                'F-E-Line': f'1-1-{level+1}' if level >= 0 else 'ROOT'
            })

    df = pd.DataFrame(rows)
    print(f"Created synthetic BOM data: {len(df)} rows")
    return df

def remove_accessories_and_non_physical_items(df):
    """Remove accessories, manuals, software, and non-physical items"""
    
    # Define exclusion patterns
    exclusion_patterns = [
        r'manual|documentation|doc|guide|instruction',
        r'software|firmware|program|application|app',
        r'paper|label|sticker|decal|marking',
        r'accessory|accessories|optional|spare',
        r'packaging|box|container|bag|wrap',
        r'cable tie|zip tie|fastener|screw|bolt|nut',
        r'warranty|service|maintenance|support',
        r'training|education|course|tutorial',
        r'license|subscription|agreement|contract'
    ]
    
    # Combine patterns
    combined_pattern = '|'.join(exclusion_patterns)
    
    # Initial count
    initial_count = len(df)
    
    # Filter out non-physical items
    mask = ~df['Item Description'].str.contains(combined_pattern, case=False, na=False)
    df_filtered = df[mask].copy()
    
    # Additional filtering by item number patterns
    item_exclusion_patterns = [
        r'^DOC',
        r'^MAN',
        r'^SW',
        r'^LIC',
        r'^ACC'
    ]
    
    for pattern in item_exclusion_patterns:
        mask = ~df_filtered['Item Number'].str.contains(pattern, case=False, na=False)
        df_filtered = df_filtered[mask]
    
    removed_count = initial_count - len(df_filtered)
    removed_items = df[~df.index.isin(df_filtered.index)]
    
    print(f"Removed {removed_count} non-physical items:")
    print("Categories removed:")
    for pattern in exclusion_patterns:
        count = df['Item Description'].str.contains(pattern, case=False, na=False).sum()
        if count > 0:
            print(f"  - {pattern}: {count} items")
    
    return df_filtered, removed_items

# SBERT EMBEDDING GENERATION

def generate_sbert_embeddings(descriptions):
    """Generate SBERT embeddings for item descriptions"""
    
    model = SentenceTransformer('all-MiniLM-L6-v2')
    embeddings = model.encode(descriptions.tolist())
    
    return embeddings

# CLUSTERING BY FUNCTION/ACTION

def cluster_by_function(df, embeddings):
    """Cluster components by their function/action"""
    
    # Extract functional keywords
    functional_keywords = []
    for desc in df['Item Description']:
        # Extract action verbs and functional terms
        words = re.findall(r'\b(?:pump|valve|sensor|motor|drive|control|filter|heat|cool|measure|detect|connect|support|mount|seal|protect)\b', 
                          desc.lower())
        functional_keywords.append(' '.join(words) if words else desc.lower())
    
    # Generate embeddings for functional descriptions
    func_embeddings = generate_sbert_embeddings(pd.Series(functional_keywords))
    
    # Clustering
    results = {}
    
    # KMeans clustering
    for n_clusters in [5, 8, 10, 12]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        labels = kmeans.fit_predict(func_embeddings)
        
        silhouette = silhouette_score(func_embeddings, labels)
        calinski = calinski_harabasz_score(func_embeddings, labels)
        davies_bouldin = davies_bouldin_score(func_embeddings, labels)
        
        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    # Hierarchical clustering
    for n_clusters in [5, 8, 10, 12]:
        hierarchical = AgglomerativeClustering(n_clusters=n_clusters)
        labels = hierarchical.fit_predict(func_embeddings)
        
        silhouette = silhouette_score(func_embeddings, labels)
        calinski = calinski_harabasz_score(func_embeddings, labels)
        davies_bouldin = davies_bouldin_score(func_embeddings, labels)
        
        results[f'hierarchical_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    # DBSCAN clustering
    for eps in [0.3, 0.5, 0.7]:
        dbscan = DBSCAN(eps=eps, min_samples=3)
        labels = dbscan.fit_predict(func_embeddings)
        
        if len(set(labels)) > 1:
            silhouette = silhouette_score(func_embeddings, labels)
            calinski = calinski_harabasz_score(func_embeddings, labels)
            davies_bouldin = davies_bouldin_score(func_embeddings, labels)
            
            results[f'dbscan_{eps}'] = {
                'labels': labels,
                'silhouette_score': silhouette,
                'calinski_harabasz_score': calinski,
                'davies_bouldin_score': davies_bouldin,
                'n_clusters': len(set(labels)) - (1 if -1 in labels else 0)
            }
    
    return results, func_embeddings

# CLUSTERING BY STRUCTURE

def cluster_by_structure(df, embeddings):
    """Cluster components by their structural characteristics"""
    
    # Extract structural features
    structural_features = []
    
    for idx, row in df.iterrows():
        features = []
        
        # Level-based features
        features.append(f"level_{row['Level']}")
        
        # F-E-Line based features
        if 'F-E-Line' in row and pd.notna(row['F-E-Line']):
            fe_parts = str(row['F-E-Line']).split('-')
            for i, part in enumerate(fe_parts):
                features.append(f"fe_{i}_{part}")
        
        # Description-based structural terms
        desc = str(row['Item Description']).lower()
        structural_terms = re.findall(r'\b(?:assembly|sub|component|part|module|unit|system|housing|frame|bracket|plate|panel)\b', desc)
        features.extend(structural_terms)
        
        structural_features.append(' '.join(features))
    
    # Generate embeddings for structural descriptions
    struct_embeddings = generate_sbert_embeddings(pd.Series(structural_features))
    
    # Clustering
    results = {}
    
    # KMeans clustering
    for n_clusters in [5, 8, 10, 12]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        labels = kmeans.fit_predict(struct_embeddings)
        
        silhouette = silhouette_score(struct_embeddings, labels)
        calinski = calinski_harabasz_score(struct_embeddings, labels)
        davies_bouldin = davies_bouldin_score(struct_embeddings, labels)
        
        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    # Hierarchical clustering
    for n_clusters in [5, 8, 10, 12]:
        hierarchical = AgglomerativeClustering(n_clusters=n_clusters)
        labels = hierarchical.fit_predict(struct_embeddings)
        
        silhouette = silhouette_score(struct_embeddings, labels)
        calinski = calinski_harabasz_score(struct_embeddings, labels)
        davies_bouldin = davies_bouldin_score(struct_embeddings, labels)
        
        results[f'hierarchical_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    return results, struct_embeddings

# CLUSTERING BY PHYSICAL ASSEMBLY

def cluster_by_physical_assembly(df, embeddings):
    """Cluster components by physical assembly characteristics"""
    
    # Extract physical assembly features
    assembly_features = []
    
    for idx, row in df.iterrows():
        features = []
        
        # Physical assembly terms from description
        desc = str(row['Item Description']).lower()
        
        # Assembly type
        assembly_types = re.findall(r'\b(?:mechanical|electrical|hydraulic|pneumatic|electronic|optical|thermal)\b', desc)
        features.extend([f"type_{t}" for t in assembly_types])
        
        # Assembly function
        assembly_functions = re.findall(r'\b(?:rotating|linear|static|dynamic|fixed|movable|flexible|rigid)\b', desc)
        features.extend([f"function_{f}" for f in assembly_functions])
        
        # Physical characteristics
        physical_chars = re.findall(r'\b(?:metal|plastic|rubber|ceramic|composite|steel|aluminum|copper|brass)\b', desc)
        features.extend([f"material_{m}" for m in physical_chars])
        
        # Size indicators
        size_indicators = re.findall(r'\b(?:small|medium|large|micro|mini|standard|heavy|light)\b', desc)
        features.extend([f"size_{s}" for s in size_indicators])
        
        assembly_features.append(' '.join(features) if features else desc)
    
    # Generate embeddings for assembly descriptions
    assembly_embeddings = generate_sbert_embeddings(pd.Series(assembly_features))
    
    # Clustering
    results = {}
    
    # KMeans clustering
    for n_clusters in [6, 8, 10, 12, 15]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        labels = kmeans.fit_predict(assembly_embeddings)
        
        silhouette = silhouette_score(assembly_embeddings, labels)
        calinski = calinski_harabasz_score(assembly_embeddings, labels)
        davies_bouldin = davies_bouldin_score(assembly_embeddings, labels)
        
        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    # Hierarchical clustering
    for n_clusters in [6, 8, 10, 12, 15]:
        hierarchical = AgglomerativeClustering(n_clusters=n_clusters)
        labels = hierarchical.fit_predict(assembly_embeddings)
        
        silhouette = silhouette_score(assembly_embeddings, labels)
        calinski = calinski_harabasz_score(assembly_embeddings, labels)
        davies_bouldin = davies_bouldin_score(assembly_embeddings, labels)
        
        results[f'hierarchical_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    return results, assembly_embeddings

def main():
    """Main execution function"""
    
    # Load data
    print("LOADING VOYAGER BOM DATA")
    df = load_voyager_bom_data()
    
    # Remove non-physical items
    print("\nREMOVING NON-PHYSICAL ITEMS")
    df_clean, removed_items = remove_accessories_and_non_physical_items(df)
    
    # Generate base embeddings
    print("\nGENERATING SBERT EMBEDDINGS")
    base_embeddings = generate_sbert_embeddings(df_clean['Item Description'])
    
    # Functional clustering
    print("\nCLUSTERING BY FUNCTION")
    func_results, func_embeddings = cluster_by_function(df_clean, base_embeddings)
    
    # Structural clustering
    print("\nCLUSTERING BY STRUCTURE")
    struct_results, struct_embeddings = cluster_by_structure(df_clean, base_embeddings)
    
    # Physical assembly clustering
    print("\nCLUSTERING BY PHYSICAL ASSEMBLY")
    assembly_results, assembly_embeddings = cluster_by_physical_assembly(df_clean, base_embeddings)
    
    return df_clean, removed_items, func_results, struct_results, assembly_results

# WEIGHT AND MATERIAL CLUSTERING

def extract_weight_features(df):
    """Extract weight-related features from descriptions"""

    weight_features = []

    for desc in df['Item Description']:
        desc_lower = str(desc).lower()

        # Weight indicators
        weight_terms = re.findall(r'\b(?:heavy|light|lightweight|massive|compact|dense|thin|thick)\b', desc_lower)

        # Size-related weight implications
        size_weight = re.findall(r'\b(?:large|small|big|tiny|huge|miniature|standard|oversized)\b', desc_lower)

        # Material density implications
        density_materials = re.findall(r'\b(?:steel|iron|aluminum|titanium|plastic|carbon|composite)\b', desc_lower)

        features = weight_terms + size_weight + density_materials
        weight_features.append(' '.join(features) if features else 'standard_weight')

    return weight_features

def extract_material_features(df):
    """Extract material-related features from descriptions"""

    material_features = []

    for desc in df['Item Description']:
        desc_lower = str(desc).lower()

        # Primary materials
        materials = re.findall(r'\b(?:steel|aluminum|plastic|rubber|ceramic|glass|carbon|titanium|brass|copper|bronze|iron|wood|fabric|leather|silicon|polymer)\b', desc_lower)

        # Material properties
        properties = re.findall(r'\b(?:stainless|galvanized|anodized|coated|treated|hardened|tempered|reinforced|laminated)\b', desc_lower)

        # Material categories
        categories = re.findall(r'\b(?:metal|metallic|non-metal|composite|synthetic|natural|organic|inorganic)\b', desc_lower)

        features = materials + properties + categories
        material_features.append(' '.join(features) if features else 'standard_material')

    return material_features

def cluster_by_weight(df, base_embeddings):
    """Cluster components by weight characteristics"""

    weight_features = extract_weight_features(df)
    weight_embeddings = generate_sbert_embeddings(pd.Series(weight_features))

    results = {}

    # KMeans clustering
    for n_clusters in [4, 6, 8, 10]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        labels = kmeans.fit_predict(weight_embeddings)

        silhouette = silhouette_score(weight_embeddings, labels)
        calinski = calinski_harabasz_score(weight_embeddings, labels)
        davies_bouldin = davies_bouldin_score(weight_embeddings, labels)

        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }

    # Hierarchical clustering
    for n_clusters in [4, 6, 8, 10]:
        hierarchical = AgglomerativeClustering(n_clusters=n_clusters)
        labels = hierarchical.fit_predict(weight_embeddings)

        silhouette = silhouette_score(weight_embeddings, labels)
        calinski = calinski_harabasz_score(weight_embeddings, labels)
        davies_bouldin = davies_bouldin_score(weight_embeddings, labels)

        results[f'hierarchical_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }

    return results, weight_embeddings

def cluster_by_material(df, base_embeddings):
    """Cluster components by material characteristics"""

    material_features = extract_material_features(df)
    material_embeddings = generate_sbert_embeddings(pd.Series(material_features))

    results = {}

    # KMeans clustering
    for n_clusters in [5, 7, 9, 12]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        labels = kmeans.fit_predict(material_embeddings)

        silhouette = silhouette_score(material_embeddings, labels)
        calinski = calinski_harabasz_score(material_embeddings, labels)
        davies_bouldin = davies_bouldin_score(material_embeddings, labels)

        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }

    # Hierarchical clustering
    for n_clusters in [5, 7, 9, 12]:
        hierarchical = AgglomerativeClustering(n_clusters=n_clusters)
        labels = hierarchical.fit_predict(material_embeddings)

        silhouette = silhouette_score(material_embeddings, labels)
        calinski = calinski_harabasz_score(material_embeddings, labels)
        davies_bouldin = davies_bouldin_score(material_embeddings, labels)

        results[f'hierarchical_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }

    return results, material_embeddings

def cluster_by_combined_weight_material(df, weight_embeddings, material_embeddings):
    """Cluster components by combined weight and material characteristics"""

    # Combine weight and material embeddings
    combined_embeddings = np.hstack([weight_embeddings, material_embeddings])

    # Standardize combined features
    scaler = StandardScaler()
    combined_embeddings_scaled = scaler.fit_transform(combined_embeddings)

    results = {}

    # KMeans clustering
    for n_clusters in [6, 8, 10, 12, 15]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        labels = kmeans.fit_predict(combined_embeddings_scaled)

        silhouette = silhouette_score(combined_embeddings_scaled, labels)
        calinski = calinski_harabasz_score(combined_embeddings_scaled, labels)
        davies_bouldin = davies_bouldin_score(combined_embeddings_scaled, labels)

        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }

    # Hierarchical clustering
    for n_clusters in [6, 8, 10, 12, 15]:
        hierarchical = AgglomerativeClustering(n_clusters=n_clusters)
        labels = hierarchical.fit_predict(combined_embeddings_scaled)

        silhouette = silhouette_score(combined_embeddings_scaled, labels)
        calinski = calinski_harabasz_score(combined_embeddings_scaled, labels)
        davies_bouldin = davies_bouldin_score(combined_embeddings_scaled, labels)

        results[f'hierarchical_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }

    return results, combined_embeddings_scaled

# SIMILARITY MATRIX GENERATION

def generate_similarity_matrix(embeddings, labels, method_name):
    """Generate similarity matrix for clustering results"""

    from sklearn.metrics.pairwise import cosine_similarity

    # Calculate cosine similarity matrix
    similarity_matrix = cosine_similarity(embeddings)

    # Create cluster-based similarity summary
    unique_labels = np.unique(labels)
    cluster_similarity = np.zeros((len(unique_labels), len(unique_labels)))

    for i, label_i in enumerate(unique_labels):
        for j, label_j in enumerate(unique_labels):
            mask_i = labels == label_i
            mask_j = labels == label_j

            if np.any(mask_i) and np.any(mask_j):
                cluster_similarity[i, j] = similarity_matrix[np.ix_(mask_i, mask_j)].mean()

    return similarity_matrix, cluster_similarity, unique_labels

# RESULTS COMPILATION AND SCORING

def compile_clustering_results(df_clean, func_results, struct_results, assembly_results,
                             weight_results, material_results, combined_results):
    """Compile all clustering results with scores"""

    all_results = {}

    # Function clustering results
    all_results['function'] = func_results

    # Structure clustering results
    all_results['structure'] = struct_results

    # Physical assembly clustering results
    all_results['physical_assembly'] = assembly_results

    # Weight clustering results
    all_results['weight'] = weight_results

    # Material clustering results
    all_results['material'] = material_results

    # Combined weight-material clustering results
    all_results['combined_weight_material'] = combined_results

    return all_results

def find_best_clustering_models(all_results):
    """Find best performing clustering models for each approach"""

    best_models = {}

    for approach, results in all_results.items():
        best_silhouette = -1
        best_model = None

        for model_name, metrics in results.items():
            if metrics['silhouette_score'] > best_silhouette:
                best_silhouette = metrics['silhouette_score']
                best_model = model_name

        best_models[approach] = {
            'model': best_model,
            'metrics': results[best_model] if best_model else None
        }

    return best_models

# MAIN EXECUTION WITH EXTENDED CLUSTERING

def main():
    """Main execution function with all clustering approaches"""

    # Load data
    print("LOADING VOYAGER BOM DATA")
    df = load_voyager_bom_data()

    # Remove non-physical items
    print("REMOVING NON-PHYSICAL ITEMS")
    df_clean, removed_items = remove_accessories_and_non_physical_items(df)

    # Generate base embeddings
    print("GENERATING SBERT EMBEDDINGS")
    base_embeddings = generate_sbert_embeddings(df_clean['Item Description'])

    # Functional clustering
    print("CLUSTERING BY FUNCTION")
    func_results, func_embeddings = cluster_by_function(df_clean, base_embeddings)

    # Structural clustering
    print("CLUSTERING BY STRUCTURE")
    struct_results, struct_embeddings = cluster_by_structure(df_clean, base_embeddings)

    # Physical assembly clustering
    print("CLUSTERING BY PHYSICAL ASSEMBLY")
    assembly_results, assembly_embeddings = cluster_by_physical_assembly(df_clean, base_embeddings)

    # Weight clustering
    print("CLUSTERING BY WEIGHT")
    weight_results, weight_embeddings = cluster_by_weight(df_clean, base_embeddings)

    # Material clustering
    print("CLUSTERING BY MATERIAL")
    material_results, material_embeddings = cluster_by_material(df_clean, base_embeddings)

    # Combined weight-material clustering
    print("CLUSTERING BY COMBINED WEIGHT AND MATERIAL")
    combined_results, combined_embeddings = cluster_by_combined_weight_material(df_clean, weight_embeddings, material_embeddings)

    # Compile all results
    print("COMPILING RESULTS")
    all_results = compile_clustering_results(df_clean, func_results, struct_results, assembly_results,
                                           weight_results, material_results, combined_results)

    # Find best models
    best_models = find_best_clustering_models(all_results)

    return df_clean, removed_items, all_results, best_models

if __name__ == "__main__":
    df_clean, removed_items, all_results, best_models = main()
