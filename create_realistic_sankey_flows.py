import json
import pandas as pd
import numpy as np
from collections import defaultdict

def create_realistic_flow_data():
    """Create realistic flow data based on actual BOM hierarchy"""
    
    # Load tree statistics
    with open('output/voyager_bom_tree_stats.json', 'r', encoding='utf-8') as f:
        stats = json.load(f)
    
    # Get level distribution
    level_dist = stats['level_distribution']
    
    # Create nodes with realistic data
    nodes = []
    node_id = 0
    
    # Add root node
    nodes.append({
        'id': node_id,
        'name': 'VOYAGER',
        'level': -1,
        'count': 1,
        'color': '#e74c3c',
        'description': 'SIGNA Voyager 1.5T MRI System'
    })
    node_id += 1
    
    # Add level nodes with actual counts
    level_colors = {
        0: '#9b59b6',  # Purple
        1: '#27ae60',  # Green
        2: '#f39c12',  # Orange
        3: '#3498db',  # Blue
        4: '#1abc9c',  # Turquoise
        5: '#34495e',  # Dark gray
        6: '#95a5a6',  # Gray
        7: '#e67e22',  # Dark orange
        8: '#2ecc71',  # Light green
        9: '#8e44ad',  # Purple
        10: '#16a085', # Dark turquoise
    }
    
    max_display_level = 10
    for level in range(0, max_display_level + 1):
        level_str = str(level)
        if level_str in level_dist:
            count = level_dist[level_str]
            nodes.append({
                'id': node_id,
                'name': f'Level {level}',
                'level': level,
                'count': count,
                'color': level_colors.get(level, '#95a5a6'),
                'description': f'Components at hierarchy level {level}'
            })
            node_id += 1
    
    # Create realistic links based on hierarchy
    links = []
    
    # Root to Level 0 (direct components)
    if len(nodes) > 1:
        links.append({
            'source': 0,  # VOYAGER
            'target': 1,  # Level 0
            'value': nodes[1]['count'],
            'color': '#e74c3c'
        })
    
    # Level to level flows (realistic parent-child relationships)
    for i in range(1, len(nodes) - 1):
        current_node = nodes[i]
        next_node = nodes[i + 1]
        
        # Calculate realistic flow based on hierarchy expansion
        # Higher levels typically have more components
        flow_ratio = min(0.8, next_node['count'] / max(current_node['count'], 1))
        flow_value = int(current_node['count'] * flow_ratio)
        
        if flow_value > 0:
            links.append({
                'source': i,
                'target': i + 1,
                'value': flow_value,
                'color': current_node['color']
            })
    
    return nodes, links, stats

def create_enhanced_sankey_html():
    """Create enhanced Sankey with realistic flows"""
    
    nodes, links, stats = create_realistic_flow_data()
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voyager BOM - Enhanced Sankey Flow Diagram</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://unpkg.com/d3-sankey@0.12.3/dist/d3-sankey.min.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }}
        
        .header::before {{
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.05) 10px,
                rgba(255,255,255,0.05) 20px
            );
            animation: slide 20s linear infinite;
        }}
        
        @keyframes slide {{
            0% {{ transform: translateX(-50px) translateY(-50px); }}
            100% {{ transform: translateX(50px) translateY(50px); }}
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 3.5em;
            font-weight: 300;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }}
        
        .header p {{
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 1.3em;
            position: relative;
            z-index: 1;
        }}
        
        .controls {{
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 2px solid #dee2e6;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }}
        
        .control-button {{
            padding: 12px 25px;
            border: none;
            border-radius: 30px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }}
        
        .control-button:hover {{
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
        }}
        
        .visualization {{
            padding: 40px;
            min-height: 900px;
            background: linear-gradient(45deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
        }}
        
        .sankey-container {{
            width: 100%;
            height: 800px;
            border: 3px solid #dee2e6;
            border-radius: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: inset 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }}
        
        .sankey-container::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(155, 89, 182, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(39, 174, 96, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }}
        
        .node rect {{
            cursor: pointer;
            stroke: #000;
            stroke-width: 2px;
            transition: all 0.3s ease;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.2));
        }}
        
        .node rect:hover {{
            stroke-width: 4px;
            filter: drop-shadow(4px 4px 8px rgba(0,0,0,0.3)) brightness(1.1);
            transform: scale(1.02);
        }}
        
        .node text {{
            pointer-events: none;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
            font-weight: bold;
            font-size: 14px;
        }}
        
        .link {{
            fill: none;
            stroke-opacity: 0.7;
            transition: all 0.3s ease;
        }}
        
        .link:hover {{
            stroke-opacity: 1;
            filter: drop-shadow(0 0 5px currentColor);
        }}
        
        .tooltip {{
            position: absolute;
            background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(52,73,94,0.9) 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            font-size: 14px;
            pointer-events: none;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 1000;
            max-width: 350px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: 2px solid rgba(255,255,255,0.1);
        }}
        
        .stats-panel {{
            margin-top: 40px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
        }}
        
        .stat-card {{
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 6px solid #3498db;
            transition: all 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }}
        
        .stat-title {{
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        
        .stat-value {{
            font-size: 3em;
            font-weight: bold;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .stat-description {{
            color: #7f8c8d;
            font-size: 1em;
            margin-top: 10px;
            line-height: 1.4;
        }}
        
        .flow-legend {{
            margin-top: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            border-radius: 15px;
            text-align: center;
        }}
        
        .legend-title {{
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        }}
        
        .legend-items {{
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }}
        
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: bold;
        }}
        
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #2c3e50;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 VOYAGER BOM Flow Analysis</h1>
            <p>Enhanced Sankey Diagram - Realistic Hierarchical Component Flows</p>
        </div>
        
        <div class="controls">
            <button class="control-button" onclick="animateFlow()">🎬 Animate Flow</button>
            <button class="control-button" onclick="highlightCriticalPath()">⚡ Critical Path</button>
            <button class="control-button" onclick="resetView()">🔄 Reset View</button>
            <button class="control-button" onclick="exportData()">💾 Export Data</button>
            <button class="control-button" onclick="toggleLabels()">🏷️ Toggle Labels</button>
        </div>
        
        <div class="visualization">
            <div id="sankey" class="sankey-container"></div>
            
            <div class="flow-legend">
                <div class="legend-title">🎨 Flow Color Legend</div>
                <div class="legend-items">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #e74c3c;"></div>
                        <span>Root System</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #9b59b6;"></div>
                        <span>Direct Components</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #27ae60;"></div>
                        <span>Major Assemblies</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #f39c12;"></div>
                        <span>Sub-assemblies</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #3498db;"></div>
                        <span>Components</span>
                    </div>
                </div>
            </div>
            
            <div class="stats-panel">
                <div class="stat-card">
                    <div class="stat-title">📊 Total Components</div>
                    <div class="stat-value">{stats['total_nodes']:,}</div>
                    <div class="stat-description">Complete BOM structure with all hierarchical levels represented</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🏗️ Maximum Depth</div>
                    <div class="stat-value">{stats['max_level']}</div>
                    <div class="stat-description">Deepest level in the hierarchical structure</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🌿 F-E-Lines</div>
                    <div class="stat-value">{stats['total_f_e_lines']}</div>
                    <div class="stat-description">Unique product lines and assembly groups</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🔗 Flow Connections</div>
                    <div class="stat-value">{len(links)}</div>
                    <div class="stat-description">Inter-level hierarchical relationships</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="tooltip" id="tooltip"></div>
    
    <script>
        // Data for D3 Sankey
        const data = {{
            nodes: {json.dumps(nodes)},
            links: {json.dumps(links)}
        }};
        
        // Set up dimensions
        const margin = {{top: 30, right: 30, bottom: 30, left: 30}};
        const width = 1700 - margin.left - margin.right;
        const height = 740 - margin.top - margin.bottom;
        
        // Create SVG
        const svg = d3.select("#sankey")
            .append("svg")
            .attr("width", width + margin.left + margin.right)
            .attr("height", height + margin.top + margin.bottom);
            
        const g = svg.append("g")
            .attr("transform", `translate(${{margin.left}},${{margin.top}})`);
        
        // Create Sankey generator
        const sankey = d3.sankey()
            .nodeWidth(100)
            .nodePadding(50)
            .extent([[1, 1], [width - 1, height - 6]]);
        
        // Generate the Sankey diagram
        const {{nodes, links}} = sankey(data);
        
        // Add links
        const link = g.append("g")
            .selectAll(".link")
            .data(links)
            .enter().append("path")
            .attr("class", "link")
            .attr("d", d3.sankeyLinkHorizontal())
            .attr("stroke", d => d.color || "#bdc3c7")
            .attr("stroke-width", d => Math.max(2, d.width))
            .style("mix-blend-mode", "multiply");
        
        // Add nodes
        const node = g.append("g")
            .selectAll(".node")
            .data(nodes)
            .enter().append("g")
            .attr("class", "node")
            .attr("transform", d => `translate(${{d.x0}},${{d.y0}})`);
        
        node.append("rect")
            .attr("height", d => d.y1 - d.y0)
            .attr("width", sankey.nodeWidth())
            .attr("fill", d => d.color)
            .attr("stroke", "#2c3e50")
            .attr("rx", 8)
            .attr("ry", 8);
        
        // Add node labels
        const nodeLabels = node.append("text")
            .attr("x", -6)
            .attr("y", d => (d.y1 - d.y0) / 2)
            .attr("dy", "0.35em")
            .attr("text-anchor", "end")
            .style("font-size", "13px")
            .style("font-weight", "bold")
            .text(d => d.name)
            .filter(d => d.x0 < width / 2)
            .attr("x", sankey.nodeWidth() + 6)
            .attr("text-anchor", "start");
        
        // Add count labels
        node.append("text")
            .attr("x", sankey.nodeWidth() / 2)
            .attr("y", d => (d.y1 - d.y0) / 2 + 20)
            .attr("dy", "0.35em")
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("fill", "white")
            .style("text-shadow", "1px 1px 2px rgba(0,0,0,0.7)")
            .text(d => d.count.toLocaleString());
        
        // Tooltip
        const tooltip = d3.select("#tooltip");
        
        // Add interactivity
        node.on("mouseover", function(event, d) {{
            tooltip.style("opacity", 1)
                .html(`
                    <strong>${{d.name}}</strong><br>
                    <strong>Level:</strong> ${{d.level}}<br>
                    <strong>Components:</strong> ${{d.count.toLocaleString()}}<br>
                    <strong>Percentage:</strong> ${{((d.count / {stats['total_nodes']}) * 100).toFixed(2)}}%<br>
                    <strong>Description:</strong> ${{d.description}}
                `)
                .style("left", (event.pageX + 15) + "px")
                .style("top", (event.pageY - 15) + "px");
        }})
        .on("mouseout", function() {{
            tooltip.style("opacity", 0);
        }});
        
        link.on("mouseover", function(event, d) {{
            tooltip.style("opacity", 1)
                .html(`
                    <strong>🔗 Flow Connection</strong><br>
                    <strong>From:</strong> ${{d.source.name}}<br>
                    <strong>To:</strong> ${{d.target.name}}<br>
                    <strong>Flow Value:</strong> ${{d.value.toLocaleString()}}<br>
                    <strong>Flow Type:</strong> Hierarchical Parent-Child
                `)
                .style("left", (event.pageX + 15) + "px")
                .style("top", (event.pageY - 15) + "px");
        }})
        .on("mouseout", function() {{
            tooltip.style("opacity", 0);
        }});
        
        // Animation and interaction functions
        let labelsVisible = true;
        
        function animateFlow() {{
            link.style("stroke-dasharray", "8,4")
                .style("stroke-dashoffset", 0)
                .transition()
                .duration(3000)
                .ease(d3.easeLinear)
                .style("stroke-dashoffset", -24)
                .on("end", function() {{
                    d3.select(this).style("stroke-dasharray", null);
                }});
        }}
        
        function highlightCriticalPath() {{
            // Highlight the main flow path
            link.style("opacity", 0.2);
            node.select("rect").style("opacity", 0.3);
            
            // Highlight critical path (first few levels)
            link.filter((d, i) => i < 3)
                .style("opacity", 1)
                .style("stroke-width", d => Math.max(4, d.width + 2));
                
            node.filter((d, i) => i < 4)
                .select("rect")
                .style("opacity", 1)
                .style("filter", "drop-shadow(0 0 10px currentColor)");
        }}
        
        function resetView() {{
            link.style("stroke-dasharray", null)
                .style("opacity", 0.7)
                .style("stroke-width", d => Math.max(2, d.width));
            node.select("rect")
                .style("opacity", 1)
                .style("filter", "drop-shadow(2px 2px 4px rgba(0,0,0,0.2))");
        }}
        
        function toggleLabels() {{
            labelsVisible = !labelsVisible;
            nodeLabels.style("opacity", labelsVisible ? 1 : 0);
        }}
        
        function exportData() {{
            const exportData = {{
                nodes: data.nodes,
                links: data.links,
                statistics: {{
                    totalComponents: {stats['total_nodes']},
                    maxLevel: {stats['max_level']},
                    uniqueFELines: {stats['total_f_e_lines']},
                    flowConnections: {len(links)}
                }},
                metadata: {{
                    created: new Date().toISOString(),
                    source: "Voyager BOM Hierarchical Analysis",
                    description: "Enhanced Sankey flow diagram data"
                }}
            }};
            
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {{type: 'application/json'}});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'voyager_bom_enhanced_sankey_data.json';
            link.click();
        }}
        
        // Initial animation
        setTimeout(() => {{
            animateFlow();
        }}, 1500);
        
        // Add periodic subtle animation
        setInterval(() => {{
            if (Math.random() > 0.7) {{
                const randomLink = link.nodes()[Math.floor(Math.random() * link.size())];
                d3.select(randomLink)
                    .transition()
                    .duration(1000)
                    .style("stroke-opacity", 1)
                    .transition()
                    .duration(1000)
                    .style("stroke-opacity", 0.7);
            }}
        }}, 5000);
    </script>
</body>
</html>
"""
    
    return html_content

def main():
    """Main function"""
    print("🌊 CREATING ENHANCED REALISTIC SANKEY VISUALIZATION")
    print("=" * 60)
    
    html_content = create_enhanced_sankey_html()
    
    with open('output/voyager_bom_enhanced_realistic_sankey.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ Enhanced realistic Sankey visualization created!")
    print("📁 File: output/voyager_bom_enhanced_realistic_sankey.html")
    print("🎨 Features:")
    print("  - Realistic flow data based on actual BOM hierarchy")
    print("  - Interactive animations and controls")
    print("  - Enhanced visual styling with gradients and shadows")
    print("  - Detailed tooltips with comprehensive information")
    print("  - Export functionality for data analysis")

if __name__ == "__main__":
    main()
