#!/usr/bin/env python3
"""
Enhanced BOM-PCM Matching Algorithm
Implementing: Enhanced Embeddings, Hierarchical Matching, Ensemble Approach
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import jaccard_score
import re
from difflib import SequenceMatcher
import warnings
warnings.filterwarnings('ignore')

class EnhancedBOMPCMAlgorithm:
    def __init__(self):
        """Initialize enhanced algorithm"""
        self.bom_data = None
        self.pcm_data = None
        self.results_data = None
        
        # Multiple embedding models
        self.sbert_model = None
        self.clinical_model = None
        self.tfidf_vectorizer = None
        
        # Enhanced results
        self.enhanced_matches = []
        self.performance_comparison = {}
        
    def load_data(self):
        """Load data"""
        print("📊 LOADING DATA FOR ENHANCED ALGORITHM")
        print("=" * 50)
        
        try:
            self.results_data = pd.read_excel('output/voyager_complete_bom_pcm_table.xlsx', sheet_name='Complete_Table')
            self.pcm_data = pd.read_csv('output/real_voyager_pcm_codes.csv')
            self.bom_data = pd.read_excel('output/voyager_5408509_bom_pcm_aggregated.xlsx')
            
            print(f"✅ Data loaded successfully")
            print(f"   BOM items: {len(self.bom_data)}")
            print(f"   PCM codes: {len(self.pcm_data)}")
            
            return True
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    def load_enhanced_embedding_models(self):
        """Load multiple embedding models (Enhancement 4)"""
        print("\n🧠 LOADING ENHANCED EMBEDDING MODELS")
        print("=" * 50)
        
        # 1. Standard SBERT
        print("Loading SBERT model...")
        self.sbert_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # 2. Try to load clinical/biomedical model (fallback to SBERT if not available)
        print("Loading clinical/biomedical model...")
        try:
            # Try clinical model first
            self.clinical_model = SentenceTransformer('emilyalsentzer/Bio_ClinicalBERT')
            print("✅ Bio_ClinicalBERT loaded")
        except:
            try:
                # Fallback to biomedical model
                self.clinical_model = SentenceTransformer('dmis-lab/biobert-base-cased-v1.1')
                print("✅ BioBERT loaded")
            except:
                # Final fallback to SBERT
                self.clinical_model = self.sbert_model
                print("⚠️ Using SBERT as clinical model fallback")
        
        # 3. TF-IDF for lexical similarity
        print("Initializing TF-IDF vectorizer...")
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english',
            ngram_range=(1, 2),
            lowercase=True
        )
        
        print("✅ All embedding models loaded")
    
    def preprocess_with_domain_knowledge(self, text):
        """Enhanced preprocessing with medical device domain knowledge"""
        if pd.isna(text) or text == '':
            return ''
        
        text = str(text).lower()
        
        # Medical device specific normalizations
        domain_mappings = {
            'tdi': 'time domain imaging',
            '1.5t': '1.5 tesla',
            '3.0t': '3.0 tesla',
            'mr': 'magnetic resonance',
            'mri': 'magnetic resonance imaging',
            'rf': 'radio frequency',
            'coil': 'magnetic coil',
            'array': 'coil array',
            'phantom': 'test phantom',
            'gating': 'respiratory gating',
            'voyager': 'voyager system'
        }
        
        # Apply domain mappings
        for abbrev, full_form in domain_mappings.items():
            text = re.sub(r'\b' + abbrev + r'\b', full_form, text)
        
        # Remove punctuation but keep important separators
        text = re.sub(r'[^\w\s\-\.]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def hierarchical_filtering(self, bom_item, pcm_candidates):
        """Hierarchical matching with level and category awareness (Enhancement 5)"""
        
        # Level-aware filtering
        bom_level = bom_item.get('Level', 0)
        
        # Define level compatibility rules
        level_compatible_pcm = []
        
        for pcm_item in pcm_candidates:
            # Rule 1: High-level BOM items (0-2) can match any PCM
            if bom_level <= 2:
                level_compatible_pcm.append(pcm_item)
            
            # Rule 2: Mid-level BOM items (3-5) prefer specific PCM categories
            elif 3 <= bom_level <= 5:
                pcm_desc = str(pcm_item.get('description', '')).lower()
                if any(keyword in pcm_desc for keyword in ['system', 'controller', 'interface', 'board']):
                    level_compatible_pcm.append(pcm_item)
            
            # Rule 3: Low-level BOM items (6+) prefer component-level PCM
            else:
                pcm_desc = str(pcm_item.get('description', '')).lower()
                if any(keyword in pcm_desc for keyword in ['cable', 'connector', 'module', 'component']):
                    level_compatible_pcm.append(pcm_item)
        
        # Category-based filtering
        bom_desc = str(bom_item.get('Item_Description', '')).lower()
        
        # Define category rules
        category_filtered_pcm = []
        
        for pcm_item in level_compatible_pcm:
            pcm_desc = str(pcm_item.get('description', '')).lower()
            
            # Coil/Array matching
            if any(keyword in bom_desc for keyword in ['coil', 'array']):
                if any(keyword in pcm_desc for keyword in ['coil', 'array', 'head', 'neck']):
                    category_filtered_pcm.append(pcm_item)
            
            # System/Controller matching
            elif any(keyword in bom_desc for keyword in ['system', 'controller']):
                if any(keyword in pcm_desc for keyword in ['system', 'controller', 'upgrade']):
                    category_filtered_pcm.append(pcm_item)
            
            # Table/Mechanical matching
            elif any(keyword in bom_desc for keyword in ['table', 'mechanical']):
                if any(keyword in pcm_desc for keyword in ['table', 'mechanical', 'config']):
                    category_filtered_pcm.append(pcm_item)
            
            # Default: include if no specific category
            else:
                category_filtered_pcm.append(pcm_item)
        
        # If filtering is too restrictive, return original candidates
        if len(category_filtered_pcm) == 0:
            return level_compatible_pcm if len(level_compatible_pcm) > 0 else pcm_candidates
        
        return category_filtered_pcm
    
    def calculate_ensemble_similarity(self, bom_desc, pcm_desc, bom_item, pcm_item):
        """Ensemble approach with multiple similarity metrics (Enhancement 6)"""
        
        # Preprocess descriptions
        bom_processed = self.preprocess_with_domain_knowledge(bom_desc)
        pcm_processed = self.preprocess_with_domain_knowledge(pcm_desc)
        
        similarities = {}
        
        # 1. Semantic similarity (SBERT)
        try:
            bom_embedding = self.sbert_model.encode([bom_processed])
            pcm_embedding = self.sbert_model.encode([pcm_processed])
            semantic_sim = cosine_similarity(bom_embedding, pcm_embedding)[0][0]
            similarities['semantic_sbert'] = semantic_sim
        except:
            similarities['semantic_sbert'] = 0.0
        
        # 2. Clinical/Biomedical similarity
        try:
            bom_clinical = self.clinical_model.encode([bom_processed])
            pcm_clinical = self.clinical_model.encode([pcm_processed])
            clinical_sim = cosine_similarity(bom_clinical, pcm_clinical)[0][0]
            similarities['semantic_clinical'] = clinical_sim
        except:
            similarities['semantic_clinical'] = similarities['semantic_sbert']
        
        # 3. Lexical similarity (Jaccard)
        bom_words = set(bom_processed.split())
        pcm_words = set(pcm_processed.split())
        
        if len(bom_words) > 0 and len(pcm_words) > 0:
            jaccard_sim = len(bom_words.intersection(pcm_words)) / len(bom_words.union(pcm_words))
        else:
            jaccard_sim = 0.0
        similarities['lexical_jaccard'] = jaccard_sim
        
        # 4. String similarity (Levenshtein-based)
        sequence_sim = SequenceMatcher(None, bom_processed, pcm_processed).ratio()
        similarities['string_sequence'] = sequence_sim
        
        # 5. Code pattern matching
        bom_codes = re.findall(r'[A-Z]\d{4,6}[A-Z]{0,2}', str(bom_item.get('Item_ID', '')))
        pcm_codes = re.findall(r'[A-Z]\d{4,6}[A-Z]{0,2}', str(pcm_item.get('code', '')))
        
        code_sim = 0.0
        if bom_codes and pcm_codes:
            for bom_code in bom_codes:
                for pcm_code in pcm_codes:
                    # Check prefix similarity
                    if bom_code[:2] == pcm_code[:2]:
                        code_sim = max(code_sim, 0.8)
                    elif bom_code[:1] == pcm_code[:1]:
                        code_sim = max(code_sim, 0.4)
        
        similarities['code_pattern'] = code_sim
        
        # 6. Weighted ensemble score
        weights = {
            'semantic_sbert': 0.3,
            'semantic_clinical': 0.25,
            'lexical_jaccard': 0.2,
            'string_sequence': 0.1,
            'code_pattern': 0.15
        }
        
        ensemble_score = sum(similarities[metric] * weights[metric] for metric in weights.keys())
        similarities['ensemble_score'] = ensemble_score
        
        return similarities
    
    def enhanced_matching_algorithm(self, threshold=0.7):
        """Run enhanced matching algorithm"""
        print(f"\n🚀 RUNNING ENHANCED MATCHING ALGORITHM")
        print("=" * 60)
        print(f"Threshold: {threshold}")
        print("Enhancements: Domain Embeddings + Hierarchical + Ensemble")
        
        self.load_enhanced_embedding_models()
        
        enhanced_matches = []
        
        # Convert PCM data to list for easier processing
        pcm_candidates = self.pcm_data.to_dict('records')
        
        print(f"\nProcessing {len(self.results_data)} BOM items...")
        
        for idx, bom_row in self.results_data.iterrows():
            if idx % 5000 == 0:
                print(f"  Processed {idx}/{len(self.results_data)} items...")
            
            bom_item = bom_row.to_dict()
            bom_desc = str(bom_row['Item_Description'])
            
            # Apply hierarchical filtering
            filtered_pcm = self.hierarchical_filtering(bom_item, pcm_candidates)
            
            best_match = None
            best_score = 0.0
            best_similarities = {}
            
            # Calculate ensemble similarity for each filtered PCM candidate
            for pcm_item in filtered_pcm:
                pcm_desc = str(pcm_item.get('description', ''))
                
                similarities = self.calculate_ensemble_similarity(
                    bom_desc, pcm_desc, bom_item, pcm_item
                )
                
                ensemble_score = similarities['ensemble_score']
                
                if ensemble_score > best_score:
                    best_score = ensemble_score
                    best_match = pcm_item
                    best_similarities = similarities
            
            # Apply threshold
            if best_score >= threshold and best_match is not None:
                match_result = {
                    'bom_item_id': bom_item['Item_ID'],
                    'bom_description': bom_desc,
                    'bom_level': bom_item.get('Level', 0),
                    'pcm_code': best_match['code'],
                    'pcm_description': best_match['description'],
                    'ensemble_score': best_score,
                    'semantic_sbert': best_similarities['semantic_sbert'],
                    'semantic_clinical': best_similarities['semantic_clinical'],
                    'lexical_jaccard': best_similarities['lexical_jaccard'],
                    'string_sequence': best_similarities['string_sequence'],
                    'code_pattern': best_similarities['code_pattern'],
                    'hierarchical_filtered': len(filtered_pcm) < len(pcm_candidates)
                }
                enhanced_matches.append(match_result)
            else:
                # No match above threshold
                match_result = {
                    'bom_item_id': bom_item['Item_ID'],
                    'bom_description': bom_desc,
                    'bom_level': bom_item.get('Level', 0),
                    'pcm_code': '',
                    'pcm_description': '',
                    'ensemble_score': 0.0,
                    'semantic_sbert': 0.0,
                    'semantic_clinical': 0.0,
                    'lexical_jaccard': 0.0,
                    'string_sequence': 0.0,
                    'code_pattern': 0.0,
                    'hierarchical_filtered': False
                }
                enhanced_matches.append(match_result)
        
        self.enhanced_matches = enhanced_matches
        
        # Calculate performance metrics
        matched_items = len([m for m in enhanced_matches if m['ensemble_score'] > 0])
        coverage_rate = (matched_items / len(enhanced_matches)) * 100
        avg_score = np.mean([m['ensemble_score'] for m in enhanced_matches if m['ensemble_score'] > 0])
        
        print(f"\n✅ Enhanced matching completed!")
        print(f"   Total items: {len(enhanced_matches)}")
        print(f"   Matched items: {matched_items}")
        print(f"   Coverage rate: {coverage_rate:.1f}%")
        print(f"   Average ensemble score: {avg_score:.3f}")
        
        return enhanced_matches
    
    def compare_with_original(self):
        """Compare enhanced algorithm with original"""
        print(f"\n📊 COMPARING ENHANCED VS ORIGINAL ALGORITHM")
        print("=" * 60)
        
        # Original algorithm results
        original_matched = len(self.results_data[self.results_data['Item_ID_PCM'] != ''])
        original_coverage = (original_matched / len(self.results_data)) * 100
        original_avg_sim = self.results_data[self.results_data['Similarity_Score'] > 0]['Similarity_Score'].mean()
        
        # Enhanced algorithm results
        enhanced_matched = len([m for m in self.enhanced_matches if m['ensemble_score'] > 0])
        enhanced_coverage = (enhanced_matched / len(self.enhanced_matches)) * 100
        enhanced_avg_score = np.mean([m['ensemble_score'] for m in self.enhanced_matches if m['ensemble_score'] > 0])
        
        # High-quality matches (>0.8)
        original_high_quality = len(self.results_data[self.results_data['Similarity_Score'] > 0.8])
        enhanced_high_quality = len([m for m in self.enhanced_matches if m['ensemble_score'] > 0.8])
        
        comparison = {
            'original': {
                'matched_items': original_matched,
                'coverage_rate': original_coverage,
                'avg_similarity': original_avg_sim,
                'high_quality_matches': original_high_quality
            },
            'enhanced': {
                'matched_items': enhanced_matched,
                'coverage_rate': enhanced_coverage,
                'avg_similarity': enhanced_avg_score,
                'high_quality_matches': enhanced_high_quality
            }
        }
        
        print(f"📈 PERFORMANCE COMPARISON:")
        print(f"                    Original    Enhanced    Improvement")
        print(f"Matched Items:      {original_matched:8,}    {enhanced_matched:8,}    {((enhanced_matched/original_matched-1)*100):+6.1f}%")
        print(f"Coverage Rate:      {original_coverage:8.1f}%   {enhanced_coverage:8.1f}%   {(enhanced_coverage-original_coverage):+6.1f}pp")
        print(f"Avg Similarity:     {original_avg_sim:8.3f}    {enhanced_avg_score:8.3f}    {((enhanced_avg_score/original_avg_sim-1)*100):+6.1f}%")
        print(f"High Quality (>0.8): {original_high_quality:7,}    {enhanced_high_quality:8,}    {((enhanced_high_quality/max(original_high_quality,1)-1)*100):+6.1f}%")
        
        self.performance_comparison = comparison
        return comparison
    
    def create_enhanced_visualizations(self):
        """Create visualizations for enhanced algorithm"""
        print(f"\n📊 CREATING ENHANCED ALGORITHM VISUALIZATIONS")
        print("=" * 60)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Enhanced Algorithm Performance Analysis', fontsize=16, fontweight='bold')
        
        # 1. Score distribution comparison
        original_scores = self.results_data[self.results_data['Similarity_Score'] > 0]['Similarity_Score']
        enhanced_scores = [m['ensemble_score'] for m in self.enhanced_matches if m['ensemble_score'] > 0]
        
        ax1.hist(original_scores, bins=20, alpha=0.7, label='Original', color='lightcoral')
        ax1.hist(enhanced_scores, bins=20, alpha=0.7, label='Enhanced', color='lightblue')
        ax1.set_title('Score Distribution Comparison', fontweight='bold')
        ax1.set_xlabel('Similarity Score')
        ax1.set_ylabel('Frequency')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. Coverage comparison
        comparison = self.performance_comparison
        categories = ['Original', 'Enhanced']
        coverage_values = [comparison['original']['coverage_rate'], comparison['enhanced']['coverage_rate']]
        
        bars = ax2.bar(categories, coverage_values, color=['lightcoral', 'lightblue'])
        ax2.set_title('Coverage Rate Comparison', fontweight='bold')
        ax2.set_ylabel('Coverage Rate (%)')
        
        for bar, value in zip(bars, coverage_values):
            ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 1,
                    f'{value:.1f}%', ha='center', va='bottom')
        
        # 3. Quality distribution
        quality_ranges = ['0.6-0.7', '0.7-0.8', '0.8-0.9', '0.9-1.0']
        
        # Original quality distribution
        orig_quality = [
            len(self.results_data[(self.results_data['Similarity_Score'] >= 0.6) & (self.results_data['Similarity_Score'] < 0.7)]),
            len(self.results_data[(self.results_data['Similarity_Score'] >= 0.7) & (self.results_data['Similarity_Score'] < 0.8)]),
            len(self.results_data[(self.results_data['Similarity_Score'] >= 0.8) & (self.results_data['Similarity_Score'] < 0.9)]),
            len(self.results_data[self.results_data['Similarity_Score'] >= 0.9])
        ]
        
        # Enhanced quality distribution
        enh_quality = [
            len([m for m in self.enhanced_matches if 0.6 <= m['ensemble_score'] < 0.7]),
            len([m for m in self.enhanced_matches if 0.7 <= m['ensemble_score'] < 0.8]),
            len([m for m in self.enhanced_matches if 0.8 <= m['ensemble_score'] < 0.9]),
            len([m for m in self.enhanced_matches if m['ensemble_score'] >= 0.9])
        ]
        
        x = np.arange(len(quality_ranges))
        width = 0.35
        
        ax3.bar(x - width/2, orig_quality, width, label='Original', color='lightcoral')
        ax3.bar(x + width/2, enh_quality, width, label='Enhanced', color='lightblue')
        ax3.set_title('Quality Distribution Comparison', fontweight='bold')
        ax3.set_xlabel('Quality Range')
        ax3.set_ylabel('Number of Matches')
        ax3.set_xticks(x)
        ax3.set_xticklabels(quality_ranges)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. Ensemble components contribution
        if enhanced_scores:
            ensemble_components = ['SBERT', 'Clinical', 'Jaccard', 'Sequence', 'Code Pattern']
            avg_contributions = [
                np.mean([m['semantic_sbert'] for m in self.enhanced_matches if m['ensemble_score'] > 0]),
                np.mean([m['semantic_clinical'] for m in self.enhanced_matches if m['ensemble_score'] > 0]),
                np.mean([m['lexical_jaccard'] for m in self.enhanced_matches if m['ensemble_score'] > 0]),
                np.mean([m['string_sequence'] for m in self.enhanced_matches if m['ensemble_score'] > 0]),
                np.mean([m['code_pattern'] for m in self.enhanced_matches if m['ensemble_score'] > 0])
            ]
            
            bars = ax4.bar(ensemble_components, avg_contributions, color='lightgreen')
            ax4.set_title('Average Contribution by Ensemble Component', fontweight='bold')
            ax4.set_ylabel('Average Score')
            ax4.tick_params(axis='x', rotation=45)
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('output/enhanced_algorithm_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ Enhanced visualizations saved: enhanced_algorithm_analysis.png")
    
    def export_enhanced_results(self):
        """Export enhanced results"""
        print(f"\n💾 EXPORTING ENHANCED RESULTS")
        print("=" * 40)
        
        # Convert to DataFrame
        enhanced_df = pd.DataFrame(self.enhanced_matches)
        
        # Export to Excel
        with pd.ExcelWriter('output/enhanced_algorithm_results.xlsx', engine='openpyxl') as writer:
            enhanced_df.to_excel(writer, sheet_name='Enhanced_Matches', index=False)
            
            # High quality matches
            high_quality = enhanced_df[enhanced_df['ensemble_score'] > 0.8]
            high_quality.to_excel(writer, sheet_name='High_Quality_Matches', index=False)
            
            # Comparison summary
            comparison_df = pd.DataFrame(self.performance_comparison).T
            comparison_df.to_excel(writer, sheet_name='Performance_Comparison')
        
        print(f"✅ Enhanced results exported: output/enhanced_algorithm_results.xlsx")

def main():
    """Main execution"""
    print("🚀 ENHANCED BOM-PCM MATCHING ALGORITHM")
    print("=" * 70)
    print("Implementing: Enhanced Embeddings + Hierarchical + Ensemble")
    print("")
    
    algorithm = EnhancedBOMPCMAlgorithm()
    
    if not algorithm.load_data():
        return None
    
    # Run enhanced algorithm
    enhanced_matches = algorithm.enhanced_matching_algorithm(threshold=0.7)
    
    # Compare with original
    comparison = algorithm.compare_with_original()
    
    # Create visualizations
    algorithm.create_enhanced_visualizations()
    
    # Export results
    algorithm.export_enhanced_results()
    
    print(f"\n✅ ENHANCED ALGORITHM ANALYSIS COMPLETE")
    print(f"📊 Results saved in output/enhanced_algorithm_results.xlsx")
    print(f"📈 Visualizations saved in output/enhanced_algorithm_analysis.png")
    
    return algorithm

if __name__ == "__main__":
    algorithm = main()
