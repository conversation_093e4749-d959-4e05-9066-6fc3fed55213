import torch
from transformers import AutoTokenizer, AutoModel
from PIL import Image
import fitz  # PyMuPDF
import numpy as np
from typing import List, Dict, Tuple
import re
import json

class ColPaliMachinePartsRetriever:
    def __init__(self, model_name: str = "vidore/colpali-v1.2"):
        """
        Initialise le système de retrieval ColPali pour les pièces de machine
        """
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # Essayer de charger le modèle ColPali, avec fallback en cas d'échec
        try:
            print(f"Tentative de chargement du modèle ColPali: {model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModel.from_pretrained(model_name).to(self.device)
            self.model.eval()
            self.model_loaded = True
            print("✅ Modèle ColPali chargé avec succès")
        except Exception as e:
            print(f"⚠️ Impossible de charger le modèle ColPali: {e}")
            print("⚠️ Utilisation du mode fallback (extraction basique)")
            self.model_loaded = False
            self.tokenizer = None
            self.model = None

        # Patterns pour identifier les pièces de machine (français + anglais)
        self.part_patterns = [
            r'(?:pièce|piece|part|component|assembly|unit|module)\s*(?:n[°o]?\s*)?(\d+)',
            r'(?:ref|référence|reference|part\s*number|p/n|pn)\s*[:\-]?\s*([A-Z0-9\-]+)',
            r'(?:code|model|modèle|serial|s/n)\s*[:\-]?\s*([A-Z0-9\-]+)',
            r'(?:article|art|item|sku)\s*[:\-]?\s*([A-Z0-9\-]+)',
            r'[A-Z]\d{4,6}[A-Z]{0,2}',  # Pattern pour codes comme M7010FC, R4390JA
            r'\b\d{6,10}\b',  # Codes numériques longs
            r'[A-Z]{2,4}-\d{3,6}',  # Patterns comme ABC-1234
        ]

        # Mots-clés techniques (français + anglais)
        self.technical_keywords = [
            # Français
            'dimensions', 'poids', 'matériau', 'matière', 'specifications',
            'caractéristiques', 'installation', 'montage', 'assemblage',
            'maintenance', 'remplacement', 'durée de vie', 'température',
            'pression', 'débit', 'puissance', 'tension', 'courant',
            # Anglais
            'dimensions', 'weight', 'material', 'specifications', 'specs',
            'characteristics', 'installation', 'mounting', 'assembly',
            'maintenance', 'replacement', 'lifetime', 'temperature',
            'pressure', 'flow', 'power', 'voltage', 'current',
            'coil', 'magnet', 'gradient', 'shim', 'rf', 'transmit',
            'receive', 'cable', 'connector', 'board', 'module',
            'system', 'subsystem', 'interface', 'controller',
            'upgrade', 'option', 'accessory', 'phantom', 'test'
        ]

        # Vocabulaire technique accumulé
        self.technical_vocabulary = set()
        self.extracted_terms = []

    def pdf_to_images(self, pdf_path: str) -> List[Tuple[Image.Image, int]]:
        """
        Convertit chaque page du PDF en image
        """
        doc = fitz.open(pdf_path)
        images = []
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            mat = fitz.Matrix(2.0, 2.0)  # Zoom x2 pour meilleure qualité
            pix = page.get_pixmap(matrix=mat)
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            images.append((img, page_num))
        
        doc.close()
        return images

    def extract_text_from_page(self, pdf_path: str, page_num: int) -> str:
        """
        Extrait le texte d'une page spécifique du PDF
        """
        doc = fitz.open(pdf_path)
        page = doc.load_page(page_num)
        text = page.get_text()
        doc.close()
        return text

    def encode_image(self, image: Image.Image) -> torch.Tensor:
        """
        Encode une image avec ColPali (ou fallback si modèle non disponible)
        """
        if not self.model_loaded:
            # Mode fallback: retourner un tensor vide
            return torch.zeros(1, 768).to(self.device)

        # Preprocessing de l'image
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Redimensionnement si nécessaire
        if image.size[0] > 1024 or image.size[1] > 1024:
            image.thumbnail((1024, 1024), Image.Resampling.LANCZOS)

        try:
            # Conversion en tensor et normalisation
            image_array = np.array(image)
            image_tensor = torch.from_numpy(image_array).float().permute(2, 0, 1).unsqueeze(0)
            image_tensor = image_tensor.to(self.device) / 255.0

            with torch.no_grad():
                # Utilisation du modèle pour encoder l'image
                outputs = self.model(pixel_values=image_tensor)
                embeddings = outputs.last_hidden_state.mean(dim=1)

            return embeddings
        except Exception as e:
            print(f"⚠️ Erreur d'encodage image: {e}")
            return torch.zeros(1, 768).to(self.device)

    def encode_query(self, query: str) -> torch.Tensor:
        """
        Encode une requête textuelle (ou fallback si modèle non disponible)
        """
        if not self.model_loaded:
            # Mode fallback: retourner un tensor vide
            return torch.zeros(1, 768).to(self.device)

        try:
            inputs = self.tokenizer(query, return_tensors="pt",
                                   padding=True, truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            with torch.no_grad():
                outputs = self.model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1)

            return embeddings
        except Exception as e:
            print(f"⚠️ Erreur d'encodage requête: {e}")
            return torch.zeros(1, 768).to(self.device)

    def find_machine_parts(self, text: str) -> List[Dict]:
        """
        Identifie les pièces de machine dans le texte
        """
        parts = []
        lines = text.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # Recherche des patterns de pièces
            for pattern in self.part_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    part_id = match.group(1)
                    
                    # Extraction du contexte (lignes précédentes et suivantes)
                    context_start = max(0, i - 2)
                    context_end = min(len(lines), i + 3)
                    context = '\n'.join(lines[context_start:context_end])
                    
                    # Extraction des données techniques
                    technical_data = self.extract_technical_data(context)
                    
                    part_info = {
                        'id': part_id,
                        'line': line,
                        'context': context,
                        'technical_data': technical_data,
                        'line_number': i
                    }
                    parts.append(part_info)
        
        return parts

    def extract_technical_data(self, text: str) -> Dict:
        """
        Extrait les données techniques d'un texte
        """
        technical_data = {}
        
        # Patterns pour différents types de données
        patterns = {
            'dimensions': r'(?:dimensions?|taille|size)\s*:?\s*([0-9.,x×\s]+(?:mm|cm|m|inch|in))',
            'poids': r'(?:poids|weight|masse)\s*:?\s*([0-9.,]+\s*(?:kg|g|lb))',
            'matériau': r'(?:matériau|matière|material)\s*:?\s*([a-zA-Z\s]+)',
            'température': r'(?:température|temperature|temp)\s*:?\s*([0-9.,\-+]+\s*°?[CF]?)',
            'pression': r'(?:pression|pressure)\s*:?\s*([0-9.,]+\s*(?:bar|psi|Pa))',
            'débit': r'(?:débit|flow)\s*:?\s*([0-9.,]+\s*(?:l/min|m³/h|gpm))',
            'puissance': r'(?:puissance|power)\s*:?\s*([0-9.,]+\s*(?:W|kW|HP))',
            'tension': r'(?:tension|voltage)\s*:?\s*([0-9.,]+\s*V)',
            'courant': r'(?:courant|current)\s*:?\s*([0-9.,]+\s*A)'
        }
        
        for key, pattern in patterns.items():
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                technical_data[key] = match.group(1).strip()
        
        return technical_data

    def calculate_text_similarity(self, text: str, query: str) -> float:
        """
        Calcule la similarité textuelle simple (fallback quand ColPali n'est pas disponible)
        """
        text_lower = text.lower()
        query_lower = query.lower()

        # Mots de la requête
        query_words = set(query_lower.split())
        text_words = set(text_lower.split())

        if not query_words or not text_words:
            return 0.0

        # Similarité Jaccard
        intersection = len(query_words.intersection(text_words))
        union = len(query_words.union(text_words))
        jaccard_sim = intersection / union if union > 0 else 0.0

        # Bonus pour correspondance exacte
        exact_match_bonus = 0.5 if query_lower in text_lower else 0.0

        return min(1.0, jaccard_sim + exact_match_bonus)

    def extract_all_technical_vocabulary(self, pdf_path: str) -> Dict:
        """
        Extrait TOUT le vocabulaire technique du document pour créer un socle
        """
        print("🔍 EXTRACTION COMPLÈTE DU VOCABULAIRE TECHNIQUE")
        print("=" * 60)

        doc = fitz.open(pdf_path)
        all_text = ""
        page_contents = []

        # Extraire tout le texte
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            all_text += text + "\n"
            page_contents.append({
                'page_number': page_num + 1,
                'text': text,
                'word_count': len(text.split())
            })

        doc.close()

        # Analyser le vocabulaire
        vocabulary_analysis = self.analyze_technical_vocabulary(all_text)

        # Extraire les termes techniques par catégorie
        technical_terms = self.categorize_technical_terms(all_text)

        # Extraire tous les codes/références
        all_codes = self.extract_all_codes(all_text)

        # Créer le socle de vocabulaire
        vocabulary_base = {
            'document_info': {
                'total_pages': len(page_contents),
                'total_words': len(all_text.split()),
                'unique_words': len(set(all_text.lower().split()))
            },
            'page_contents': page_contents,
            'vocabulary_analysis': vocabulary_analysis,
            'technical_terms': technical_terms,
            'all_codes': all_codes,
            'full_text': all_text
        }

        # Sauvegarder le socle
        self.save_vocabulary_base(vocabulary_base)

        return vocabulary_base

    def analyze_technical_vocabulary(self, text: str) -> Dict:
        """
        Analyse le vocabulaire technique du document
        """
        words = text.lower().split()
        word_freq = {}

        # Compter la fréquence des mots
        for word in words:
            # Nettoyer le mot
            clean_word = re.sub(r'[^\w]', '', word)
            if len(clean_word) > 2:  # Ignorer les mots trop courts
                word_freq[clean_word] = word_freq.get(clean_word, 0) + 1

        # Trier par fréquence
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)

        # Identifier les termes techniques
        technical_words = []
        for word, freq in sorted_words:
            if (word in self.technical_keywords or
                len(word) > 6 or  # Mots longs souvent techniques
                re.match(r'[a-z]+ing$', word) or  # Mots en -ing
                re.match(r'[a-z]+tion$', word) or  # Mots en -tion
                re.match(r'[a-z]+ment$', word)):  # Mots en -ment
                technical_words.append({'word': word, 'frequency': freq})

        return {
            'total_unique_words': len(word_freq),
            'most_frequent': sorted_words[:50],
            'technical_words': technical_words[:100],
            'word_frequency': word_freq
        }

    def categorize_technical_terms(self, text: str) -> Dict:
        """
        Catégorise les termes techniques par domaine
        """
        categories = {
            'medical_imaging': ['mri', 'magnetic', 'resonance', 'imaging', 'scan', 'patient', 'clinical'],
            'hardware': ['coil', 'magnet', 'gradient', 'shim', 'cable', 'connector', 'board'],
            'software': ['software', 'program', 'application', 'interface', 'system', 'control'],
            'maintenance': ['maintenance', 'service', 'repair', 'replacement', 'install', 'remove'],
            'specifications': ['specification', 'dimension', 'weight', 'temperature', 'pressure', 'voltage'],
            'safety': ['safety', 'warning', 'caution', 'danger', 'hazard', 'protection'],
            'procedures': ['procedure', 'step', 'instruction', 'operation', 'process', 'method']
        }

        categorized_terms = {}
        text_lower = text.lower()

        for category, keywords in categories.items():
            found_terms = []
            for keyword in keywords:
                # Chercher le mot et ses variations
                pattern = r'\b' + keyword + r'\w*\b'
                matches = re.findall(pattern, text_lower)
                if matches:
                    found_terms.extend(matches)

            # Compter les occurrences
            term_counts = {}
            for term in found_terms:
                term_counts[term] = term_counts.get(term, 0) + 1

            categorized_terms[category] = {
                'terms': list(set(found_terms)),
                'counts': term_counts,
                'total_occurrences': len(found_terms)
            }

        return categorized_terms

    def extract_all_codes(self, text: str) -> List[Dict]:
        """
        Extrait tous les codes/références du document
        """
        all_codes = []
        lines = text.split('\n')

        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # Appliquer tous les patterns
            for pattern in self.part_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    code_info = {
                        'code': match.group(0),
                        'line_number': line_num + 1,
                        'line_text': line,
                        'pattern_used': pattern,
                        'context': self.get_context(lines, line_num, 2)
                    }
                    all_codes.append(code_info)

        return all_codes

    def get_context(self, lines: List[str], line_num: int, context_size: int = 2) -> str:
        """
        Obtient le contexte autour d'une ligne
        """
        start = max(0, line_num - context_size)
        end = min(len(lines), line_num + context_size + 1)
        return '\n'.join(lines[start:end])

    def save_vocabulary_base(self, vocabulary_base: Dict):
        """
        Sauvegarde le socle de vocabulaire
        """
        import json
        import pandas as pd

        # Sauvegarder en JSON
        json_file = 'output/technical_vocabulary_base.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            # Convertir les sets en listes pour JSON
            vocab_copy = vocabulary_base.copy()
            json.dump(vocab_copy, f, indent=2, ensure_ascii=False, default=str)

        # Sauvegarder les termes techniques en CSV
        if vocabulary_base['technical_terms']:
            terms_data = []
            for category, data in vocabulary_base['technical_terms'].items():
                for term, count in data['counts'].items():
                    terms_data.append({
                        'category': category,
                        'term': term,
                        'count': count
                    })

            terms_df = pd.DataFrame(terms_data)
            terms_df.to_csv('output/technical_terms_by_category.csv', index=False)

        # Sauvegarder tous les codes en CSV
        if vocabulary_base['all_codes']:
            codes_df = pd.DataFrame(vocabulary_base['all_codes'])
            codes_df.to_csv('output/extracted_codes_all.csv', index=False)

        print(f"✅ Socle de vocabulaire sauvegardé:")
        print(f"   - JSON: {json_file}")
        print(f"   - Termes: output/technical_terms_by_category.csv")
        print(f"   - Codes: output/extracted_codes_all.csv")

    def retrieve_parts_data(self, pdf_path: str, query: str = None) -> List[Dict]:
        """
        Récupère toutes les pièces et leurs données depuis le PDF
        """
        print("Conversion du PDF en images...")
        images = self.pdf_to_images(pdf_path)
        
        all_parts = []
        
        for image, page_num in images:
            print(f"Traitement de la page {page_num + 1}...")
            
            # Extraction du texte de la page
            text = self.extract_text_from_page(pdf_path, page_num)
            
            # Recherche des pièces dans le texte
            parts = self.find_machine_parts(text)
            
            # Ajout des informations de page
            for part in parts:
                part['page_number'] = page_num + 1
                part['image_size'] = image.size
                
                # Si une requête est fournie, calcul de la similarité
                if query:
                    if self.model_loaded:
                        image_embedding = self.encode_image(image)
                        query_embedding = self.encode_query(query)

                        # Calcul de la similarité cosinus
                        similarity = torch.cosine_similarity(
                            image_embedding, query_embedding
                        ).item()
                        part['similarity_score'] = similarity
                    else:
                        # Mode fallback: similarité basée sur le texte
                        text_similarity = self.calculate_text_similarity(part['line'] + ' ' + part['context'], query)
                        part['similarity_score'] = text_similarity
                
                all_parts.append(part)
        
        # Tri par score de similarité si une requête est fournie
        if query:
            all_parts.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)
        
        return all_parts

    def search_specific_part(self, pdf_path: str, part_query: str) -> List[Dict]:
        """
        Recherche une pièce spécifique
        """
        print(f"Recherche de: {part_query}")
        parts = self.retrieve_parts_data(pdf_path, part_query)
        
        # Filtrage par pertinence
        relevant_parts = []
        threshold = 0.7 if self.model_loaded else 0.3  # Seuil plus bas en mode fallback

        for part in parts:
            if (part.get('similarity_score', 0) > threshold or
                part_query.lower() in part['line'].lower() or
                part_query.lower() in part['context'].lower()):
                relevant_parts.append(part)
        
        return relevant_parts

    def export_parts_data(self, parts: List[Dict], output_file: str = "machine_parts.json"):
        """
        Exporte les données des pièces vers un fichier JSON
        """
        # Conversion des tensors en valeurs pour la sérialisation
        exportable_parts = []
        for part in parts:
            exportable_part = {k: v for k, v in part.items() 
                             if not isinstance(v, torch.Tensor)}
            exportable_parts.append(exportable_part)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(exportable_parts, f, ensure_ascii=False, indent=2)
        
        print(f"Données exportées vers {output_file}")

    def generate_parts_summary(self, parts: List[Dict]) -> str:
        """
        Génère un résumé des pièces trouvées
        """
        summary = f"=== RÉSUMÉ DES PIÈCES DE MACHINE ===\n\n"
        summary += f"Nombre total de pièces trouvées: {len(parts)}\n\n"
        
        for i, part in enumerate(parts, 1):
            summary += f"PIÈCE {i}:\n"
            summary += f"  ID: {part['id']}\n"
            summary += f"  Page: {part['page_number']}\n"
            summary += f"  Ligne: {part['line']}\n"
            
            if part['technical_data']:
                summary += f"  Données techniques:\n"
                for key, value in part['technical_data'].items():
                    summary += f"    - {key}: {value}\n"
            
            if part.get('similarity_score'):
                summary += f"  Score de similarité: {part['similarity_score']:.3f}\n"
            
            summary += f"  Contexte:\n{part['context']}\n"
            summary += "-" * 50 + "\n\n"
        
        return summary

# Exemple d'utilisation
def main():
    # Initialisation du système
    retriever = ColPaliMachinePartsRetriever()
    
    # Chemin vers le manuel PDF
    pdf_path = r"input\PIM\3.0T Signa PET MR.pdf"
    
    try:
        # ÉTAPE 1: Extraction complète du vocabulaire technique (SOCLE)
        print("🔍 ÉTAPE 1: Création du socle de vocabulaire technique")
        vocabulary_base = retriever.extract_all_technical_vocabulary(pdf_path)

        print(f"\n📊 RÉSUMÉ DU SOCLE CRÉÉ:")
        print(f"   📄 Pages analysées: {vocabulary_base['document_info']['total_pages']}")
        print(f"   📝 Mots totaux: {vocabulary_base['document_info']['total_words']:,}")
        print(f"   🔤 Mots uniques: {vocabulary_base['document_info']['unique_words']:,}")
        print(f"   🔧 Termes techniques: {len(vocabulary_base['vocabulary_analysis']['technical_words'])}")
        print(f"   🏷️ Codes extraits: {len(vocabulary_base['all_codes'])}")

        # Afficher les catégories trouvées
        print(f"\n📋 CATÉGORIES TECHNIQUES IDENTIFIÉES:")
        for category, data in vocabulary_base['technical_terms'].items():
            if data['total_occurrences'] > 0:
                print(f"   {category}: {data['total_occurrences']} occurrences, {len(data['terms'])} termes uniques")

        # Afficher les termes les plus fréquents
        print(f"\n🔝 TERMES TECHNIQUES LES PLUS FRÉQUENTS:")
        for i, term_data in enumerate(vocabulary_base['vocabulary_analysis']['technical_words'][:10]):
            print(f"   {i+1}. {term_data['word']} ({term_data['frequency']} fois)")

        # Afficher quelques codes trouvés
        print(f"\n🏷️ EXEMPLES DE CODES EXTRAITS:")
        for i, code in enumerate(vocabulary_base['all_codes'][:10]):
            print(f"   {i+1}. {code['code']} (ligne {code['line_number']})")

        # ÉTAPE 2: Test de recherche avec le socle
        print(f"\n🔍 ÉTAPE 2: Test de recherche avec le socle créé")

        # Recherches test en anglais
        test_queries = [
            "coil",
            "magnet",
            "gradient",
            "cable",
            "connector",
            "maintenance",
            "installation",
            "system"
        ]

        for query in test_queries:
            print(f"\n🔎 Test recherche: '{query}'")
            specific_parts = retriever.search_specific_part(pdf_path, query)

            if specific_parts:
                print(f"   ✅ Trouvé {len(specific_parts)} résultat(s)")
                # Afficher les 3 meilleurs résultats
                for i, part in enumerate(specific_parts[:3]):
                    score = part.get('similarity_score', 0)
                    print(f"   {i+1}. Score: {score:.3f} | Page {part['page_number']}")
                    print(f"      {part['line'][:100]}...")
            else:
                print(f"   ❌ Aucun résultat")

        print(f"\n✅ SOCLE DE VOCABULAIRE TECHNIQUE CRÉÉ ET TESTÉ")
        print(f"📁 Fichiers générés dans output/:")
        print(f"   - technical_vocabulary_base.json")
        print(f"   - technical_terms_by_category.csv")
        print(f"   - extracted_codes_all.csv")
        print(f"\n🚀 Ce socle peut maintenant être utilisé pour analyser d'autres documents!")

    except Exception as e:
        print(f"Erreur: {e}")
        print("Assurez-vous que le fichier PDF existe et que les dépendances sont installées.")

if __name__ == "__main__":
    main()