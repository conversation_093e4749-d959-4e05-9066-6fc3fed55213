#!/usr/bin/env python3
"""
Simple test of F-E-Line encoding
"""
import pandas as pd

# Test the encoding function directly
def test_encoding():
    print("🧪 Simple F-E-Line Encoding Test")
    
    # Create sample F-E-Line data
    sample_fe_lines = pd.Series([
        "1", "1.1", "1.1.1", "1.2", "2", "2.1", "3", "3.1", "3.1.1", "3.1.2",
        "A", "B", "C", "1", "1.1", "2"  # Include some duplicates
    ])
    
    print(f"📊 Sample F-E-Line data: {len(sample_fe_lines)} items")
    print(f"📊 Unique values: {sample_fe_lines.nunique()}")
    
    # Test encoding
    try:
        from bom_similarity_analysis import encode_fe_line
        
        encoded, fe_to_code, code_to_fe, parent_child = encode_fe_line(sample_fe_lines)
        
        print(f"\n✅ Encoding successful!")
        print(f"📊 Encoded shape: {encoded.shape}")
        print(f"📊 Encoded dtype: {encoded.dtype}")
        print(f"📊 Unique codes: {len(fe_to_code)}")
        print(f"📊 Parent-child relationships: {len(parent_child)}")
        
        # Show mapping
        print(f"\n📋 F-E-Line to Code mapping:")
        for fe, code in list(fe_to_code.items())[:10]:
            print(f"  '{fe}' → {code}")
        
        # Show parent-child relationships
        if parent_child:
            print(f"\n🌳 Parent-child relationships:")
            for child, parent in parent_child.items():
                child_fe = code_to_fe[child]
                parent_fe = code_to_fe[parent]
                print(f"  {parent_fe} → {child_fe}")
        
        print(f"\n✅ F-E-Line encoding works correctly!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_encoding()
