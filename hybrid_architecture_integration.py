#!/usr/bin/env python3
"""
Hybrid Architecture Integration
Combines automatic extraction + manual validation for optimal results
"""

import os
import pandas as pd
import numpy as np
import json
import re
from collections import defaultdict, Counter
import matplotlib.pyplot as plt

def create_intelligent_architecture_mapping(bom_df):
    """Create intelligent architecture mapping based on BOM analysis"""
    print("🧠 Creating intelligent architecture mapping...")
    
    # Analyze component patterns
    descriptions = bom_df['Item_Description'].astype(str).str.lower()
    
    # Define intelligent classification rules
    classification_rules = {
        'Mandatory': {
            'keywords': ['main', 'primary', 'core', 'essential', 'critical', 'base', 'fundamental'],
            'patterns': ['*main*', '*primary*', '*core*', '*base*'],
            'architecture_level': 1,
            'shipping_impact': 1,
            'creation_impact': 1,
            'confidence': 0.9
        },
        'Mandatory_Selectable': {
            'keywords': ['option', 'select', 'choice', 'variant', 'config', 'alternative'],
            'patterns': ['*option*', '*select*', '*variant*', '*config*'],
            'architecture_level': 2,
            'shipping_impact': 1,
            'creation_impact': 0,
            'confidence': 0.8
        },
        'Optional': {
            'keywords': ['optional', 'accessory', 'upgrade', 'addon', 'extra', 'additional'],
            'patterns': ['*optional*', '*accessory*', '*upgrade*', '*addon*'],
            'architecture_level': 3,
            'shipping_impact': 0,
            'creation_impact': 0,
            'confidence': 0.7
        }
    }
    
    # Apply intelligent classification
    intelligent_mapping = []
    
    for comp_type, rules in classification_rules.items():
        # Find components matching this type
        matches = set()
        
        # Keyword matching
        for keyword in rules['keywords']:
            mask = descriptions.str.contains(keyword, na=False)
            matches.update(bom_df[mask]['Item_Description'].unique())
        
        # Pattern matching
        for pattern in rules['patterns']:
            regex_pattern = pattern.replace('*', '.*')
            mask = descriptions.str.contains(regex_pattern, na=False, regex=True)
            matches.update(bom_df[mask]['Item_Description'].unique())
        
        # Create mapping entries
        for description in matches:
            # Extract potential item ID patterns
            matching_items = bom_df[bom_df['Item_Description'] == description]
            item_ids = matching_items['Item_ID'].unique()
            
            # Create pattern from item IDs
            if len(item_ids) > 0:
                # Use first item ID as base pattern
                base_id = str(item_ids[0])
                # Create wildcard pattern
                pattern = re.sub(r'\d+', '*', base_id)
                
                mapping_entry = {
                    'Component_Name': description[:50],  # Truncate long names
                    'Item_ID_Pattern': pattern,
                    'Component_Type': comp_type,
                    'Architecture_Level': rules['architecture_level'],
                    'Shipping_Impact': rules['shipping_impact'],
                    'Creation_Impact': rules['creation_impact'],
                    'Confidence': rules['confidence'],
                    'Auto_Generated': True,
                    'Sample_Item_IDs': ','.join(item_ids[:3])  # First 3 IDs as examples
                }
                
                intelligent_mapping.append(mapping_entry)
    
    print(f"✅ Generated {len(intelligent_mapping)} intelligent mappings")
    return pd.DataFrame(intelligent_mapping)

def analyze_fe_line_architecture_correlation(bom_df):
    """Analyze correlation between F-E-Line and architecture patterns"""
    print("🔍 Analyzing F-E-Line architecture correlation...")
    
    fe_line_analysis = {}
    
    for fe_line in bom_df['F_E_Line'].unique():
        fe_data = bom_df[bom_df['F_E_Line'] == fe_line]
        
        # Analyze this F-E-Line
        analysis = {
            'fe_line': fe_line,
            'item_count': len(fe_data),
            'unique_items': fe_data['Item_ID'].nunique(),
            'sample_descriptions': list(fe_data['Item_Description'].head(5)),
            
            # Infer architecture characteristics
            'inferred_criticality': infer_criticality_from_fe_line(fe_line, fe_data),
            'inferred_level': infer_architecture_level(fe_line, len(fe_data)),
            'shipping_likelihood': infer_shipping_impact(fe_data),
            'creation_likelihood': infer_creation_impact(fe_data)
        }
        
        fe_line_analysis[fe_line] = analysis
    
    return fe_line_analysis

def infer_criticality_from_fe_line(fe_line, fe_data):
    """Infer component criticality from F-E-Line patterns"""
    
    # Parse F-E-Line levels
    try:
        levels = [int(x) for x in str(fe_line).split('-')]
        level_1, level_2, level_3 = levels[0], levels[1], levels[2]
    except:
        return 'Unknown'
    
    # Size-based inference
    size = len(fe_data)
    
    # Large assemblies are likely mandatory
    if size > 1000:
        return 'Mandatory'
    
    # Medium assemblies could be selectable
    elif size > 100:
        return 'Mandatory_Selectable'
    
    # Small assemblies might be optional
    elif size < 50:
        return 'Optional'
    
    # Default based on hierarchy
    if level_1 == 1 and level_2 == 1:
        return 'Mandatory'  # Main assembly
    else:
        return 'Mandatory_Selectable'

def infer_architecture_level(fe_line, size):
    """Infer architecture level from F-E-Line and size"""
    
    try:
        levels = [int(x) for x in str(fe_line).split('-')]
        level_sum = sum(levels)
    except:
        return 2
    
    # Size-based adjustment
    if size > 5000:
        return 1  # Core level
    elif size > 1000:
        return 2  # Major level
    elif size > 100:
        return 3  # Minor level
    else:
        return 4  # Accessory level

def infer_shipping_impact(fe_data):
    """Infer shipping impact from component characteristics"""
    
    descriptions = fe_data['Item_Description'].astype(str).str.lower()
    
    # Physical components likely affect shipping
    physical_keywords = ['assembly', 'module', 'unit', 'component', 'part', 'hardware']
    physical_score = sum(descriptions.str.contains(keyword, na=False).sum() for keyword in physical_keywords)
    
    # Software/licenses don't affect shipping
    virtual_keywords = ['software', 'license', 'application', 'program']
    virtual_score = sum(descriptions.str.contains(keyword, na=False).sum() for keyword in virtual_keywords)
    
    # Calculate likelihood
    total_items = len(fe_data)
    if total_items == 0:
        return 0.5
    
    physical_ratio = physical_score / total_items
    virtual_ratio = virtual_score / total_items
    
    # Return probability of shipping impact
    return max(0.1, min(0.9, physical_ratio - virtual_ratio + 0.5))

def infer_creation_impact(fe_data):
    """Infer creation/manufacturing impact"""
    
    descriptions = fe_data['Item_Description'].astype(str).str.lower()
    
    # Manufacturing-related keywords
    manufacturing_keywords = ['assembly', 'fabrication', 'manufacturing', 'production', 'build']
    manufacturing_score = sum(descriptions.str.contains(keyword, na=False).sum() for keyword in manufacturing_keywords)
    
    # Configuration-related keywords
    config_keywords = ['configuration', 'setup', 'install', 'software', 'license']
    config_score = sum(descriptions.str.contains(keyword, na=False).sum() for keyword in config_keywords)
    
    total_items = len(fe_data)
    if total_items == 0:
        return 0.5
    
    manufacturing_ratio = manufacturing_score / total_items
    config_ratio = config_score / total_items
    
    return max(0.1, min(0.9, manufacturing_ratio + config_ratio * 0.5))

def create_hybrid_mapping(bom_df, intelligent_mapping, fe_line_analysis):
    """Create hybrid mapping combining intelligent analysis and F-E-Line correlation"""
    print("🔄 Creating hybrid architecture mapping...")
    
    hybrid_mapping = []
    
    # Start with intelligent mapping
    for _, row in intelligent_mapping.iterrows():
        hybrid_mapping.append(row.to_dict())
    
    # Add F-E-Line based mappings for uncovered components
    covered_patterns = set()
    for mapping in hybrid_mapping:
        covered_patterns.add(mapping['Item_ID_Pattern'])
    
    # Create F-E-Line based mappings
    for fe_line, analysis in fe_line_analysis.items():
        if analysis['item_count'] > 10:  # Only for significant F-E-Lines
            
            # Create pattern for this F-E-Line
            pattern = f"FE_{fe_line.replace('-', '_')}_*"
            
            if pattern not in covered_patterns:
                fe_mapping = {
                    'Component_Name': f"F-E-Line {fe_line} Assembly",
                    'Item_ID_Pattern': f"*{fe_line}*",  # Match F-E-Line in descriptions
                    'Component_Type': analysis['inferred_criticality'],
                    'Architecture_Level': analysis['inferred_level'],
                    'Shipping_Impact': 1 if analysis['shipping_likelihood'] > 0.6 else 0,
                    'Creation_Impact': 1 if analysis['creation_likelihood'] > 0.6 else 0,
                    'Confidence': 0.6,  # Lower confidence for inferred
                    'Auto_Generated': True,
                    'Source': 'F-E-Line Analysis',
                    'Sample_Item_IDs': ','.join(bom_df[bom_df['F_E_Line'] == fe_line]['Item_ID'].head(3))
                }
                
                hybrid_mapping.append(fe_mapping)
    
    print(f"✅ Created hybrid mapping with {len(hybrid_mapping)} entries")
    return pd.DataFrame(hybrid_mapping)

def validate_and_score_mapping(bom_df, mapping_df):
    """Validate and score the mapping quality"""
    print("✅ Validating mapping quality...")
    
    # Apply mapping to BOM
    bom_copy = bom_df.copy()
    bom_copy['Mapped_Type'] = 'Unclassified'
    bom_copy['Mapping_Confidence'] = 0.0
    
    total_mapped = 0
    
    for _, mapping in mapping_df.iterrows():
        pattern = mapping['Item_ID_Pattern']
        regex_pattern = pattern.replace('*', '.*').upper()
        
        # Match against Item_ID and Description
        id_mask = bom_copy['Item_ID'].astype(str).str.upper().str.contains(regex_pattern, na=False, regex=True)
        desc_mask = bom_copy['Item_Description'].astype(str).str.upper().str.contains(regex_pattern, na=False, regex=True)
        
        combined_mask = id_mask | desc_mask
        
        if combined_mask.any():
            bom_copy.loc[combined_mask, 'Mapped_Type'] = mapping['Component_Type']
            bom_copy.loc[combined_mask, 'Mapping_Confidence'] = mapping.get('Confidence', 0.5)
            total_mapped += combined_mask.sum()
    
    # Calculate quality metrics
    mapping_coverage = total_mapped / len(bom_df)
    avg_confidence = bom_copy[bom_copy['Mapped_Type'] != 'Unclassified']['Mapping_Confidence'].mean()
    
    type_distribution = bom_copy['Mapped_Type'].value_counts()
    
    validation_report = {
        'total_components': len(bom_df),
        'mapped_components': total_mapped,
        'mapping_coverage': mapping_coverage,
        'average_confidence': avg_confidence,
        'type_distribution': dict(type_distribution),
        'quality_score': mapping_coverage * avg_confidence  # Combined quality metric
    }
    
    print(f"📊 Mapping Quality Report:")
    print(f"   Coverage: {mapping_coverage:.1%}")
    print(f"   Avg Confidence: {avg_confidence:.2f}")
    print(f"   Quality Score: {validation_report['quality_score']:.2f}")
    
    return validation_report, bom_copy

def save_hybrid_results(bom_df, hybrid_mapping, fe_line_analysis, validation_report, output_folder):
    """Save all hybrid analysis results"""
    print("💾 Saving hybrid architecture integration results...")
    
    # Save hybrid mapping
    mapping_file = os.path.join(output_folder, 'hybrid_architecture_mapping.xlsx')
    with pd.ExcelWriter(mapping_file, engine='openpyxl') as writer:
        hybrid_mapping.to_excel(writer, sheet_name='Hybrid_Mapping', index=False)
        
        # Add validation sheet
        validation_df = pd.DataFrame([validation_report])
        validation_df.to_excel(writer, sheet_name='Validation_Report', index=False)
        
        # Add F-E-Line analysis
        fe_analysis_df = pd.DataFrame.from_dict(fe_line_analysis, orient='index')
        fe_analysis_df.to_excel(writer, sheet_name='FE_Line_Analysis', index=False)
    
    # Save comprehensive report
    report_file = os.path.join(output_folder, 'hybrid_integration_report.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("HYBRID ARCHITECTURE INTEGRATION REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("METHODOLOGY\n")
        f.write("-" * 15 + "\n")
        f.write("Hybrid approach combining:\n")
        f.write("1. Intelligent pattern recognition\n")
        f.write("2. F-E-Line correlation analysis\n")
        f.write("3. Automatic validation and scoring\n")
        f.write("4. Manual validation framework\n\n")
        
        f.write("MAPPING QUALITY\n")
        f.write("-" * 20 + "\n")
        f.write(f"Total components: {validation_report['total_components']}\n")
        f.write(f"Mapped components: {validation_report['mapped_components']}\n")
        f.write(f"Coverage: {validation_report['mapping_coverage']:.1%}\n")
        f.write(f"Average confidence: {validation_report['average_confidence']:.2f}\n")
        f.write(f"Quality score: {validation_report['quality_score']:.2f}\n\n")
        
        f.write("TYPE DISTRIBUTION\n")
        f.write("-" * 20 + "\n")
        for comp_type, count in validation_report['type_distribution'].items():
            percentage = (count / validation_report['total_components']) * 100
            f.write(f"{comp_type}: {count} ({percentage:.1f}%)\n")
    
    print("✅ Hybrid results saved")

def main():
    """Main hybrid integration function"""
    print("🔄 HYBRID ARCHITECTURE INTEGRATION")
    print("=" * 50)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Load Artist BOM data
    artist_file = r"C:\Users\<USER>\OneDrive - GE HealthCare\Documents\BOM_Cluster\input\4516256_0d1582e3-6687-4d88-94c5-4c98b07e76cb_1747378299412.xlsx"
    
    try:
        df = pd.read_excel(artist_file, sheet_name=1)
        df = df.iloc[:, :4]
        df.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
        df = df.dropna(subset=['F_E_Line', 'Item_ID', 'Item_Description'])
        df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
        
        print(f"✅ Loaded {len(df)} Artist BOM items")
        
    except Exception as e:
        print(f"❌ Error loading BOM data: {e}")
        return
    
    # Create intelligent mapping
    intelligent_mapping = create_intelligent_architecture_mapping(df)
    
    # Analyze F-E-Line correlations
    fe_line_analysis = analyze_fe_line_architecture_correlation(df)
    
    # Create hybrid mapping
    hybrid_mapping = create_hybrid_mapping(df, intelligent_mapping, fe_line_analysis)
    
    # Validate mapping
    validation_report, mapped_bom = validate_and_score_mapping(df, hybrid_mapping)
    
    # Save results
    save_hybrid_results(df, hybrid_mapping, fe_line_analysis, validation_report, output_folder)
    
    print(f"\n🎉 Hybrid Integration Complete!")
    print("=" * 50)
    print(f"📊 Mapping coverage: {validation_report['mapping_coverage']:.1%}")
    print(f"🎯 Quality score: {validation_report['quality_score']:.2f}")
    print(f"📁 Results saved in: {os.path.abspath(output_folder)}")
    
    # Show next steps
    print(f"\n📋 Next Steps:")
    print("1. Review hybrid_architecture_mapping.xlsx")
    print("2. Validate and adjust mappings as needed")
    print("3. Run enhanced_clustering_with_architecture.py")
    print("4. Compare results with original clustering")

if __name__ == "__main__":
    main()
