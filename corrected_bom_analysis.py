#!/usr/bin/env python3
"""
Corrected BOM Similarity Analysis
Fixed to use the correct columns: F-E-Line as Item ID, and proper data structure.
"""

import os
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
from scipy.spatial.distance import squareform
import matplotlib.pyplot as plt
import traceback
from datetime import datetime
import warnings
import pickle
import hashlib
import random
warnings.filterwarnings('ignore')

def clean_description(text):
    if pd.isna(text):
        return ""
    return str(text).lower().strip()

def load_bom_data_corrected(file_path):
    """Load and preprocess BOM data with CORRECTED column mapping"""
    try:
        # Read the second sheet (index 1) which contains BOM data
        xls = pd.ExcelFile(file_path)
        if len(xls.sheet_names) < 2:
            print(f"Warning: {file_path} doesn't have a second sheet")
            return None

        df = pd.read_excel(file_path, sheet_name=1)

        # Keep only the first 4 columns
        if len(df.columns) >= 4:
            df = df.iloc[:, :4]
            # CORRECTED column mapping based on actual data structure
            df.columns = ['Level', 'Sequence', 'Item_ID', 'Item_Description']
            
            # The real Item ID is in the 3rd column (F-E-Line), not the 2nd column
            print(f"    📊 Original rows: {len(df)}")

            # Remove rows with ANY empty values in ALL 4 columns
            df_before = len(df)
            df = df.dropna(subset=['Level', 'Sequence', 'Item_ID', 'Item_Description'])
            df_after = len(df)

            # Additional cleaning: remove rows where any column is just whitespace
            df = df[df['Level'].astype(str).str.strip() != '']
            df = df[df['Sequence'].astype(str).str.strip() != '']
            df = df[df['Item_ID'].astype(str).str.strip() != '']
            df = df[df['Item_Description'].astype(str).str.strip() != '']

            df_after_cleaning = len(df)

            print(f"    🧹 After removing incomplete rows: {df_after} (removed {df_before - df_after})")
            print(f"    🧹 After removing empty/whitespace: {df_after_cleaning} (removed {df_after - df_after_cleaning})")

            # Filter to only Level 1, 2, and 3 (extract numeric part from level)
            df_before_level_filter = len(df)
            # Extract first number from level string (e.g., "1-1-2" -> 1)
            df['Level_Numeric'] = df['Level'].astype(str).str.extract(r'^(\d+)').astype(float)
            df = df[df['Level_Numeric'].isin([1.0, 2.0, 3.0])]
            df_after_level_filter = len(df)
            
            print(f"    🎯 After filtering to Level 1, 2 & 3: {df_after_level_filter} (removed {df_before_level_filter - df_after_level_filter})")

            if df_after_level_filter == 0:
                print(f"    ❌ No valid Level 1, 2 or 3 rows found")
                return None

            # Clean and normalize descriptions for better matching
            df['Item_Description'] = df['Item_Description'].apply(clean_description)

            # Remove rows where description becomes empty after cleaning
            df = df[df['Item_Description'].str.len() > 0]

            # Remove rows where Item_ID is just a number (these are sequence numbers, not real IDs)
            df_before_id_filter = len(df)
            df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
            df_after_id_filter = len(df)
            
            print(f"    🔧 After removing numeric-only Item IDs: {df_after_id_filter} (removed {df_before_id_filter - df_after_id_filter})")

            df_final = len(df)
            if df_final == 0:
                print(f"    ❌ No valid items after filtering")
                return None

            print(f"    ✅ Final valid Level 1,2&3 items: {df_final}")
            
            # Show sample of what we're working with
            if df_final > 0:
                print(f"    📋 Sample Item IDs: {df['Item_ID'].head(5).tolist()}")
                print(f"    📋 Sample Descriptions: {df['Item_Description'].head(3).tolist()}")
            
            return df
        else:
            print(f"Warning: {file_path} doesn't have at least 4 columns")
            return None
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def fast_sample_items_corrected(all_bom_data, max_items=5000):
    """Fast sampling with corrected data structure"""
    print(f"\n🎯 Fast sampling to {max_items} items for corrected analysis...")
    
    # Combine all items with file info
    all_items = []
    for file_name, df in all_bom_data.items():
        for idx, row in df.iterrows():
            item_info = {
                'file': file_name,
                'item_id': str(row['Item_ID']),
                'level': row['Level'],
                'sequence': str(row['Sequence']),
                'description': str(row['Item_Description']),
                'level_numeric': row['Level_Numeric'],
                'original_index': idx
            }
            all_items.append(item_info)
    
    print(f"    📊 Total valid items across all files: {len(all_items)}")
    
    if len(all_items) <= max_items:
        print(f"    ✅ Dataset size acceptable, using all {len(all_items)} items")
        return all_items
    
    # Fast sampling strategy
    print(f"    🔄 Applying fast sampling...")
    
    # Group by file
    file_groups = {}
    for item in all_items:
        file_key = item['file']
        if file_key not in file_groups:
            file_groups[file_key] = []
        file_groups[file_key].append(item)
    
    # Calculate items per file (proportional)
    items_per_file = max_items // len(file_groups)
    sampled_items = []
    
    for file_key, file_items in file_groups.items():
        if len(file_items) <= items_per_file:
            sampled_items.extend(file_items)
        else:
            # Prioritize Level 1 and 2, then sample
            level_1_2 = [item for item in file_items if item['level_numeric'] in [1.0, 2.0]]
            level_3 = [item for item in file_items if item['level_numeric'] == 3.0]
            
            # Take more Level 1&2 items
            level_1_2_count = min(len(level_1_2), int(items_per_file * 0.7))
            level_3_count = min(len(level_3), items_per_file - level_1_2_count)
            
            if level_1_2_count > 0:
                sampled_items.extend(random.sample(level_1_2, level_1_2_count))
            if level_3_count > 0:
                sampled_items.extend(random.sample(level_3, level_3_count))
    
    print(f"    ✅ Fast sampling complete: {len(sampled_items)} items selected")
    return sampled_items

def create_corrected_similarity_analysis(sampled_items, threshold=0.7):
    """Corrected similarity analysis using proper Item IDs"""
    print(f"\n🚀 Corrected similarity analysis on {len(sampled_items)} items...")
    
    # Extract features
    item_ids = [item['item_id'] for item in sampled_items]
    descriptions = [item['description'] for item in sampled_items]
    
    # Check for duplicate Item IDs (these should be exact matches)
    from collections import Counter
    item_id_counts = Counter(item_ids)
    duplicate_ids = {k: v for k, v in item_id_counts.items() if v > 1}
    
    if duplicate_ids:
        print(f"    🔍 Found {len(duplicate_ids)} Item IDs appearing multiple times:")
        for item_id, count in list(duplicate_ids.items())[:5]:
            print(f"      '{item_id}': appears {count} times")
    
    # Optimized TF-IDF with limited features
    print("    🔍 Creating optimized TF-IDF vectors...")
    desc_vectorizer = TfidfVectorizer(
        min_df=2, max_df=0.8, ngram_range=(1, 2), 
        stop_words='english', lowercase=True, strip_accents='unicode',
        max_features=3000  # Limit features for speed
    )
    
    id_vectorizer = TfidfVectorizer(
        min_df=1, analyzer='char_wb', ngram_range=(2, 4), lowercase=True,
        max_features=1000  # Limit features for IDs
    )
    
    try:
        desc_tfidf_matrix = desc_vectorizer.fit_transform(descriptions)
        id_tfidf_matrix = id_vectorizer.fit_transform(item_ids)
        print(f"    ✅ TF-IDF matrices created: {desc_tfidf_matrix.shape}, {id_tfidf_matrix.shape}")
        
        # Calculate similarities efficiently
        print("    🔍 Calculating similarity matrices...")
        desc_similarity_matrix = cosine_similarity(desc_tfidf_matrix)
        id_similarity_matrix = cosine_similarity(id_tfidf_matrix)
        
        # Find similar pairs efficiently
        print(f"    🔍 Finding similar pairs (threshold: {threshold})...")
        similarity_records = []
        n_items = len(sampled_items)
        
        for i in range(n_items):
            for j in range(i + 1, n_items):  # Only upper triangle
                desc_sim = desc_similarity_matrix[i, j]
                id_sim = id_similarity_matrix[i, j]
                
                # Check exact matches first
                exact_id_match = item_ids[i].lower().strip() == item_ids[j].lower().strip()
                
                # Combined score
                combined_score = (desc_sim * 0.6) + (id_sim * 0.4)
                if exact_id_match:
                    combined_score = 1.0  # Perfect match for same Item ID
                
                combined_score = min(combined_score, 1.0)
                
                if combined_score >= threshold:
                    item1 = sampled_items[i]
                    item2 = sampled_items[j]
                    
                    record = {
                        'Item_ID_1': item1['item_id'],
                        'File_1': item1['file'],
                        'Level_1': item1['level'],
                        'Sequence_1': item1['sequence'],
                        'Description_1': item1['description'],
                        'Item_ID_2': item2['item_id'],
                        'File_2': item2['file'],
                        'Level_2': item2['level'],
                        'Sequence_2': item2['sequence'],
                        'Description_2': item2['description'],
                        'Similarity_Score': round(combined_score, 4),
                        'Resemblance_Percentage': round(combined_score * 100, 2),
                        'Same_File': item1['file'] == item2['file'],
                        'Same_Level': item1['level'] == item2['level'],
                        'Exact_ID_Match': exact_id_match,
                        'Cross_File_Match': item1['file'] != item2['file'],
                        'Description_Similarity': round(desc_sim, 4),
                        'ID_Similarity': round(id_sim, 4)
                    }
                    similarity_records.append(record)
        
        similarity_df = pd.DataFrame(similarity_records)
        
        if len(similarity_df) > 0:
            similarity_df = similarity_df.sort_values('Similarity_Score', ascending=False)
            print(f"    ✅ Found {len(similarity_df)} similar item pairs")
            print(f"    📊 Cross-file matches: {similarity_df['Cross_File_Match'].sum()}")
            print(f"    📊 Same-file matches: {(~similarity_df['Cross_File_Match']).sum()}")
            print(f"    📊 Exact ID matches: {similarity_df['Exact_ID_Match'].sum()}")
        else:
            print(f"    ⚠️  No similar items found above threshold {threshold}")
        
        return similarity_df, sampled_items
        
    except Exception as e:
        print(f"    ❌ Error during similarity calculation: {e}")
        return None, None

def main():
    print("🚀 Starting CORRECTED Global BOM Similarity Analysis")
    print("=" * 70)
    print(f"⏰ Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Using CORRECTED column mapping: F-E-Line as Item ID")

    input_folder = 'input'
    output_folder = 'output'

    # Create output folder
    os.makedirs(output_folder, exist_ok=True)

    # Get all Excel files
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    print(f"📊 Found {len(excel_files)} Excel files to analyze")

    if len(excel_files) == 0:
        print("❌ No Excel files found in the input folder!")
        return

    # Load BOM data with corrected structure
    print("\n🔄 Loading BOM data with CORRECTED column mapping...")
    bom_data = {}
    failed_files = []

    for i, file in enumerate(excel_files, 1):  # Process all files
        print(f"  [{i}/{len(excel_files)}] Loading: {file}")
        file_path = os.path.join(input_folder, file)

        try:
            df = load_bom_data_corrected(file_path)
            if df is not None:
                bom_data[file] = df
                print(f"    ✅ Loaded {len(df)} valid items")
            else:
                failed_files.append(file)
                print(f"    ❌ Failed to load")
        except Exception as e:
            failed_files.append(file)
            print(f"    ❌ Error: {str(e)}")

    print(f"\n📈 Successfully loaded {len(bom_data)} files")
    if failed_files:
        print(f"⚠️  Failed to load {len(failed_files)} files: {failed_files}")

    if len(bom_data) < 1:
        print("❌ Need at least 1 file with valid data!")
        return

    # Fast sampling - increase sample size for all files
    sampled_items = fast_sample_items_corrected(bom_data, max_items=10000)
    
    # Corrected similarity analysis
    similarity_df, item_metadata = create_corrected_similarity_analysis(sampled_items, threshold=0.7)
    
    if similarity_df is None:
        print("❌ Could not perform similarity analysis!")
        return
    
    # Generate reports
    print(f"\n📝 Generating corrected analysis reports...")
    
    # Text summary report
    with open(os.path.join(output_folder, 'corrected_similarity_summary.txt'), 'w', encoding='utf-8') as f:
        f.write("CORRECTED Global BOM Similarity Analysis Summary\n")
        f.write("=" * 60 + "\n")
        f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Analysis Method: Corrected Column Mapping + TF-IDF Similarity\n")
        f.write(f"Column Mapping: F-E-Line as Item ID (corrected)\n")
        f.write(f"Total files analyzed: {len(bom_data)}\n")
        f.write(f"Total items sampled: {len(sampled_items)}\n")
        f.write(f"Similar pairs found: {len(similarity_df) if similarity_df is not None else 0}\n\n")

        if similarity_df is not None and len(similarity_df) > 0:
            f.write("Top 20 Similar Item Pairs (CORRECTED)\n")
            f.write("=" * 40 + "\n")
            top_pairs = similarity_df.head(20)
            for i, (_, pair) in enumerate(top_pairs.iterrows(), 1):
                f.write(f"{i:2d}. {pair['Item_ID_1']} ↔ {pair['Item_ID_2']} ({pair['Resemblance_Percentage']:.1f}%)\n")
                f.write(f"    Files: {pair['File_1'][:30]}... ↔ {pair['File_2'][:30]}...\n")
                f.write(f"    Descriptions: {pair['Description_1'][:40]}... ↔ {pair['Description_2'][:40]}...\n")
                f.write(f"    Cross-file: {pair['Cross_File_Match']} | Exact ID: {pair['Exact_ID_Match']}\n\n")

    # Excel report
    try:
        with pd.ExcelWriter(os.path.join(output_folder, 'corrected_bom_similarity_analysis.xlsx'), engine='openpyxl') as writer:
            if similarity_df is not None and len(similarity_df) > 0:
                similarity_df.to_excel(writer, sheet_name='Corrected_Similarities', index=False)

        print(f"\n📊 Excel report saved: corrected_bom_similarity_analysis.xlsx")

    except Exception as e:
        print(f"\n❌ Error saving Excel report: {str(e)}")

    # Final summary
    print(f"\n🎉 CORRECTED Global BOM Similarity Analysis Complete!")
    print("=" * 70)
    print(f"⏰ End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Reports saved to: {os.path.abspath(output_folder)}")
    print(f"📊 Total files analyzed: {len(bom_data)}")
    print(f"📊 Items sampled for analysis: {len(sampled_items)}")
    print(f"🎯 Similar pairs found: {len(similarity_df) if similarity_df is not None else 0}")

    if similarity_df is not None and len(similarity_df) > 0:
        print(f"\n🏆 Top 5 most similar items found (CORRECTED):")
        top_matches = similarity_df.head(5)
        for i, (_, match) in enumerate(top_matches.iterrows(), 1):
            cross_file = " [Cross-File]" if match['Cross_File_Match'] else " [Same-File]"
            exact_id = " [Exact ID]" if match['Exact_ID_Match'] else ""
            print(f"  {i}. {match['Item_ID_1']} ↔ {match['Item_ID_2']} ({match['Resemblance_Percentage']:.1f}%){cross_file}{exact_id}")
    else:
        print("\n⚠️  No similar items found with current threshold (0.7)")

if __name__ == "__main__":
    main()
