#!/usr/bin/env python3
"""
Quick test of the optimization
"""

import pandas as pd
import os

# Test the deduplication concept
print("🧪 Quick Deduplication Test")
print("=" * 30)

# Get first Excel file
input_folder = 'input'
excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]

if excel_files:
    test_file = excel_files[0]
    print(f"📄 Testing: {test_file}")
    
    try:
        # Read Excel file
        file_path = os.path.join(input_folder, test_file)
        df = pd.read_excel(file_path, sheet_name=1)
        
        print(f"📊 Original shape: {df.shape}")
        
        if len(df.columns) >= 4:
            # Take first 4 columns
            df = df.iloc[:, :4]
            df.columns = ['Level', 'Item ID', 'F-E-Line', 'Item Description']
            
            # Remove empty rows
            df = df.dropna(subset=['Level', 'Item ID', 'F-E-Line', 'Item Description'])
            print(f"📊 After removing empty: {df.shape}")
            
            # Create unique key
            df['unique_key'] = df['Item ID'].astype(str) + "|||" + df['Item Description'].astype(str)
            
            # Count duplicates
            duplicates = df['unique_key'].duplicated().sum()
            unique_items = len(df['unique_key'].unique())
            
            print(f"📊 Total items: {len(df)}")
            print(f"📊 Unique items: {unique_items}")
            print(f"📊 Duplicates: {duplicates}")
            print(f"💾 Reduction: {(duplicates/len(df)*100):.1f}%")
            
            if duplicates > 0:
                print("✅ Deduplication will help!")
            else:
                print("⚠️  No duplicates found in this file")
                
        else:
            print("❌ Not enough columns")
            
    except Exception as e:
        print(f"❌ Error: {e}")
else:
    print("❌ No Excel files found")

print("\n🎯 Test complete!")
