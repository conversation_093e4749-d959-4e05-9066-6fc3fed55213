#!/usr/bin/env python3
"""
Exploratory Multi-Dimensional Clustering for Artist Product
Advanced clustering combining multiple features beyond simple F-E-Line grouping
"""

import os
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import silhouette_score, calinski_harabasz_score
import matplotlib.pyplot as plt
# import seaborn as sns  # Not available
from collections import Counter
import re
import warnings
warnings.filterwarnings('ignore')

def load_artist_data():
    """Load Artist BOM data"""
    file_path = r"C:\Users\<USER>\OneDrive - GE HealthCare\Documents\BOM_Cluster\input\4516256_0d1582e3-6687-4d88-94c5-4c98b07e76cb_1747378299412.xlsx"
    
    try:
        df = pd.read_excel(file_path, sheet_name=1)
        df = df.iloc[:, :4]
        df.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
        
        # Clean data
        df = df.dropna(subset=['F_E_Line', 'Item_ID', 'Item_Description'])
        df = df[df['Item_ID'].astype(str).str.strip() != '']
        df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
        
        print(f"✅ Loaded {len(df)} Artist BOM items for exploratory analysis")
        return df
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

def extract_comprehensive_features(df):
    """Extract comprehensive features for exploratory clustering"""
    print("🔍 Extracting comprehensive features for clustering...")
    
    # 1. F-E-Line Features (hierarchical structure)
    df['FE_Level_1'] = df['F_E_Line'].astype(str).str.split('-').str[0].astype(float)
    df['FE_Level_2'] = df['F_E_Line'].astype(str).str.split('-').str[1].astype(float)
    df['FE_Level_3'] = df['F_E_Line'].astype(str).str.split('-').str[2].astype(float)
    df['FE_Depth'] = df['FE_Level_1'] + df['FE_Level_2'] + df['FE_Level_3']
    df['FE_Complexity'] = df['FE_Level_1'] * df['FE_Level_2'] * df['FE_Level_3']
    
    # 2. Item ID Features (patterns in part numbers)
    df['Item_Length'] = df['Item_ID'].astype(str).str.len()
    df['Item_Has_Numbers'] = df['Item_ID'].astype(str).str.contains(r'\d').astype(int)
    df['Item_Has_Letters'] = df['Item_ID'].astype(str).str.contains(r'[A-Za-z]').astype(int)
    df['Item_Has_Dash'] = df['Item_ID'].astype(str).str.contains(r'-').astype(int)
    df['Item_Numeric_Count'] = df['Item_ID'].astype(str).str.count(r'\d')
    df['Item_Alpha_Count'] = df['Item_ID'].astype(str).str.count(r'[A-Za-z]')
    
    # 3. Description Features (semantic analysis)
    descriptions = df['Item_Description'].astype(str).str.lower()
    df['Desc_Length'] = descriptions.str.len()
    df['Desc_Word_Count'] = descriptions.str.split().str.len()
    df['Desc_Unique_Words'] = descriptions.apply(lambda x: len(set(x.split())))
    df['Desc_Complexity'] = df['Desc_Unique_Words'] / df['Desc_Word_Count']
    
    # 4. Technical Keywords (domain-specific)
    technical_keywords = {
        'size_indicators': ['3.0t', '1.5t', '8-channel', '16-channel', '32-channel'],
        'medical_terms': ['excite', 'array', 'coil', 'gradient', 'rf', 'bore', 'patient'],
        'electronic_terms': ['pcb', 'circuit', 'processor', 'controller', 'interface'],
        'mechanical_terms': ['screw', 'bolt', 'mount', 'bracket', 'housing', 'cover'],
        'software_terms': ['software', 'license', 'application', 'program', 'os'],
        'quality_terms': ['premium', 'standard', 'basic', 'advanced', 'professional']
    }
    
    for category, keywords in technical_keywords.items():
        pattern = '|'.join(keywords)
        df[f'Has_{category}'] = descriptions.str.contains(pattern, na=False).astype(int)
        df[f'Count_{category}'] = descriptions.str.count(pattern)
    
    # 5. Frequency-based Features
    item_counts = df['Item_ID'].value_counts()
    df['Item_Frequency'] = df['Item_ID'].map(item_counts)
    df['Item_Rarity'] = 1 / df['Item_Frequency']
    
    fe_line_counts = df['F_E_Line'].value_counts()
    df['FE_Line_Size'] = df['F_E_Line'].map(fe_line_counts)
    df['FE_Line_Rarity'] = 1 / df['FE_Line_Size']
    
    # 6. Position-based Features
    df['Sequence_Numeric'] = pd.to_numeric(df['Sequence'], errors='coerce').fillna(0)
    df['Position_in_FE'] = df.groupby('F_E_Line').cumcount() + 1
    df['Relative_Position'] = df['Position_in_FE'] / df['FE_Line_Size']
    
    print(f"✅ Extracted {len([col for col in df.columns if col not in ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']])} features")
    return df

def create_text_features(df):
    """Create TF-IDF features from descriptions"""
    print("📝 Creating TF-IDF text features...")
    
    # Advanced TF-IDF with multiple configurations
    vectorizers = {
        'unigrams': TfidfVectorizer(max_features=200, ngram_range=(1,1), min_df=3, max_df=0.8),
        'bigrams': TfidfVectorizer(max_features=200, ngram_range=(2,2), min_df=2, max_df=0.9),
        'mixed': TfidfVectorizer(max_features=300, ngram_range=(1,2), min_df=2, max_df=0.85)
    }
    
    descriptions = df['Item_Description'].astype(str).str.lower()
    
    text_features = {}
    for name, vectorizer in vectorizers.items():
        try:
            tfidf_matrix = vectorizer.fit_transform(descriptions)
            feature_names = [f'tfidf_{name}_{i}' for i in range(tfidf_matrix.shape[1])]
            tfidf_df = pd.DataFrame(tfidf_matrix.toarray(), columns=feature_names, index=df.index)
            text_features[name] = tfidf_df
            print(f"  ✅ {name}: {tfidf_matrix.shape[1]} features")
        except Exception as e:
            print(f"  ⚠️  {name} failed: {e}")
    
    return text_features

def perform_exploratory_clustering(df, text_features):
    """Perform multiple clustering algorithms with different feature combinations"""
    print("🎯 Performing exploratory clustering...")
    
    # Prepare feature sets
    numeric_features = [col for col in df.columns if col not in ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description'] 
                       and df[col].dtype in ['int64', 'float64']]
    
    feature_sets = {
        'structural': ['FE_Level_1', 'FE_Level_2', 'FE_Level_3', 'FE_Depth', 'FE_Complexity', 
                      'Position_in_FE', 'Relative_Position', 'FE_Line_Size'],
        'item_patterns': ['Item_Length', 'Item_Has_Numbers', 'Item_Has_Letters', 'Item_Has_Dash',
                         'Item_Numeric_Count', 'Item_Alpha_Count', 'Item_Frequency', 'Item_Rarity'],
        'description_stats': ['Desc_Length', 'Desc_Word_Count', 'Desc_Unique_Words', 'Desc_Complexity'],
        'technical_content': [col for col in df.columns if col.startswith('Has_') or col.startswith('Count_')],
        'all_numeric': numeric_features
    }
    
    clustering_results = {}
    
    for feature_set_name, features in feature_sets.items():
        print(f"\n🔍 Clustering with {feature_set_name} features ({len(features)} features)...")
        
        # Prepare data
        X = df[features].fillna(0)
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Multiple clustering algorithms
        algorithms = {
            'kmeans_5': KMeans(n_clusters=5, random_state=42, n_init=10),
            'kmeans_8': KMeans(n_clusters=8, random_state=42, n_init=10),
            'kmeans_12': KMeans(n_clusters=12, random_state=42, n_init=10),
            'hierarchical_5': AgglomerativeClustering(n_clusters=5),
            'hierarchical_8': AgglomerativeClustering(n_clusters=8),
            'dbscan_05': DBSCAN(eps=0.5, min_samples=5),
            'dbscan_08': DBSCAN(eps=0.8, min_samples=10),
            'dbscan_12': DBSCAN(eps=1.2, min_samples=15)
        }
        
        feature_results = {}
        
        for algo_name, algorithm in algorithms.items():
            try:
                labels = algorithm.fit_predict(X_scaled)
                n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
                
                if n_clusters > 1:
                    silhouette = silhouette_score(X_scaled, labels)
                    calinski = calinski_harabasz_score(X_scaled, labels)
                    
                    feature_results[algo_name] = {
                        'labels': labels,
                        'n_clusters': n_clusters,
                        'silhouette_score': silhouette,
                        'calinski_score': calinski,
                        'noise_points': sum(labels == -1) if -1 in labels else 0
                    }
                    
                    print(f"    {algo_name}: {n_clusters} clusters, silhouette={silhouette:.3f}")
                
            except Exception as e:
                print(f"    ⚠️  {algo_name} failed: {e}")
        
        clustering_results[feature_set_name] = feature_results
    
    # Text-based clustering
    print(f"\n📝 Text-based clustering...")
    for text_name, text_df in text_features.items():
        X_text = text_df.values
        
        text_algorithms = {
            'kmeans_text_8': KMeans(n_clusters=8, random_state=42),
            'hierarchical_text_6': AgglomerativeClustering(n_clusters=6),
            'dbscan_text': DBSCAN(eps=0.3, min_samples=3)
        }
        
        text_results = {}
        for algo_name, algorithm in text_algorithms.items():
            try:
                labels = algorithm.fit_predict(X_text)
                n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
                
                if n_clusters > 1:
                    silhouette = silhouette_score(X_text, labels)
                    text_results[algo_name] = {
                        'labels': labels,
                        'n_clusters': n_clusters,
                        'silhouette_score': silhouette
                    }
                    print(f"    {text_name}_{algo_name}: {n_clusters} clusters, silhouette={silhouette:.3f}")
            except:
                pass
        
        clustering_results[f'text_{text_name}'] = text_results
    
    return clustering_results

def analyze_best_clustering(df, clustering_results):
    """Analyze and select best clustering results"""
    print("\n🏆 Analyzing best clustering results...")
    
    # Find best clustering by silhouette score
    best_results = []
    
    for feature_set, algorithms in clustering_results.items():
        for algo_name, results in algorithms.items():
            if 'silhouette_score' in results:
                best_results.append({
                    'feature_set': feature_set,
                    'algorithm': algo_name,
                    'n_clusters': results['n_clusters'],
                    'silhouette_score': results['silhouette_score'],
                    'calinski_score': results.get('calinski_score', 0),
                    'labels': results['labels']
                })
    
    # Sort by silhouette score
    best_results.sort(key=lambda x: x['silhouette_score'], reverse=True)
    
    print("🎯 Top 10 clustering results:")
    for i, result in enumerate(best_results[:10], 1):
        print(f"  {i:2d}. {result['feature_set']:15s} | {result['algorithm']:15s} | "
              f"{result['n_clusters']:2d} clusters | silhouette={result['silhouette_score']:.3f}")
    
    # Analyze best clustering in detail
    if best_results:
        best = best_results[0]
        df['Best_Cluster'] = best['labels']
        
        print(f"\n🔍 Detailed analysis of best clustering:")
        print(f"Feature set: {best['feature_set']}")
        print(f"Algorithm: {best['algorithm']}")
        print(f"Number of clusters: {best['n_clusters']}")
        print(f"Silhouette score: {best['silhouette_score']:.3f}")
        
        # Cluster characteristics
        cluster_analysis = {}
        for cluster_id in sorted(df['Best_Cluster'].unique()):
            if cluster_id == -1:  # Skip noise
                continue
                
            cluster_data = df[df['Best_Cluster'] == cluster_id]
            
            analysis = {
                'size': len(cluster_data),
                'percentage': len(cluster_data) / len(df) * 100,
                'top_fe_lines': dict(cluster_data['F_E_Line'].value_counts().head(3)),
                'avg_item_frequency': cluster_data['Item_Frequency'].mean(),
                'common_descriptions': list(cluster_data['Item_Description'].value_counts().head(3).index),
                'technical_profile': {}
            }
            
            # Technical profile
            tech_cols = [col for col in df.columns if col.startswith('Has_')]
            for col in tech_cols:
                if cluster_data[col].sum() > 0:
                    analysis['technical_profile'][col] = cluster_data[col].mean()
            
            cluster_analysis[f'Cluster_{cluster_id}'] = analysis
        
        return best, cluster_analysis
    
    return None, None

def create_advanced_visualizations(df, best_result, cluster_analysis, output_folder):
    """Create comprehensive visualizations"""
    print("📊 Creating advanced visualizations...")
    
    if best_result is None:
        return
    
    fig, axes = plt.subplots(3, 3, figsize=(20, 15))
    
    # 1. Cluster distribution
    cluster_counts = df['Best_Cluster'].value_counts().sort_index()
    axes[0,0].bar(range(len(cluster_counts)), cluster_counts.values)
    axes[0,0].set_title('Cluster Size Distribution')
    axes[0,0].set_xlabel('Cluster ID')
    axes[0,0].set_ylabel('Number of Items')
    
    # 2. F-E-Line vs Clusters
    fe_cluster_crosstab = pd.crosstab(df['F_E_Line'], df['Best_Cluster'])
    top_fe_lines = df['F_E_Line'].value_counts().head(10).index
    fe_subset = fe_cluster_crosstab.loc[top_fe_lines]
    
    im = axes[0,1].imshow(fe_subset.values, cmap='Blues', aspect='auto')
    axes[0,1].set_title('F-E-Line vs Clusters Heatmap')
    axes[0,1].set_xlabel('Cluster ID')
    axes[0,1].set_ylabel('Top F-E-Lines')
    axes[0,1].set_yticks(range(len(top_fe_lines)))
    axes[0,1].set_yticklabels(top_fe_lines, fontsize=8)
    
    # 3. Item frequency vs clusters
    for cluster_id in sorted(df['Best_Cluster'].unique())[:6]:
        if cluster_id != -1:
            cluster_data = df[df['Best_Cluster'] == cluster_id]
            axes[0,2].hist(cluster_data['Item_Frequency'], alpha=0.6, 
                          label=f'Cluster {cluster_id}', bins=20)
    axes[0,2].set_title('Item Frequency Distribution by Cluster')
    axes[0,2].set_xlabel('Item Frequency')
    axes[0,2].set_ylabel('Count')
    axes[0,2].legend()
    axes[0,2].set_yscale('log')
    
    # 4. Description length vs clusters
    df.boxplot(column='Desc_Length', by='Best_Cluster', ax=axes[1,0])
    axes[1,0].set_title('Description Length by Cluster')
    axes[1,0].set_xlabel('Cluster ID')
    
    # 5. Technical content heatmap
    tech_cols = [col for col in df.columns if col.startswith('Has_')][:8]
    if tech_cols:
        tech_by_cluster = df.groupby('Best_Cluster')[tech_cols].mean()
        im2 = axes[1,1].imshow(tech_by_cluster.values, cmap='Reds', aspect='auto')
        axes[1,1].set_title('Technical Content by Cluster')
        axes[1,1].set_xlabel('Technical Features')
        axes[1,1].set_ylabel('Cluster ID')
        axes[1,1].set_xticks(range(len(tech_cols)))
        axes[1,1].set_xticklabels([col.replace('Has_', '') for col in tech_cols], rotation=45)
        
    # 6. PCA visualization
    numeric_features = [col for col in df.columns if col not in ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description', 'Best_Cluster'] 
                       and df[col].dtype in ['int64', 'float64']]
    
    if len(numeric_features) > 2:
        X_pca_viz = df[numeric_features].fillna(0)
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_pca_viz)
        
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X_scaled)
        
        scatter = axes[1,2].scatter(X_pca[:, 0], X_pca[:, 1], c=df['Best_Cluster'], 
                                   cmap='tab10', alpha=0.6)
        axes[1,2].set_title(f'PCA Visualization\n({pca.explained_variance_ratio_[0]:.1%} + {pca.explained_variance_ratio_[1]:.1%} variance)')
        axes[1,2].set_xlabel('PC1')
        axes[1,2].set_ylabel('PC2')
        plt.colorbar(scatter, ax=axes[1,2])
    
    # 7-9. Cluster profiles
    for i, (cluster_name, analysis) in enumerate(list(cluster_analysis.items())[:3]):
        row, col = 2, i
        
        # Technical profile radar-like
        tech_profile = analysis.get('technical_profile', {})
        if tech_profile:
            features = list(tech_profile.keys())[:6]
            values = [tech_profile[f] for f in features]
            
            axes[row, col].bar(range(len(features)), values)
            axes[row, col].set_title(f'{cluster_name}\n({analysis["size"]} items, {analysis["percentage"]:.1f}%)')
            axes[row, col].set_xticks(range(len(features)))
            axes[row, col].set_xticklabels([f.replace('Has_', '') for f in features], rotation=45, fontsize=8)
            axes[row, col].set_ylabel('Presence Rate')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, 'exploratory_clustering_analysis.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Advanced visualizations saved")

def save_exploratory_results(df, best_result, cluster_analysis, clustering_results, output_folder):
    """Save comprehensive exploratory results"""
    print("💾 Saving exploratory clustering results...")
    
    # Save main results
    df.to_excel(os.path.join(output_folder, 'artist_exploratory_clustering.xlsx'), index=False)
    
    # Save cluster analysis
    if cluster_analysis:
        cluster_summary = []
        for cluster_name, analysis in cluster_analysis.items():
            summary_row = {
                'Cluster_Name': cluster_name,
                'Size': analysis['size'],
                'Percentage': analysis['percentage'],
                'Avg_Item_Frequency': analysis['avg_item_frequency'],
                'Top_FE_Lines': str(analysis['top_fe_lines']),
                'Sample_Descriptions': '; '.join(analysis['common_descriptions'][:2])
            }
            
            # Add technical profile
            for tech_feature, value in analysis.get('technical_profile', {}).items():
                summary_row[tech_feature] = value
            
            cluster_summary.append(summary_row)
        
        cluster_df = pd.DataFrame(cluster_summary)
        cluster_df.to_excel(os.path.join(output_folder, 'artist_cluster_profiles.xlsx'), index=False)
    
    # Save comprehensive report
    with open(os.path.join(output_folder, 'artist_exploratory_report.txt'), 'w', encoding='utf-8') as f:
        f.write("ARTIST EXPLORATORY CLUSTERING ANALYSIS\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("METHODOLOGY\n")
        f.write("-" * 15 + "\n")
        f.write("Multi-dimensional clustering combining:\n")
        f.write("- Structural features (F-E-Line hierarchy)\n")
        f.write("- Item pattern features (ID structure)\n")
        f.write("- Description statistics\n")
        f.write("- Technical content analysis\n")
        f.write("- Frequency and rarity metrics\n")
        f.write("- TF-IDF text features\n\n")
        
        if best_result:
            f.write("BEST CLUSTERING RESULT\n")
            f.write("-" * 25 + "\n")
            f.write(f"Feature set: {best_result['feature_set']}\n")
            f.write(f"Algorithm: {best_result['algorithm']}\n")
            f.write(f"Number of clusters: {best_result['n_clusters']}\n")
            f.write(f"Silhouette score: {best_result['silhouette_score']:.3f}\n\n")
        
        if cluster_analysis:
            f.write("CLUSTER PROFILES\n")
            f.write("-" * 20 + "\n")
            for cluster_name, analysis in cluster_analysis.items():
                f.write(f"\n{cluster_name}:\n")
                f.write(f"  Size: {analysis['size']} items ({analysis['percentage']:.1f}%)\n")
                f.write(f"  Avg item frequency: {analysis['avg_item_frequency']:.1f}\n")
                f.write(f"  Top F-E-Lines: {analysis['top_fe_lines']}\n")
                f.write(f"  Sample descriptions: {analysis['common_descriptions'][:2]}\n")
                
                if analysis.get('technical_profile'):
                    f.write(f"  Technical profile:\n")
                    for tech, value in analysis['technical_profile'].items():
                        f.write(f"    {tech}: {value:.2f}\n")
    
    print("✅ Exploratory results saved")

def main():
    """Main exploratory clustering analysis"""
    print("🚀 ARTIST EXPLORATORY CLUSTERING ANALYSIS")
    print("=" * 60)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Load data
    df = load_artist_data()
    if df is None:
        return
    
    # Extract comprehensive features
    df_features = extract_comprehensive_features(df)
    
    # Create text features
    text_features = create_text_features(df_features)
    
    # Perform exploratory clustering
    clustering_results = perform_exploratory_clustering(df_features, text_features)
    
    # Analyze best results
    best_result, cluster_analysis = analyze_best_clustering(df_features, clustering_results)
    
    # Create visualizations
    create_advanced_visualizations(df_features, best_result, cluster_analysis, output_folder)
    
    # Save results
    save_exploratory_results(df_features, best_result, cluster_analysis, clustering_results, output_folder)
    
    print(f"\n🎉 Exploratory Clustering Analysis Complete!")
    print("=" * 60)
    if best_result:
        print(f"🏆 Best clustering: {best_result['feature_set']} + {best_result['algorithm']}")
        print(f"📊 {best_result['n_clusters']} clusters with silhouette score {best_result['silhouette_score']:.3f}")
    print(f"📁 Results saved in: {os.path.abspath(output_folder)}")

if __name__ == "__main__":
    main()
