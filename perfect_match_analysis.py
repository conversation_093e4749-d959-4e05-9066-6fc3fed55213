#!/usr/bin/env python3
"""
Perfect Match Analysis
Detailed analysis of what "perfect matches" actually contain across all columns.
"""

import os
import pandas as pd
import pickle

def analyze_perfect_matches():
    print("🔍 PERFECT MATCH DETAILED ANALYSIS")
    print("=" * 50)
    
    # Load the corrected similarity results
    output_folder = 'output'
    similarity_file = os.path.join(output_folder, 'corrected_bom_similarity_analysis.xlsx')
    
    if not os.path.exists(similarity_file):
        print("❌ No corrected similarity results found. Please run corrected_bom_analysis.py first.")
        return
    
    try:
        similarity_df = pd.read_excel(similarity_file, sheet_name='Corrected_Similarities', engine='openpyxl')
        print(f"✅ Loaded {len(similarity_df)} similarity pairs")
        
        # Filter for perfect matches (100% similarity)
        perfect_matches = similarity_df[similarity_df['Similarity_Score'] >= 0.999]
        print(f"🎯 Perfect matches (≥99.9%): {len(perfect_matches)}")
        
        if len(perfect_matches) == 0:
            print("❌ No perfect matches found")
            return
        
        print(f"\n📊 DETAILED ANALYSIS OF PERFECT MATCHES:")
        print("=" * 60)
        
        for i, (_, match) in enumerate(perfect_matches.head(10).iterrows(), 1):
            print(f"\n🔍 PERFECT MATCH #{i}:")
            print(f"   Similarity Score: {match['Similarity_Score']:.3f} ({match['Resemblance_Percentage']:.1f}%)")
            print(f"   Cross-file match: {match['Cross_File_Match']}")
            print(f"   Exact ID match: {match['Exact_ID_Match']}")
            
            print(f"\n   📋 ITEM 1:")
            print(f"      File: {match['File_1']}")
            print(f"      Item ID: '{match['Item_ID_1']}'")
            print(f"      Level: '{match['Level_1']}'")
            print(f"      Sequence: '{match['Sequence_1']}'")
            print(f"      Description: '{match['Description_1']}'")
            
            print(f"\n   📋 ITEM 2:")
            print(f"      File: {match['File_2']}")
            print(f"      Item ID: '{match['Item_ID_2']}'")
            print(f"      Level: '{match['Level_2']}'")
            print(f"      Sequence: '{match['Sequence_2']}'")
            print(f"      Description: '{match['Description_2']}'")
            
            # Detailed comparison
            print(f"\n   🔍 DETAILED COMPARISON:")
            
            # Item ID comparison
            id_match = match['Item_ID_1'] == match['Item_ID_2']
            print(f"      Item ID identical: {id_match}")
            if not id_match:
                print(f"         '{match['Item_ID_1']}' vs '{match['Item_ID_2']}'")
            
            # Level comparison
            level_match = match['Level_1'] == match['Level_2']
            print(f"      Level identical: {level_match}")
            if not level_match:
                print(f"         '{match['Level_1']}' vs '{match['Level_2']}'")
            
            # Sequence comparison
            seq_match = match['Sequence_1'] == match['Sequence_2']
            print(f"      Sequence identical: {seq_match}")
            if not seq_match:
                print(f"         '{match['Sequence_1']}' vs '{match['Sequence_2']}'")
            
            # Description comparison
            desc_match = match['Description_1'].lower().strip() == match['Description_2'].lower().strip()
            print(f"      Description identical: {desc_match}")
            if not desc_match:
                print(f"         '{match['Description_1'][:50]}...' vs")
                print(f"         '{match['Description_2'][:50]}...'")
            
            # File comparison
            file_match = match['File_1'] == match['File_2']
            print(f"      Same file: {file_match}")
            
            # Overall assessment
            all_identical = id_match and level_match and seq_match and desc_match
            print(f"      🎯 ALL ELEMENTS IDENTICAL: {all_identical}")
            
            if all_identical and not file_match:
                print(f"      ✅ TRUE PERFECT CROSS-FILE DUPLICATE")
            elif all_identical and file_match:
                print(f"      ⚠️  SAME ITEM WITHIN SAME FILE (possible data issue)")
            else:
                print(f"      ⚠️  NOT ALL ELEMENTS IDENTICAL (similarity based on algorithm)")
            
            print(f"      Description Similarity: {match['Description_Similarity']:.3f}")
            print(f"      ID Similarity: {match['ID_Similarity']:.3f}")
            
            print("-" * 60)
        
        # Summary statistics
        print(f"\n📊 PERFECT MATCH SUMMARY:")
        print("=" * 30)
        
        # Count different types of perfect matches
        exact_id_matches = perfect_matches['Exact_ID_Match'].sum()
        cross_file_perfect = perfect_matches['Cross_File_Match'].sum()
        same_file_perfect = len(perfect_matches) - cross_file_perfect
        
        print(f"Total perfect matches: {len(perfect_matches)}")
        print(f"Exact ID matches: {exact_id_matches}")
        print(f"Cross-file perfect matches: {cross_file_perfect}")
        print(f"Same-file perfect matches: {same_file_perfect}")
        
        # Check for truly identical items (all columns match)
        truly_identical = 0
        for _, match in perfect_matches.iterrows():
            id_match = match['Item_ID_1'] == match['Item_ID_2']
            level_match = match['Level_1'] == match['Level_2']
            desc_match = match['Description_1'].lower().strip() == match['Description_2'].lower().strip()
            
            if id_match and level_match and desc_match:
                truly_identical += 1
        
        print(f"Truly identical items (all columns): {truly_identical}")
        print(f"Algorithm-based perfect matches: {len(perfect_matches) - truly_identical}")
        
        # Most common perfect match Item IDs
        print(f"\n🔧 Most Common Perfect Match Item IDs:")
        all_perfect_ids = list(perfect_matches['Item_ID_1']) + list(perfect_matches['Item_ID_2'])
        from collections import Counter
        id_counts = Counter(all_perfect_ids)
        
        for item_id, count in id_counts.most_common(10):
            print(f"   '{item_id}': {count} perfect matches")
        
    except Exception as e:
        print(f"❌ Error analyzing perfect matches: {e}")
        import traceback
        traceback.print_exc()

def check_specific_item_across_files():
    """Check how a specific item appears across different files"""
    print(f"\n🔍 CROSS-FILE ITEM ANALYSIS")
    print("=" * 40)
    
    # Load cached BOM data
    cache_folder = 'cache'
    cache_data_file = os.path.join(cache_folder, 'bom_data.pkl')
    
    if not os.path.exists(cache_data_file):
        print("❌ No cached BOM data found")
        return
    
    try:
        with open(cache_data_file, 'rb') as f:
            bom_data = pickle.load(f)
        
        # Let's check a specific Item ID that appears in multiple files
        target_item_id = "U1-330028"  # From our perfect match results
        
        print(f"🔍 Analyzing Item ID: '{target_item_id}' across all files...")
        
        occurrences = []
        for file_name, df in bom_data.items():
            # Check if this item appears in this file
            if 'Item ID' in df.columns:
                # Use the corrected column mapping
                df_corrected = df.copy()
                df_corrected.columns = ['Level', 'Sequence', 'Item_ID', 'Item_Description', 'Level_Numeric']
                
                matches = df_corrected[df_corrected['Item_ID'] == target_item_id]
                
                if len(matches) > 0:
                    for _, row in matches.iterrows():
                        occurrences.append({
                            'file': file_name,
                            'level': row['Level'],
                            'sequence': row['Sequence'],
                            'item_id': row['Item_ID'],
                            'description': row['Item_Description']
                        })
        
        print(f"📊 Found {len(occurrences)} occurrences of '{target_item_id}':")
        
        for i, occ in enumerate(occurrences, 1):
            print(f"\n   {i}. File: {occ['file'][:50]}...")
            print(f"      Level: '{occ['level']}'")
            print(f"      Sequence: '{occ['sequence']}'")
            print(f"      Description: '{occ['description']}'")
        
        # Check if all occurrences are truly identical
        if len(occurrences) > 1:
            print(f"\n🔍 CHECKING IF ALL OCCURRENCES ARE IDENTICAL:")
            
            first_occ = occurrences[0]
            all_identical = True
            
            for occ in occurrences[1:]:
                level_match = first_occ['level'] == occ['level']
                desc_match = first_occ['description'].lower().strip() == occ['description'].lower().strip()
                
                print(f"   Comparing with occurrence {occurrences.index(occ) + 1}:")
                print(f"      Level match: {level_match}")
                print(f"      Description match: {desc_match}")
                
                if not (level_match and desc_match):
                    all_identical = False
            
            print(f"\n   🎯 ALL OCCURRENCES TRULY IDENTICAL: {all_identical}")
        
    except Exception as e:
        print(f"❌ Error in cross-file analysis: {e}")

def main():
    print("🚀 Starting Perfect Match Detailed Analysis")
    print("=" * 60)
    
    # Analyze perfect matches in detail
    analyze_perfect_matches()
    
    # Check specific items across files
    check_specific_item_across_files()
    
    print(f"\n🎉 Perfect Match Analysis Complete!")
    print("=" * 60)
    
    print(f"\n💡 INTERPRETATION GUIDE:")
    print("   ✅ TRUE PERFECT MATCH = All columns identical (Item ID, Level, Description)")
    print("   ⚠️  ALGORITHM PERFECT MATCH = High similarity score but not all columns identical")
    print("   🔍 CROSS-FILE DUPLICATE = Same item appears in multiple BOM files")
    print("   ⚠️  SAME-FILE DUPLICATE = Same item appears multiple times in one file")

if __name__ == "__main__":
    main()
