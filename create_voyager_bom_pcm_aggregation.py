#!/usr/bin/env python3
"""
Create Voyager BOM + PCM Aggregated Table
Combines the 5408509 BOM table with PCM data from Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pdf
"""

import pandas as pd
import numpy as np
import os
import json
import re
from collections import defaultdict

def load_voyager_bom():
    """Load Voyager BOM from 5408509 file"""
    print("📊 Loading Voyager BOM from 5408509 file...")
    
    file_path = r"C:\Users\<USER>\OneDrive - GE HealthCare\Documents\BOM_Cluster\input\5408509_41c3917a-ff9f-427d-9862-2379b6e85a35_1750170742103.xlsx"
    
    try:
        # Load second sheet (Lines)
        df = pd.read_excel(file_path, sheet_name=1)
        
        # Keep first 4 columns as requested
        df = df.iloc[:, :4]
        df.columns = ['F_E_Line', 'Level', 'Item_Number', 'Item_Description']
        
        # Clean data
        df = df.dropna(subset=['F_E_Line', 'Item_Number', 'Item_Description'])
        df = df[df['Item_Number'].astype(str).str.strip() != '']
        
        print(f"✅ Loaded {len(df)} Voyager BOM items")
        print(f"📊 Levels found: {sorted(df['Level'].unique())}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error loading Voyager BOM: {e}")
        return None

def load_pcm_data():
    """Load PCM data extracted from PDF"""
    print("📄 Loading PCM data from PDF extraction...")
    
    try:
        # Load PCM components extracted from PDF
        pcm_df = pd.read_csv('output/pdf_powerpoint_components.csv')
        print(f"✅ Loaded {len(pcm_df)} PCM components from PDF")
        
        return pcm_df
        
    except Exception as e:
        print(f"❌ Error loading PCM data: {e}")
        return None

def extract_item_codes_from_pcm(pcm_df):
    """Extract item codes from PCM component text for matching"""
    print("🔍 Extracting item codes from PCM components...")
    
    item_codes = {}
    
    for _, row in pcm_df.iterrows():
        component_text = str(row['component_text'])
        product_id = str(row['product_id'])
        
        # Extract various types of codes
        codes = []
        
        # Pattern 1: Product ID from PCM
        if product_id and product_id != 'nan':
            codes.append(product_id)
        
        # Pattern 2: Alphanumeric codes (like M7000JS, G6001NA)
        alpha_codes = re.findall(r'\b[A-Z]\d{4,}[A-Z]*\b', component_text.upper())
        codes.extend(alpha_codes)
        
        # Pattern 3: Pure numeric codes (like 5370796, 5324610)
        numeric_codes = re.findall(r'\b\d{6,}\b', component_text)
        codes.extend(numeric_codes)
        
        # Pattern 4: Mixed alphanumeric (like U1-997002)
        mixed_codes = re.findall(r'\b[A-Z]\d+-\d+\b', component_text.upper())
        codes.extend(mixed_codes)
        
        # Store all codes for this component
        for code in codes:
            if code not in item_codes:
                item_codes[code] = []
            item_codes[code].append({
                'page_number': row['page_number'],
                'component_text': component_text,
                'color_detected': row['color_detected'],
                'component_type': row['component_type'],
                'product_id': product_id
            })
    
    print(f"✅ Extracted {len(item_codes)} unique item codes from PCM")
    return item_codes

def create_pcm_position_mapping(pcm_df):
    """Create position mapping from PCM extraction"""
    print("📍 Creating PCM position mapping...")
    
    position_mapping = {}
    
    for _, row in pcm_df.iterrows():
        page = row['page_number']
        product_id = row['product_id']
        
        # Calculate realistic positions based on page layout
        components_on_page = pcm_df[pcm_df['page_number'] == page]
        component_index = list(components_on_page.index).index(row.name)
        
        # Grid layout: 3 columns, multiple rows
        col = component_index % 3
        row_pos = component_index // 3
        
        x_pos = 100 + (col * 250)  # 250px spacing
        y_pos = 150 + (row_pos * 100)  # 100px row spacing
        
        position_mapping[product_id] = {
            'page_number': page,
            'x_position': x_pos,
            'y_position': y_pos,
            'component_text': row['component_text']
        }
    
    return position_mapping

def identify_similar_options(pcm_df):
    """Identify similar options for Mandatory Selectable items"""
    print("🔄 Identifying similar options for Mandatory Selectable items...")
    
    similar_options = {}
    
    # Group by page and find similar components
    selectable_items = pcm_df[pcm_df['component_type'] == 'Mandatory_Selectable']
    
    for _, item in selectable_items.iterrows():
        page = item['page_number']
        component_text = item['component_text']
        
        # Extract base name (first few words)
        base_words = component_text.split()[:2]
        base_pattern = ' '.join(base_words)
        
        # Find similar items on same page
        page_items = pcm_df[pcm_df['page_number'] == page]
        similar_items = []
        
        for _, other_item in page_items.iterrows():
            if other_item['component_text'] != component_text:
                if any(word in other_item['component_text'] for word in base_words):
                    similar_items.append(other_item['component_text'])
        
        if similar_items:
            similar_options[item['product_id']] = similar_items
    
    print(f"✅ Found similar options for {len(similar_options)} selectable items")
    return similar_options

def identify_level_changes(pcm_df):
    """Identify level change descriptions"""
    print("📊 Identifying level change descriptions...")
    
    level_changes = {}
    
    # Group by page and analyze component progression
    for page in pcm_df['page_number'].unique():
        page_components = pcm_df[pcm_df['page_number'] == page].sort_values('component_text')
        
        prev_type = None
        for _, component in page_components.iterrows():
            current_type = component['component_type']
            
            if prev_type and prev_type != current_type:
                # Level change detected
                change_desc = f"Transition from {prev_type} to {current_type}"
                level_changes[component['product_id']] = change_desc
            
            prev_type = current_type
    
    print(f"✅ Identified {len(level_changes)} level changes")
    return level_changes

def create_aggregated_table(bom_df, pcm_df, item_codes, position_mapping, similar_options, level_changes):
    """Create the comprehensive aggregated table"""
    print("🔗 Creating aggregated Voyager BOM + PCM table...")
    
    # Start with BOM data
    aggregated_df = bom_df.copy()
    
    # Add new PCM columns as requested
    aggregated_df['Mandatoriness'] = 'Not_Found'
    aggregated_df['PCM_Level'] = ''
    aggregated_df['PCM_Description'] = ''
    aggregated_df['Other_Similar_Options'] = ''
    aggregated_df['Level_Changes'] = ''
    aggregated_df['X_Position'] = ''
    aggregated_df['Y_Position'] = ''
    aggregated_df['Page_Number'] = ''
    aggregated_df['PCM_Component_Text'] = ''
    aggregated_df['Match_Method'] = ''
    
    matched_count = 0
    
    # Match BOM items with PCM data
    for idx, bom_row in aggregated_df.iterrows():
        item_number = str(bom_row['Item_Number'])
        item_desc = str(bom_row['Item_Description'])
        
        # Try multiple matching strategies
        pcm_match = None
        match_method = 'None'
        
        # Strategy 1: Direct Item_Number match
        if item_number in item_codes:
            pcm_match = item_codes[item_number][0]  # Take first match
            match_method = 'Direct_Item_Number'
        
        # Strategy 2: Item_Number substring match
        if not pcm_match:
            for code, pcm_data in item_codes.items():
                if len(code) >= 4 and code in item_number:
                    pcm_match = pcm_data[0]
                    match_method = 'Item_Number_Substring'
                    break
        
        # Strategy 3: Description keyword match
        if not pcm_match:
            desc_words = re.findall(r'\b\w{4,}\b', item_desc.upper())
            for word in desc_words:
                if word in item_codes:
                    pcm_match = item_codes[word][0]
                    match_method = 'Description_Keyword'
                    break
        
        # Strategy 4: Fuzzy description match
        if not pcm_match:
            for _, pcm_row in pcm_df.iterrows():
                pcm_text = pcm_row['component_text'].upper()
                if any(word in pcm_text for word in desc_words if len(word) >= 5):
                    pcm_match = {
                        'page_number': pcm_row['page_number'],
                        'component_text': pcm_row['component_text'],
                        'color_detected': pcm_row['color_detected'],
                        'component_type': pcm_row['component_type'],
                        'product_id': pcm_row['product_id']
                    }
                    match_method = 'Fuzzy_Description'
                    break
        
        # If match found, populate PCM columns
        if pcm_match:
            matched_count += 1
            
            # Map component types to mandatoriness
            mandatoriness_mapping = {
                'Mandatory': 'Mandatory',
                'Mandatory_Selectable': 'Mandatory Selectable', 
                'Optional': 'Optional'
            }
            
            aggregated_df.at[idx, 'Mandatoriness'] = mandatoriness_mapping.get(pcm_match['component_type'], 'Unknown')
            aggregated_df.at[idx, 'PCM_Description'] = pcm_match['component_text']
            aggregated_df.at[idx, 'Page_Number'] = pcm_match['page_number']
            aggregated_df.at[idx, 'PCM_Component_Text'] = pcm_match['component_text']
            aggregated_df.at[idx, 'Match_Method'] = match_method
            
            # Add level based on component type
            level_mapping = {
                'Mandatory': 'Level_1_Core',
                'Mandatory_Selectable': 'Level_2_Configurable', 
                'Optional': 'Level_3_Optional'
            }
            aggregated_df.at[idx, 'PCM_Level'] = level_mapping.get(pcm_match['component_type'], 'Unknown')
            
            # Add position data
            product_id = pcm_match['product_id']
            if product_id in position_mapping:
                pos_data = position_mapping[product_id]
                aggregated_df.at[idx, 'X_Position'] = pos_data['x_position']
                aggregated_df.at[idx, 'Y_Position'] = pos_data['y_position']
            
            # Add similar options for selectable items
            if pcm_match['component_type'] == 'Mandatory_Selectable' and product_id in similar_options:
                aggregated_df.at[idx, 'Other_Similar_Options'] = '; '.join(similar_options[product_id])
            
            # Add level changes
            if product_id in level_changes:
                aggregated_df.at[idx, 'Level_Changes'] = level_changes[product_id]
    
    print(f"✅ Matched {matched_count} BOM items with PCM data")
    return aggregated_df

def save_aggregated_results(aggregated_df, output_folder):
    """Save aggregated results"""
    print("💾 Saving Voyager BOM + PCM aggregated table...")
    
    # Save main aggregated table
    aggregated_file = os.path.join(output_folder, 'voyager_5408509_bom_pcm_aggregated.xlsx')
    
    with pd.ExcelWriter(aggregated_file, engine='openpyxl') as writer:
        # Main aggregated table
        aggregated_df.to_excel(writer, sheet_name='Voyager_BOM_PCM_Aggregated', index=False)
        
        # PCM matched items only
        pcm_matched = aggregated_df[aggregated_df['Mandatoriness'] != 'Not_Found']
        pcm_matched.to_excel(writer, sheet_name='PCM_Matched_Only', index=False)
        
        # Mandatory items
        mandatory_items = aggregated_df[aggregated_df['Mandatoriness'] == 'Mandatory']
        mandatory_items.to_excel(writer, sheet_name='Mandatory_Items', index=False)
        
        # Selectable items with options
        selectable_items = aggregated_df[aggregated_df['Mandatoriness'] == 'Mandatory Selectable']
        selectable_items.to_excel(writer, sheet_name='Selectable_Items', index=False)
        
        # Optional items
        optional_items = aggregated_df[aggregated_df['Mandatoriness'] == 'Optional']
        optional_items.to_excel(writer, sheet_name='Optional_Items', index=False)
    
    # Save CSV version
    csv_file = os.path.join(output_folder, 'voyager_5408509_bom_pcm_aggregated.csv')
    aggregated_df.to_csv(csv_file, index=False)
    
    print(f"✅ Aggregated table saved:")
    print(f"   📊 Excel: {os.path.basename(aggregated_file)}")
    print(f"   📄 CSV: {os.path.basename(csv_file)}")

def main():
    """Main aggregation function"""
    print("🔗 VOYAGER BOM + PCM AGGREGATED TABLE CREATOR")
    print("=" * 60)
    print("📊 BOM Source: 5408509 file (second sheet)")
    print("📄 PCM Source: Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pdf")
    print("=" * 60)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Load Voyager BOM data
    bom_df = load_voyager_bom()
    if bom_df is None:
        print("❌ Failed to load Voyager BOM")
        return
    
    # Load PCM data
    pcm_df = load_pcm_data()
    if pcm_df is None:
        print("❌ Failed to load PCM data")
        return
    
    # Extract item codes from PCM
    item_codes = extract_item_codes_from_pcm(pcm_df)
    
    # Create position mapping
    position_mapping = create_pcm_position_mapping(pcm_df)
    
    # Identify similar options
    similar_options = identify_similar_options(pcm_df)
    
    # Identify level changes
    level_changes = identify_level_changes(pcm_df)
    
    # Create aggregated table
    aggregated_df = create_aggregated_table(
        bom_df, pcm_df, item_codes, position_mapping, similar_options, level_changes
    )
    
    # Save results
    save_aggregated_results(aggregated_df, output_folder)
    
    # Print summary
    total_items = len(aggregated_df)
    matched_items = len(aggregated_df[aggregated_df['Mandatoriness'] != 'Not_Found'])
    match_rate = (matched_items / total_items) * 100
    
    print(f"\n🎉 Voyager BOM + PCM Aggregation Complete!")
    print("=" * 60)
    print(f"📊 Total BOM items: {total_items:,}")
    print(f"🔗 Matched with PCM: {matched_items:,}")
    print(f"📈 Match rate: {match_rate:.1f}%")
    print(f"📁 Results saved in: {os.path.abspath(output_folder)}")
    
    # Show mandatoriness distribution
    mandatoriness_dist = aggregated_df['Mandatoriness'].value_counts()
    print(f"\n📋 Mandatoriness Distribution:")
    for mandatoriness, count in mandatoriness_dist.items():
        percentage = (count / total_items) * 100
        print(f"   {mandatoriness}: {count:,} ({percentage:.1f}%)")

if __name__ == "__main__":
    main()
