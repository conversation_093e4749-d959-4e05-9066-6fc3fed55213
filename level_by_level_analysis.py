#!/usr/bin/env python3
"""
Level-by-Level BOM Analysis
Analyzes matches by BOM level (0, 1, 2, 3, etc.) to show standardization patterns at each level.
"""

import os
import pandas as pd
from collections import defaultdict, Counter
import re

def clean_description(text):
    if pd.isna(text):
        return ""
    return str(text).lower().strip()

def extract_level_number(level_str):
    """Extract the primary level number from level string"""
    try:
        # Extract first number from level string (e.g., "1-1-2" -> 1, "2" -> 2)
        match = re.search(r'^(\d+)', str(level_str))
        if match:
            return int(match.group(1))
        return None
    except:
        return None

def load_all_bom_data_by_level():
    """Load all BOM data organized by level"""
    print("🔍 Loading all BOM data organized by level...")
    
    input_folder = 'input'
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    
    # Organize items by level
    items_by_level = defaultdict(list)
    total_items = 0
    
    for file_idx, file in enumerate(excel_files, 1):
        print(f"  [{file_idx}/{len(excel_files)}] Processing: {file}")
        file_path = os.path.join(input_folder, file)
        
        try:
            # Read the second sheet
            xls = pd.ExcelFile(file_path)
            if len(xls.sheet_names) < 2:
                print(f"    ⚠️  No second sheet in {file}")
                continue
                
            df = pd.read_excel(file_path, sheet_name=1)
            
            if len(df.columns) < 4:
                print(f"    ⚠️  Not enough columns in {file}")
                continue
            
            # Use corrected column mapping
            df = df.iloc[:, :4]
            df.columns = ['Level', 'Sequence', 'Item_ID', 'Item_Description']
            
            # Clean data
            df = df.dropna(subset=['Level', 'Sequence', 'Item_ID', 'Item_Description'])
            df = df[df['Level'].astype(str).str.strip() != '']
            df = df[df['Sequence'].astype(str).str.strip() != '']
            df = df[df['Item_ID'].astype(str).str.strip() != '']
            df = df[df['Item_Description'].astype(str).str.strip() != '']
            
            # Remove numeric-only Item IDs (sequence numbers)
            df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
            
            # Clean descriptions
            df['Item_Description_Clean'] = df['Item_Description'].apply(clean_description)
            df = df[df['Item_Description_Clean'].str.len() > 0]
            
            # Extract level numbers
            df['Level_Number'] = df['Level'].apply(extract_level_number)
            df = df[df['Level_Number'].notna()]  # Remove rows where level couldn't be extracted
            
            print(f"    ✅ Processed {len(df)} valid items")
            total_items += len(df)
            
            # Organize by level
            for _, row in df.iterrows():
                level_num = int(row['Level_Number'])
                item_info = {
                    'file_name': file,
                    'original_row': df.index.get_loc(_) + 2,  # +2 for Excel header
                    'sheet_name': xls.sheet_names[1],
                    'level': str(row['Level']),
                    'level_number': level_num,
                    'sequence': str(row['Sequence']),
                    'item_id': str(row['Item_ID']).strip(),
                    'item_description': str(row['Item_Description']).strip(),
                    'item_id_clean': str(row['Item_ID']).strip().lower(),
                    'item_description_clean': row['Item_Description_Clean']
                }
                items_by_level[level_num].append(item_info)
                
        except Exception as e:
            print(f"    ❌ Error processing {file}: {e}")
            continue
    
    print(f"✅ Total items loaded: {total_items} from {len(excel_files)} files")
    print(f"📊 Levels found: {sorted(items_by_level.keys())}")
    
    return items_by_level

def analyze_level_matches(level_items, level_number):
    """Analyze matches for a specific level"""
    print(f"\n🔍 Analyzing Level {level_number} ({len(level_items)} items)...")
    
    if len(level_items) < 2:
        return {
            'level': level_number,
            'total_items': len(level_items),
            'perfect_matches': 0,
            'description_matches': 0,
            'cross_file_perfect': 0,
            'cross_file_description': 0,
            'unique_item_ids': 0,
            'unique_descriptions': 0,
            'files_involved': 0
        }
    
    # Count unique elements
    unique_item_ids = len(set(item['item_id_clean'] for item in level_items))
    unique_descriptions = len(set(item['item_description_clean'] for item in level_items))
    files_involved = len(set(item['file_name'] for item in level_items))
    
    # Find perfect matches (same ID + same description)
    perfect_matches = []
    id_desc_groups = defaultdict(list)
    
    for item in level_items:
        key = (item['item_id_clean'], item['item_description_clean'])
        id_desc_groups[key].append(item)
    
    cross_file_perfect = 0
    for (item_id, description), items in id_desc_groups.items():
        if len(items) > 1:
            files = set(item['file_name'] for item in items)
            if len(files) > 1:  # Cross-file matches
                # Count all pairs
                for i in range(len(items)):
                    for j in range(i + 1, len(items)):
                        if items[i]['file_name'] != items[j]['file_name']:
                            perfect_matches.append((items[i], items[j]))
                            cross_file_perfect += 1
    
    # Find description matches (same description + different ID)
    description_matches = []
    desc_groups = defaultdict(list)
    
    for item in level_items:
        if len(item['item_description_clean']) > 10:  # Meaningful descriptions
            desc_groups[item['item_description_clean']].append(item)
    
    cross_file_description = 0
    for description, items in desc_groups.items():
        if len(items) > 1:
            unique_ids = {}
            for item in items:
                if item['item_id_clean'] not in unique_ids:
                    unique_ids[item['item_id_clean']] = []
                unique_ids[item['item_id_clean']].append(item)
            
            if len(unique_ids) > 1:  # Different IDs for same description
                id_list = list(unique_ids.keys())
                for i in range(len(id_list)):
                    for j in range(i + 1, len(id_list)):
                        item1 = unique_ids[id_list[i]][0]
                        item2 = unique_ids[id_list[j]][0]
                        description_matches.append((item1, item2))
                        if item1['file_name'] != item2['file_name']:
                            cross_file_description += 1
    
    return {
        'level': level_number,
        'total_items': len(level_items),
        'perfect_matches': len(perfect_matches),
        'description_matches': len(description_matches),
        'cross_file_perfect': cross_file_perfect,
        'cross_file_description': cross_file_description,
        'unique_item_ids': unique_item_ids,
        'unique_descriptions': unique_descriptions,
        'files_involved': files_involved,
        'perfect_match_examples': perfect_matches[:5],  # Top 5 examples
        'description_match_examples': description_matches[:5]  # Top 5 examples
    }

def create_level_analysis_report(level_results):
    """Create comprehensive level-by-level analysis report"""
    print("\n📊 Creating level-by-level analysis report...")
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Create Excel report
    excel_file = os.path.join(output_folder, 'level_by_level_analysis.xlsx')
    
    # Prepare summary data
    summary_data = []
    for result in level_results:
        summary_data.append({
            'Level': result['level'],
            'Total_Items': result['total_items'],
            'Perfect_Matches': result['perfect_matches'],
            'Description_Matches': result['description_matches'],
            'Cross_File_Perfect_Matches': result['cross_file_perfect'],
            'Cross_File_Description_Matches': result['cross_file_description'],
            'Unique_Item_IDs': result['unique_item_ids'],
            'Unique_Descriptions': result['unique_descriptions'],
            'Files_Involved': result['files_involved'],
            'Perfect_Match_Rate_%': round((result['perfect_matches'] / result['total_items'] * 100), 2) if result['total_items'] > 0 else 0,
            'Description_Match_Rate_%': round((result['description_matches'] / result['total_items'] * 100), 2) if result['total_items'] > 0 else 0,
            'Standardization_Level': 'High' if result['perfect_matches'] > result['total_items'] * 0.1 else 'Medium' if result['perfect_matches'] > 0 else 'Low'
        })
    
    summary_df = pd.DataFrame(summary_data)
    
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # Summary sheet
        summary_df.to_excel(writer, sheet_name='Level_Summary', index=False)
        
        # Detailed breakdown for each level
        for result in level_results:
            if result['perfect_matches'] > 0 or result['description_matches'] > 0:
                level_data = []
                
                # Add perfect match examples
                for i, (item1, item2) in enumerate(result['perfect_match_examples'], 1):
                    level_data.append({
                        'Match_Type': 'Perfect_Match',
                        'Item_ID_1': item1['item_id'],
                        'Item_ID_2': item2['item_id'],
                        'Description_1': item1['item_description'][:100],
                        'Description_2': item2['item_description'][:100],
                        'File_1': item1['file_name'],
                        'File_2': item2['file_name'],
                        'Row_1': item1['original_row'],
                        'Row_2': item2['original_row']
                    })
                
                # Add description match examples
                for i, (item1, item2) in enumerate(result['description_match_examples'], 1):
                    level_data.append({
                        'Match_Type': 'Description_Match',
                        'Item_ID_1': item1['item_id'],
                        'Item_ID_2': item2['item_id'],
                        'Description_1': item1['item_description'][:100],
                        'Description_2': item2['item_description'][:100],
                        'File_1': item1['file_name'],
                        'File_2': item2['file_name'],
                        'Row_1': item1['original_row'],
                        'Row_2': item2['original_row']
                    })
                
                if level_data:
                    level_df = pd.DataFrame(level_data)
                    sheet_name = f'Level_{result["level"]}_Details'
                    level_df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"✅ Excel report saved: {excel_file}")
    
    # Create text summary
    text_file = os.path.join(output_folder, 'level_by_level_summary.txt')
    
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write("LEVEL-BY-LEVEL BOM ANALYSIS SUMMARY\n")
        f.write("=" * 60 + "\n")
        f.write(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Overall summary
        total_items = sum(r['total_items'] for r in level_results)
        total_perfect = sum(r['perfect_matches'] for r in level_results)
        total_description = sum(r['description_matches'] for r in level_results)
        
        f.write("OVERALL SUMMARY\n")
        f.write("=" * 20 + "\n")
        f.write(f"Total Items Analyzed: {total_items:,}\n")
        f.write(f"Total Perfect Matches: {total_perfect:,}\n")
        f.write(f"Total Description Matches: {total_description:,}\n")
        f.write(f"Levels Analyzed: {len(level_results)}\n\n")
        
        # Level-by-level breakdown
        f.write("LEVEL-BY-LEVEL BREAKDOWN\n")
        f.write("=" * 30 + "\n")
        
        for result in sorted(level_results, key=lambda x: x['level']):
            f.write(f"\n📊 LEVEL {result['level']}\n")
            f.write("-" * 20 + "\n")
            f.write(f"Total Items: {result['total_items']:,}\n")
            f.write(f"Perfect Matches (Same ID + Same Description): {result['perfect_matches']:,}\n")
            f.write(f"Description Matches (Same Description + Different ID): {result['description_matches']:,}\n")
            f.write(f"Cross-File Perfect Matches: {result['cross_file_perfect']:,}\n")
            f.write(f"Cross-File Description Matches: {result['cross_file_description']:,}\n")
            f.write(f"Unique Item IDs: {result['unique_item_ids']:,}\n")
            f.write(f"Unique Descriptions: {result['unique_descriptions']:,}\n")
            f.write(f"Files Involved: {result['files_involved']}\n")
            
            # Calculate rates
            perfect_rate = (result['perfect_matches'] / result['total_items'] * 100) if result['total_items'] > 0 else 0
            desc_rate = (result['description_matches'] / result['total_items'] * 100) if result['total_items'] > 0 else 0
            
            f.write(f"Perfect Match Rate: {perfect_rate:.2f}%\n")
            f.write(f"Description Match Rate: {desc_rate:.2f}%\n")
            
            # Standardization assessment
            if result['perfect_matches'] > result['total_items'] * 0.1:
                standardization = "HIGH - Extensive part reuse"
            elif result['perfect_matches'] > 0:
                standardization = "MEDIUM - Some part reuse"
            else:
                standardization = "LOW - Limited part reuse"
            
            f.write(f"Standardization Level: {standardization}\n")
            
            # Examples
            if result['perfect_match_examples']:
                f.write(f"\nTop Perfect Match Examples:\n")
                for i, (item1, item2) in enumerate(result['perfect_match_examples'][:3], 1):
                    f.write(f"  {i}. {item1['item_id']} - {item1['item_description'][:50]}...\n")
                    f.write(f"     Files: {item1['file_name'][:30]}... ↔ {item2['file_name'][:30]}...\n")
            
            if result['description_match_examples']:
                f.write(f"\nTop Description Match Examples:\n")
                for i, (item1, item2) in enumerate(result['description_match_examples'][:3], 1):
                    f.write(f"  {i}. {item1['item_description'][:50]}...\n")
                    f.write(f"     IDs: {item1['item_id']} ↔ {item2['item_id']}\n")
    
    print(f"✅ Text summary saved: {text_file}")

def main():
    print("🚀 Starting Level-by-Level BOM Analysis")
    print("=" * 60)
    print("🎯 Analyzing matches by BOM level (0, 1, 2, 3, etc.)")
    
    # Load all BOM data organized by level
    items_by_level = load_all_bom_data_by_level()
    
    if not items_by_level:
        print("❌ No valid items found!")
        return
    
    # Analyze each level
    level_results = []
    
    for level_number in sorted(items_by_level.keys()):
        level_items = items_by_level[level_number]
        result = analyze_level_matches(level_items, level_number)
        level_results.append(result)
    
    # Create comprehensive report
    create_level_analysis_report(level_results)
    
    print(f"\n🎉 Level-by-Level Analysis Complete!")
    print("=" * 60)
    print(f"📊 Summary by Level:")
    
    for result in sorted(level_results, key=lambda x: x['level']):
        print(f"   Level {result['level']}: {result['total_items']:,} items, "
              f"{result['perfect_matches']:,} perfect matches, "
              f"{result['description_matches']:,} description matches")
    
    print(f"\n📁 Files created:")
    print(f"   • level_by_level_analysis.xlsx - Detailed Excel analysis")
    print(f"   • level_by_level_summary.txt - Human-readable summary")

if __name__ == "__main__":
    main()
