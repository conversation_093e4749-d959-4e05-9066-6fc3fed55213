#!/usr/bin/env python3
"""
Global BOM Similarity Analysis with Caching
Analyzes Level 1, 2, 3 items across all BOM files with hierarchical clustering and dendrogram visualization.
Includes intelligent caching to avoid reprocessing unchanged files.
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
from scipy.spatial.distance import squareform
import matplotlib.pyplot as plt
import traceback
from datetime import datetime
import warnings
import pickle
import hashlib
warnings.filterwarnings('ignore')

def clean_description(text):
    if pd.isna(text):
        return ""
    return str(text).lower().strip()

def get_file_hash(file_path):
    """Generate a hash for a file to detect changes"""
    try:
        with open(file_path, 'rb') as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        return file_hash
    except Exception:
        return None

def save_processed_data(data, cache_file):
    """Save processed data to cache"""
    try:
        with open(cache_file, 'wb') as f:
            pickle.dump(data, f)
        return True
    except Exception as e:
        print(f"    ⚠️  Could not save cache: {e}")
        return False

def load_processed_data(cache_file):
    """Load processed data from cache"""
    try:
        with open(cache_file, 'rb') as f:
            data = pickle.load(f)
        return data
    except Exception:
        return None

def check_cache_validity(input_folder, cache_folder):
    """Check if cached data is still valid by comparing file hashes"""
    cache_info_file = os.path.join(cache_folder, 'cache_info.pkl')
    
    if not os.path.exists(cache_info_file):
        return False, {}
    
    try:
        with open(cache_info_file, 'rb') as f:
            cached_info = pickle.load(f)
    except Exception:
        return False, {}
    
    # Check if all files still exist and have same hash
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    current_info = {}
    
    for file in excel_files:
        file_path = os.path.join(input_folder, file)
        current_info[file] = get_file_hash(file_path)
    
    # Compare with cached info
    if current_info == cached_info:
        return True, current_info
    else:
        return False, current_info

def load_bom_data(file_path):
    """Load and preprocess BOM data from Excel file"""
    try:
        # Read the second sheet (index 1) which contains BOM data
        xls = pd.ExcelFile(file_path)
        if len(xls.sheet_names) < 2:
            print(f"Warning: {file_path} doesn't have a second sheet")
            return None

        df = pd.read_excel(file_path, sheet_name=1)

        # Keep only the first 4 columns
        if len(df.columns) >= 4:
            df = df.iloc[:, :4]
            # Rename columns for consistency
            df.columns = ['Level', 'Item ID', 'F-E-Line', 'Item Description']

            print(f"    📊 Original rows: {len(df)}")

            # Remove rows with ANY empty values in ALL 4 columns
            df_before = len(df)
            df = df.dropna(subset=['Level', 'Item ID', 'F-E-Line', 'Item Description'])
            df_after = len(df)

            # Additional cleaning: remove rows where any column is just whitespace
            df = df[df['Level'].astype(str).str.strip() != '']
            df = df[df['Item ID'].astype(str).str.strip() != '']
            df = df[df['F-E-Line'].astype(str).str.strip() != '']
            df = df[df['Item Description'].astype(str).str.strip() != '']

            df_after_cleaning = len(df)

            print(f"    🧹 After removing incomplete rows: {df_after} (removed {df_before - df_after})")
            print(f"    🧹 After removing empty/whitespace: {df_after_cleaning} (removed {df_after - df_after_cleaning})")

            # Filter to only Level 1, 2, and 3
            df_before_level_filter = len(df)
            # Convert Level to string and extract numeric part for comparison
            df['Level_Numeric'] = df['Level'].astype(str).str.extract('(\d+)').astype(float)
            df = df[df['Level_Numeric'].isin([1.0, 2.0, 3.0])]
            df_after_level_filter = len(df)
            
            print(f"    🎯 After filtering to Level 1, 2 & 3: {df_after_level_filter} (removed {df_before_level_filter - df_after_level_filter})")

            if df_after_level_filter == 0:
                print(f"    ❌ No valid Level 1, 2 or 3 rows found")
                return None

            # Clean and normalize descriptions for better matching
            df['Item Description'] = df['Item Description'].apply(clean_description)

            # Remove rows where description becomes empty after cleaning
            df = df[df['Item Description'].str.len() > 0]

            df_final = len(df)
            if df_final == 0:
                print(f"    ❌ No valid descriptions after cleaning")
                return None

            print(f"    ✅ Final valid Level 1,2&3 rows: {df_final}")
            return df
        else:
            print(f"Warning: {file_path} doesn't have at least 4 columns")
            return None
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def create_global_similarity_matrix(all_bom_data, max_items=50000):
    """Create a global similarity matrix with memory optimization for large datasets"""
    print("\n🌍 Creating global similarity matrix with memory optimization...")

    # Combine all items from all files into one dataset
    all_items = []
    item_metadata = []

    for file_name, df in all_bom_data.items():
        for idx, row in df.iterrows():
            item_info = {
                'file': file_name,
                'item_id': str(row['Item ID']),
                'level': row['Level'],
                'fe_line': str(row['F-E-Line']),
                'description': str(row['Item Description']),
                'level_numeric': row['Level_Numeric'],
                'original_index': idx
            }
            all_items.append(item_info)
            item_metadata.append(item_info)

    print(f"    📊 Total items across all files: {len(all_items)}")

    if len(all_items) < 2:
        print("    ❌ Need at least 2 items for global analysis")
        return None, None, None

    # Memory optimization: If dataset is too large, use intelligent sampling
    if len(all_items) > max_items:
        print(f"    🔧 Dataset too large ({len(all_items)} items). Applying intelligent sampling...")

        # Strategy 1: Remove exact duplicates first
        unique_items = {}
        for item in all_items:
            key = (item['item_id'].lower().strip(), item['description'].lower().strip())
            if key not in unique_items:
                unique_items[key] = item
            else:
                # Keep the one from the file with more items (likely more complete)
                existing_file_size = sum(1 for f_item in all_items if f_item['file'] == unique_items[key]['file'])
                current_file_size = sum(1 for f_item in all_items if f_item['file'] == item['file'])
                if current_file_size > existing_file_size:
                    unique_items[key] = item

        deduplicated_items = list(unique_items.values())
        print(f"    🔄 After deduplication: {len(deduplicated_items)} items (removed {len(all_items) - len(deduplicated_items)} duplicates)")

        # Strategy 2: If still too large, sample strategically
        if len(deduplicated_items) > max_items:
            print(f"    🎯 Still too large. Applying strategic sampling to {max_items} items...")

            # Sample proportionally from each file and level
            file_samples = {}
            for item in deduplicated_items:
                file_key = item['file']
                if file_key not in file_samples:
                    file_samples[file_key] = []
                file_samples[file_key].append(item)

            # Calculate sample size per file proportionally
            sampled_items = []
            items_per_file = max_items // len(file_samples)

            for file_key, file_items in file_samples.items():
                if len(file_items) <= items_per_file:
                    sampled_items.extend(file_items)
                else:
                    # Sample evenly across levels within each file
                    level_groups = {}
                    for item in file_items:
                        level = item['level_numeric']
                        if level not in level_groups:
                            level_groups[level] = []
                        level_groups[level].append(item)

                    items_per_level = items_per_file // len(level_groups)
                    for level, level_items in level_groups.items():
                        if len(level_items) <= items_per_level:
                            sampled_items.extend(level_items)
                        else:
                            # Random sample from this level
                            import random
                            sampled_items.extend(random.sample(level_items, items_per_level))

            all_items = sampled_items
            item_metadata = sampled_items
            print(f"    ✅ Strategic sampling complete: {len(all_items)} items selected")
        else:
            all_items = deduplicated_items
            item_metadata = deduplicated_items

    # Extract features for similarity calculation
    item_ids = [item['item_id'] for item in all_items]
    descriptions = [item['description'] for item in all_items]
    fe_lines = [item['fe_line'] for item in all_items]

    print(f"    📊 Processing {len(all_items)} items for similarity analysis...")

    # Memory-efficient similarity calculation using chunked processing
    print("    🔍 Calculating similarities with chunked processing...")

    # Use more restrictive TF-IDF parameters for large datasets
    desc_vectorizer = TfidfVectorizer(
        min_df=2, max_df=0.8, ngram_range=(1, 2),
        stop_words='english', lowercase=True, strip_accents='unicode',
        max_features=10000  # Limit features to reduce memory
    )

    id_vectorizer = TfidfVectorizer(
        min_df=1, analyzer='char_wb', ngram_range=(2, 3), lowercase=True,
        max_features=5000  # Limit features for IDs
    )

    try:
        desc_tfidf_matrix = desc_vectorizer.fit_transform(descriptions)
        id_tfidf_matrix = id_vectorizer.fit_transform(item_ids)
        print(f"    ✅ TF-IDF matrices created: {desc_tfidf_matrix.shape}, {id_tfidf_matrix.shape}")

        # For very large datasets, we'll create a sparse similarity matrix
        # Only store similarities above a threshold to save memory
        print("    🔍 Creating sparse similarity matrix...")

        n_items = len(all_items)
        similarity_threshold = 0.3  # Only store similarities above this threshold

        # Process in chunks to avoid memory issues
        chunk_size = min(1000, n_items // 10 + 1)
        similarity_data = []

        for i in range(0, n_items, chunk_size):
            end_i = min(i + chunk_size, n_items)

            # Calculate similarities for this chunk
            desc_chunk_sim = cosine_similarity(desc_tfidf_matrix[i:end_i], desc_tfidf_matrix)
            id_chunk_sim = cosine_similarity(id_tfidf_matrix[i:end_i], id_tfidf_matrix)

            for local_i, global_i in enumerate(range(i, end_i)):
                for j in range(n_items):
                    if global_i == j:
                        continue  # Skip self-similarity

                    desc_sim = desc_chunk_sim[local_i, j]
                    id_sim = id_chunk_sim[local_i, j]

                    # Quick filtering - only process if base similarity is reasonable
                    if desc_sim < 0.1 and id_sim < 0.1:
                        continue

                    # Check exact matches
                    exact_id_match = item_ids[global_i].lower().strip() == item_ids[j].lower().strip()
                    exact_fe_match = fe_lines[global_i].lower().strip() == fe_lines[j].lower().strip()

                    # Combined score with weights
                    combined_score = (desc_sim * 0.5) + (id_sim * 0.35)
                    if exact_id_match:
                        combined_score += 0.3
                    if exact_fe_match:
                        combined_score += 0.15
                    combined_score = min(combined_score, 1.0)

                    # Only store if above threshold
                    if combined_score >= similarity_threshold:
                        similarity_data.append((global_i, j, combined_score))

            if (i // chunk_size + 1) % 10 == 0:
                print(f"    📊 Processed {end_i}/{n_items} items...")

        print(f"    ✅ Sparse similarity calculation complete: {len(similarity_data)} significant similarities found")

        # Create full similarity matrix for clustering (with zeros for low similarities)
        combined_similarity_matrix = np.zeros((n_items, n_items))
        np.fill_diagonal(combined_similarity_matrix, 1.0)  # Self-similarity = 1

        for i, j, sim in similarity_data:
            combined_similarity_matrix[i, j] = sim
            combined_similarity_matrix[j, i] = sim  # Make symmetric

        # Convert similarity to distance for clustering
        distance_matrix = 1 - combined_similarity_matrix

        print(f"    ✅ Global similarity matrix created: {n_items}x{n_items} (sparse)")
        return combined_similarity_matrix, item_metadata, distance_matrix

    except MemoryError as e:
        print(f"    ❌ Memory error during similarity calculation: {e}")
        print(f"    💡 Try reducing max_items parameter (current: {max_items})")
        return None, None, None
    except Exception as e:
        print(f"    ❌ Error during similarity calculation: {e}")
        return None, None, None

def create_similarity_clusters(similarity_matrix, item_metadata, distance_matrix, threshold=0.7):
    """Create hierarchical clusters of similar items and generate dendrogram"""
    print(f"\n🌳 Creating similarity clusters (threshold: {threshold})...")

    n_items = len(item_metadata)
    if n_items < 2:
        return {}, None

    # Perform hierarchical clustering
    print("    🔗 Performing hierarchical clustering...")

    # Convert distance matrix to condensed form for linkage
    condensed_distances = squareform(distance_matrix, checks=False)

    # Create linkage matrix using Ward method
    linkage_matrix = linkage(condensed_distances, method='ward')

    # Create clusters based on threshold
    cluster_labels = fcluster(linkage_matrix, 1-threshold, criterion='distance')

    # Group items by cluster
    clusters = {}
    for i, cluster_id in enumerate(cluster_labels):
        if cluster_id not in clusters:
            clusters[cluster_id] = []
        clusters[cluster_id].append({
            'index': i,
            'metadata': item_metadata[i],
            'cluster_id': cluster_id
        })

    # Filter clusters to only include those with multiple items
    significant_clusters = {k: v for k, v in clusters.items() if len(v) > 1}

    print(f"    📊 Found {len(significant_clusters)} clusters with multiple items")
    print(f"    📊 Total clustered items: {sum(len(cluster) for cluster in significant_clusters.values())}")

    # Create dendrogram
    print("    🌳 Creating dendrogram...")
    try:
        plt.figure(figsize=(20, 12))
        dendrogram(
            linkage_matrix,
            labels=[f"{item['item_id'][:15]}..." for item in item_metadata],
            leaf_rotation=90,
            leaf_font_size=6
        )
        plt.title('BOM Items Similarity Dendrogram\n(Based on Item ID, Description, and F-E-Line)', fontsize=16)
        plt.xlabel('Items', fontsize=14)
        plt.ylabel('Distance (1 - Similarity)', fontsize=14)
        plt.tight_layout()

        # Save dendrogram
        output_folder = 'output'
        os.makedirs(output_folder, exist_ok=True)
        plt.savefig(os.path.join(output_folder, 'bom_similarity_dendrogram.png'), dpi=300, bbox_inches='tight')
        plt.close()
        print(f"    ✅ Dendrogram saved: bom_similarity_dendrogram.png")

    except Exception as e:
        print(f"    ⚠️  Could not create dendrogram: {e}")

    return significant_clusters, linkage_matrix

def create_global_similarity_dataframe(similarity_matrix, item_metadata, clusters, threshold=0.6):
    """Create a comprehensive DataFrame with all similar items and their resemblance percentages"""
    print(f"\n📊 Creating global similarity DataFrame (threshold: {threshold})...")

    similarity_records = []
    n_items = len(item_metadata)

    # Create records for all item pairs above threshold
    for i in range(n_items):
        for j in range(i + 1, n_items):  # Only upper triangle to avoid duplicates
            similarity_score = similarity_matrix[i, j]

            if similarity_score >= threshold:
                item1 = item_metadata[i]
                item2 = item_metadata[j]

                # Find cluster information
                cluster_id = None
                for cid, cluster_items in clusters.items():
                    cluster_indices = [item['index'] for item in cluster_items]
                    if i in cluster_indices and j in cluster_indices:
                        cluster_id = cid
                        break

                record = {
                    'Item_ID_1': item1['item_id'],
                    'File_1': item1['file'],
                    'Level_1': item1['level'],
                    'FE_Line_1': item1['fe_line'],
                    'Description_1': item1['description'],
                    'Item_ID_2': item2['item_id'],
                    'File_2': item2['file'],
                    'Level_2': item2['level'],
                    'FE_Line_2': item2['fe_line'],
                    'Description_2': item2['description'],
                    'Similarity_Score': round(similarity_score, 4),
                    'Resemblance_Percentage': round(similarity_score * 100, 2),
                    'Cluster_ID': cluster_id,
                    'Same_File': item1['file'] == item2['file'],
                    'Same_Level': item1['level'] == item2['level'],
                    'Exact_ID_Match': item1['item_id'].lower().strip() == item2['item_id'].lower().strip(),
                    'Exact_FE_Match': item1['fe_line'].lower().strip() == item2['fe_line'].lower().strip(),
                    'Cross_File_Match': item1['file'] != item2['file']
                }
                similarity_records.append(record)

    similarity_df = pd.DataFrame(similarity_records)

    if len(similarity_df) > 0:
        # Sort by similarity score descending
        similarity_df = similarity_df.sort_values('Similarity_Score', ascending=False)
        print(f"    ✅ Created similarity DataFrame with {len(similarity_df)} similar item pairs")
        print(f"    📊 Cross-file matches: {similarity_df['Cross_File_Match'].sum()}")
        print(f"    📊 Same-file matches: {(~similarity_df['Cross_File_Match']).sum()}")
    else:
        print(f"    ⚠️  No similar items found above threshold {threshold}")

    return similarity_df

def main():
    print("🚀 Starting Global BOM Similarity Analysis with Caching")
    print("=" * 70)
    print(f"⏰ Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Analysis Focus: Level 1, 2, 3 items with global clustering and dendrogram")

    input_folder = 'input'
    output_folder = 'output'
    cache_folder = 'cache'

    # Check if input folder exists
    if not os.path.exists(input_folder):
        print(f"❌ Error: Input folder '{input_folder}' not found!")
        print("Please create the 'input' folder and place your Excel files there.")
        return

    # Create folders if they don't exist
    os.makedirs(output_folder, exist_ok=True)
    os.makedirs(cache_folder, exist_ok=True)
    print(f"📁 Output folder: {os.path.abspath(output_folder)}")
    print(f"💾 Cache folder: {os.path.abspath(cache_folder)}")

    # Get all Excel files
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    print(f"📊 Found {len(excel_files)} Excel files to analyze")

    if len(excel_files) == 0:
        print("❌ No Excel files found in the input folder!")
        print("Please add .xlsx or .xls files to the 'input' folder.")
        return

    # Check cache validity
    print("\n🔍 Checking cache validity...")
    cache_valid, file_info = check_cache_validity(input_folder, cache_folder)
    cache_data_file = os.path.join(cache_folder, 'bom_data.pkl')

    if cache_valid and os.path.exists(cache_data_file):
        print("✅ Cache is valid! Loading preprocessed data...")
        bom_data = load_processed_data(cache_data_file)
        if bom_data is not None:
            print(f"📈 Loaded {len(bom_data)} files from cache")
            total_items = sum(len(df) for df in bom_data.values())
            print(f"📊 Total Level 1,2,3 items: {total_items}")
        else:
            print("⚠️  Cache corrupted, will reload from files")
            cache_valid = False

    if not cache_valid:
        print("🔄 Loading and preprocessing BOM data from files...")
        bom_data = {}
        failed_files = []

        for i, file in enumerate(excel_files, 1):
            print(f"  [{i}/{len(excel_files)}] Loading: {file}")
            file_path = os.path.join(input_folder, file)

            try:
                df = load_bom_data(file_path)
                if df is not None:
                    bom_data[file] = df
                    print(f"    ✅ Loaded {len(df)} Level 1,2,3 items")
                else:
                    failed_files.append(file)
                    print(f"    ❌ Failed to load (check file format)")
            except Exception as e:
                failed_files.append(file)
                print(f"    ❌ Error: {str(e)}")

        print(f"\n📈 Successfully loaded {len(bom_data)} files")
        if failed_files:
            print(f"⚠️  Failed to load {len(failed_files)} files: {failed_files}")

        # Save to cache
        print("💾 Saving preprocessed data to cache...")
        if save_processed_data(bom_data, cache_data_file):
            # Save file info for cache validation
            cache_info_file = os.path.join(cache_folder, 'cache_info.pkl')
            save_processed_data(file_info, cache_info_file)
            print("✅ Data cached successfully!")
        else:
            print("⚠️  Could not save to cache, but continuing with analysis...")

    if len(bom_data) < 1:
        print("❌ Need at least 1 file with valid data to perform analysis!")
        return

    # GLOBAL ANALYSIS
    print(f"\n🌍 GLOBAL SIMILARITY ANALYSIS")
    print("=" * 50)

    # Create global similarity matrix with memory optimization
    # Start with smaller dataset for testing, can be increased later
    max_items_for_analysis = 20000  # Adjust this based on available memory
    similarity_matrix, item_metadata, distance_matrix = create_global_similarity_matrix(bom_data, max_items=max_items_for_analysis)

    if similarity_matrix is None:
        print("❌ Could not create global similarity matrix!")
        return

    # Create similarity clusters and dendrogram
    clusters, _ = create_similarity_clusters(similarity_matrix, item_metadata, distance_matrix, threshold=0.7)

    # Create comprehensive similarity DataFrame
    similarity_df = create_global_similarity_dataframe(similarity_matrix, item_metadata, clusters, threshold=0.6)

    # Generate comprehensive reports
    print(f"\n📝 Generating global analysis reports...")

    # Create cluster summary
    cluster_summary = []
    for cluster_id, cluster_items in clusters.items():
        cluster_info = {
            'Cluster_ID': cluster_id,
            'Item_Count': len(cluster_items),
            'Files_Involved': len(set(item['metadata']['file'] for item in cluster_items)),
            'Levels_Involved': list(set(item['metadata']['level'] for item in cluster_items)),
            'Sample_Items': [item['metadata']['item_id'] for item in cluster_items[:3]]  # First 3 items as sample
        }
        cluster_summary.append(cluster_info)

    cluster_summary_df = pd.DataFrame(cluster_summary)
    if len(cluster_summary_df) > 0:
        cluster_summary_df = cluster_summary_df.sort_values('Item_Count', ascending=False)

    # Generate text summary report
    with open(os.path.join(output_folder, 'global_similarity_summary.txt'), 'w', encoding='utf-8') as f:
        f.write("Global BOM Similarity Analysis Summary - Level 1,2,3 Multi-Column Analysis\n")
        f.write("=" * 80 + "\n")
        f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Analysis Method: Global Multi-Column TF-IDF + Hierarchical Clustering\n")
        f.write(f"Data Requirement: Complete data in all 4 columns + Level 1, 2, or 3 only\n")
        f.write(f"Similarity Focus: Item ID (35%) + Item Description (50%) + F-E-Line bonus (15%)\n")
        f.write(f"Clustering Threshold: 0.7 | Similarity Threshold: 0.6\n\n")

        # Global statistics
        f.write("Global Analysis Statistics\n")
        f.write("=" * 30 + "\n")
        f.write(f"Total files analyzed: {len(bom_data)}\n")
        f.write(f"Total items analyzed: {len(item_metadata)}\n")
        f.write(f"Total similarity pairs found: {len(similarity_df) if similarity_df is not None else 0}\n")
        f.write(f"Total clusters found: {len(clusters)}\n")
        f.write(f"Items in clusters: {sum(len(cluster) for cluster in clusters.values())}\n\n")

        # Cluster summary
        if len(cluster_summary_df) > 0:
            f.write("Cluster Summary (Top 10 by size)\n")
            f.write("=" * 35 + "\n")
            for _, cluster in cluster_summary_df.head(10).iterrows():
                f.write(f"Cluster {cluster['Cluster_ID']}: {cluster['Item_Count']} items\n")
                f.write(f"  Files involved: {cluster['Files_Involved']}\n")
                f.write(f"  Levels: {cluster['Levels_Involved']}\n")
                f.write(f"  Sample items: {', '.join(cluster['Sample_Items'])}\n\n")

        # Top similarity pairs
        if similarity_df is not None and len(similarity_df) > 0:
            f.write("Top 20 Similar Item Pairs\n")
            f.write("=" * 25 + "\n")
            top_pairs = similarity_df.head(20)
            for i, (_, pair) in enumerate(top_pairs.iterrows(), 1):
                f.write(f"{i:2d}. {pair['Item_ID_1']} ↔ {pair['Item_ID_2']} ({pair['Resemblance_Percentage']:.1f}%)\n")
                f.write(f"    Files: {pair['File_1']} ↔ {pair['File_2']}\n")
                f.write(f"    Levels: {pair['Level_1']} ↔ {pair['Level_2']}\n")
                f.write(f"    Descriptions: {pair['Description_1'][:50]}... ↔ {pair['Description_2'][:50]}...\n")
                f.write(f"    Cross-file: {pair['Cross_File_Match']} | Cluster: {pair['Cluster_ID']}\n\n")

    # Save Excel reports
    try:
        with pd.ExcelWriter(os.path.join(output_folder, 'global_bom_similarity_analysis.xlsx'), engine='openpyxl') as writer:
            # Global similarity DataFrame
            if similarity_df is not None and len(similarity_df) > 0:
                similarity_df.to_excel(writer, sheet_name='Global_Similarities', index=False)

            # Cluster summary
            if len(cluster_summary_df) > 0:
                cluster_summary_df.to_excel(writer, sheet_name='Cluster_Summary', index=False)

            # File summary
            file_summary = []
            for file_name, df in bom_data.items():
                file_info = {
                    'File_Name': file_name,
                    'Total_Items': len(df),
                    'Level_1_Items': len(df[df['Level_Numeric'] == 1.0]),
                    'Level_2_Items': len(df[df['Level_Numeric'] == 2.0]),
                    'Level_3_Items': len(df[df['Level_Numeric'] == 3.0])
                }
                file_summary.append(file_info)

            file_summary_df = pd.DataFrame(file_summary)
            file_summary_df.to_excel(writer, sheet_name='File_Summary', index=False)

        print(f"\n📊 Excel reports saved: global_bom_similarity_analysis.xlsx")
        print(f"    📋 Global_Similarities sheet: All similar item pairs with resemblance percentages")
        print(f"    📋 Cluster_Summary sheet: Hierarchical clusters of similar items")
        print(f"    📋 File_Summary sheet: Overview of each BOM file")

    except Exception as e:
        print(f"\n❌ Error saving Excel report: {str(e)}")
        traceback.print_exc()

    # Final summary
    print(f"\n🎉 Global BOM Similarity Analysis Complete!")
    print("=" * 60)
    print(f"⏰ End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Reports saved to: {os.path.abspath(output_folder)}")
    print(f"📊 Total files analyzed: {len(bom_data)}")
    print(f"📊 Total Level 1,2,3 items: {len(item_metadata)}")
    print(f"🎯 Total similarity pairs found: {len(similarity_df) if similarity_df is not None else 0}")
    print(f"🌳 Total clusters found: {len(clusters)}")
    print("\n📋 Generated files:")
    print("  • global_similarity_summary.txt - Human-readable global analysis summary")
    print("  • global_bom_similarity_analysis.xlsx - Comprehensive Excel report")
    print("  • bom_similarity_dendrogram.png - Hierarchical similarity tree visualization")

    if similarity_df is not None and len(similarity_df) > 0:
        print(f"\n🏆 Top 5 most similar items found (by resemblance percentage):")
        top_matches = similarity_df.head(5)
        for i, (_, match) in enumerate(top_matches.iterrows(), 1):
            cross_file = " [Cross-File]" if match['Cross_File_Match'] else " [Same-File]"
            cluster_info = f" [Cluster {match['Cluster_ID']}]" if match['Cluster_ID'] is not None else ""
            print(f"  {i}. {match['Item_ID_1']} ↔ {match['Item_ID_2']} ({match['Resemblance_Percentage']:.1f}%){cross_file}{cluster_info}")
    else:
        print("\n⚠️  No similar Level 1,2,3 items found with current similarity threshold (0.6)")
        print("   Consider lowering the threshold in the code if needed.")

if __name__ == "__main__":
    main()
