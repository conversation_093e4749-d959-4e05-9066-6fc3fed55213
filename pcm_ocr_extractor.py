#!/usr/bin/env python3
"""
PCM OCR Parameter Extractor
Extracts items, specifications, dimensions, compatibility, and figures from image-based PCM documents
"""

import fitz  # PyMuPDF
import pandas as pd
import re
import json
import cv2
import numpy as np
from PIL import Image
import os

# OCR import with fallback
try:
    import pytesseract
    # Set Tesseract path for Windows
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("Warning: pytesseract not available. OCR functionality disabled.")

class PCMOCRExtractor:
    def __init__(self):
        """
        Initialize PCM OCR extractor
        """
        self.ocr_config = '--oem 3 --psm 6 -l eng'
        
        # Define extraction patterns
        self.item_patterns = [
            r'\b[A-Z]\d{4,6}[A-Z]{0,2}\b',
            r'\bP/N\s*[:\-]?\s*([A-Z0-9\-]+)\b',
            r'\bModel\s*[:\-]?\s*([A-Z0-9\-]+)\b',
            r'\bSerial\s*[:\-]?\s*([A-Z0-9\-]+)\b'
        ]
        
        self.spec_patterns = [
            r'(\d+\.?\d*)\s*(mm|cm|m|inch|in)\b',
            r'(\d+\.?\d*)\s*(kg|lbs|g)\b',
            r'(\d+\.?\d*)\s*(V|volt|A|amp|W|watt)\b',
            r'(\d+\.?\d*)\s*(T|tesla|MHz|Hz|kHz)\b',
            r'(\d+\.?\d*)\s*(°C|°F|celsius|fahrenheit)\b'
        ]
        
        self.compatibility_keywords = [
            'compatible', 'compatibility', 'works with', 'supports',
            'requires', 'optional', 'upgrade', 'accessory', 'standard',
            'available', 'included', 'separate'
        ]
        
        self.figure_patterns = [
            r'Figure\s+(\d+)',
            r'Fig\.\s*(\d+)',
            r'Image\s+(\d+)',
            r'Diagram\s+(\d+)',
            r'Table\s+(\d+)'
        ]
    
    def preprocess_image_for_ocr(self, image_array):
        """
        Preprocess image for better OCR results
        """
        # Convert to grayscale
        if len(image_array.shape) == 3:
            gray = cv2.cvtColor(image_array, cv2.COLOR_BGR2GRAY)
        else:
            gray = image_array
        
        # Enhance contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # Denoise
        denoised = cv2.medianBlur(enhanced, 3)
        
        # Threshold
        _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return binary
    
    def extract_text_from_page(self, page):
        """
        Extract text from PDF page using OCR
        """
        if not OCR_AVAILABLE:
            return page.get_text()  # Fallback to direct text extraction
        
        try:
            # Try direct text extraction first
            direct_text = page.get_text()
            if direct_text.strip():
                return direct_text
            
            # Use OCR for image-based content
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better OCR
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.samples
            img = np.frombuffer(img_data, dtype=np.uint8).reshape(pix.h, pix.w, pix.n)
            
            # Convert to RGB
            if pix.n == 4:  # RGBA
                img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)
            elif pix.n == 3:  # RGB
                img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
            
            # Preprocess for OCR
            processed_img = self.preprocess_image_for_ocr(img)
            
            # OCR
            text = pytesseract.image_to_string(processed_img, config=self.ocr_config)
            return text
            
        except Exception as e:
            print(f"OCR error on page: {e}")
            return ""
    
    def extract_items(self, text):
        """
        Extract item codes and product identifiers
        """
        items = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            for pattern in self.item_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    items.append({
                        'item_code': match.group(0),
                        'line_number': line_num + 1,
                        'context': line,
                        'pattern_type': self.classify_item_pattern(pattern)
                    })
        
        return items
    
    def classify_item_pattern(self, pattern):
        """
        Classify item pattern type
        """
        if 'P/N' in pattern:
            return 'part_number'
        elif 'Model' in pattern:
            return 'model_number'
        elif 'Serial' in pattern:
            return 'serial_number'
        else:
            return 'product_code'
    
    def extract_specifications(self, text):
        """
        Extract technical specifications and dimensions
        """
        specifications = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            for pattern in self.spec_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    specifications.append({
                        'value': match.group(1),
                        'unit': match.group(2),
                        'full_match': match.group(0),
                        'line_number': line_num + 1,
                        'context': line,
                        'spec_type': self.classify_specification(match.group(2))
                    })
        
        return specifications
    
    def classify_specification(self, unit):
        """
        Classify specification type based on unit
        """
        unit_lower = unit.lower()
        
        if unit_lower in ['mm', 'cm', 'm', 'inch', 'in']:
            return 'dimension'
        elif unit_lower in ['kg', 'lbs', 'g']:
            return 'weight'
        elif unit_lower in ['v', 'volt', 'a', 'amp', 'w', 'watt']:
            return 'electrical'
        elif unit_lower in ['t', 'tesla', 'mhz', 'hz', 'khz']:
            return 'magnetic_frequency'
        elif unit_lower in ['°c', '°f', 'celsius', 'fahrenheit']:
            return 'temperature'
        else:
            return 'other'
    
    def extract_compatibility_info(self, text):
        """
        Extract compatibility and relationship information
        """
        compatibility = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            line_lower = line.lower()
            
            for keyword in self.compatibility_keywords:
                if keyword in line_lower:
                    compatibility.append({
                        'keyword': keyword,
                        'line_number': line_num + 1,
                        'context': line.strip(),
                        'compatibility_type': self.classify_compatibility(keyword)
                    })
        
        return compatibility
    
    def classify_compatibility(self, keyword):
        """
        Classify compatibility type
        """
        if keyword in ['compatible', 'compatibility', 'works with']:
            return 'compatible_with'
        elif keyword in ['supports']:
            return 'supports'
        elif keyword in ['requires']:
            return 'requires'
        elif keyword in ['optional', 'accessory']:
            return 'optional'
        elif keyword in ['upgrade']:
            return 'upgrade'
        elif keyword in ['standard', 'included']:
            return 'standard'
        elif keyword in ['available', 'separate']:
            return 'available'
        else:
            return 'other'
    
    def extract_figures(self, text):
        """
        Extract figure references and names
        """
        figures = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            for pattern in self.figure_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    figures.append({
                        'figure_number': match.group(1),
                        'figure_reference': match.group(0),
                        'line_number': line_num + 1,
                        'context': line.strip(),
                        'figure_name': self.extract_figure_name(line, match)
                    })
        
        return figures
    
    def extract_figure_name(self, line, match):
        """
        Extract figure name from context
        """
        remaining_text = line[match.end():].strip()
        remaining_text = re.sub(r'^[:\-\.\s]+', '', remaining_text)
        
        if '.' in remaining_text:
            return remaining_text.split('.')[0]
        else:
            return remaining_text[:100] if len(remaining_text) > 100 else remaining_text
    
    def process_document(self, pdf_path, max_pages=None):
        """
        Process document with OCR capability
        """
        print(f"Processing document: {pdf_path}")
        
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        process_pages = min(max_pages, total_pages) if max_pages else total_pages
        
        print(f"Document has {total_pages} pages")
        print(f"Processing {process_pages} pages")
        
        all_text = ""
        page_results = []
        
        for page_num in range(process_pages):
            print(f"Processing page {page_num + 1}/{process_pages}")
            
            page = doc.load_page(page_num)
            page_text = self.extract_text_from_page(page)
            
            page_info = {
                'page_number': page_num + 1,
                'text_length': len(page_text),
                'has_content': len(page_text.strip()) > 0
            }
            
            page_results.append(page_info)
            all_text += page_text + "\n"
        
        doc.close()
        
        print(f"Total text extracted: {len(all_text)} characters")
        
        # Extract parameters
        items = self.extract_items(all_text)
        specifications = self.extract_specifications(all_text)
        compatibility = self.extract_compatibility_info(all_text)
        figures = self.extract_figures(all_text)
        
        results = {
            'document_info': {
                'file_path': pdf_path,
                'total_pages': total_pages,
                'processed_pages': process_pages,
                'ocr_enabled': OCR_AVAILABLE,
                'total_text_length': len(all_text)
            },
            'page_results': page_results,
            'extracted_items': items,
            'specifications': specifications,
            'compatibility_info': compatibility,
            'figures': figures,
            'summary': {
                'total_items': len(items),
                'total_specifications': len(specifications),
                'total_compatibility_refs': len(compatibility),
                'total_figures': len(figures)
            }
        }
        
        return results
    
    def export_results(self, results, output_folder='output'):
        """
        Export results to CSV files
        """
        os.makedirs(output_folder, exist_ok=True)
        
        # Export complete results
        with open(f'{output_folder}/pcm_ocr_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Create summary table
        summary_data = []
        
        for item in results['extracted_items']:
            summary_data.append({
                'Type': 'Item',
                'Value': item['item_code'],
                'Context': item['context'][:100],
                'Line': item['line_number'],
                'Classification': item['pattern_type']
            })
        
        for spec in results['specifications']:
            summary_data.append({
                'Type': 'Specification',
                'Value': f"{spec['value']} {spec['unit']}",
                'Context': spec['context'][:100],
                'Line': spec['line_number'],
                'Classification': spec['spec_type']
            })
        
        for compat in results['compatibility_info']:
            summary_data.append({
                'Type': 'Compatibility',
                'Value': compat['keyword'],
                'Context': compat['context'][:100],
                'Line': compat['line_number'],
                'Classification': compat['compatibility_type']
            })
        
        for figure in results['figures']:
            summary_data.append({
                'Type': 'Figure',
                'Value': figure['figure_reference'],
                'Context': figure['figure_name'][:100],
                'Line': figure['line_number'],
                'Classification': 'figure_reference'
            })
        
        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_csv(f'{output_folder}/pcm_ocr_summary.csv', index=False)
        
        print(f"Results exported to {output_folder}/")

def main():
    """
    Main execution with OCR capability
    """
    print("PCM OCR Parameter Extraction")
    print(f"OCR Available: {OCR_AVAILABLE}")
    
    extractor = PCMOCRExtractor()
    
    pdf_path = r"input\PIM\3.0T Signa PET MR.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"File not found: {pdf_path}")
        return
    
    # Process first 10 pages for testing
    results = extractor.process_document(pdf_path, max_pages=10)
    
    print("\nExtraction Results:")
    print(f"Items: {results['summary']['total_items']}")
    print(f"Specifications: {results['summary']['total_specifications']}")
    print(f"Compatibility: {results['summary']['total_compatibility_refs']}")
    print(f"Figures: {results['summary']['total_figures']}")
    
    extractor.export_results(results)
    print("Extraction complete.")

if __name__ == "__main__":
    main()
