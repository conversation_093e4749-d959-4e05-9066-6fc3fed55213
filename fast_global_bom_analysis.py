#!/usr/bin/env python3
"""
Fast Global BOM Similarity Analysis with Efficient Sampling
Optimized for large datasets with intelligent preprocessing and fast similarity calculation.
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
from scipy.spatial.distance import squareform
import matplotlib.pyplot as plt
import traceback
from datetime import datetime
import warnings
import pickle
import hashlib
import random
warnings.filterwarnings('ignore')

def clean_description(text):
    if pd.isna(text):
        return ""
    return str(text).lower().strip()

def get_file_hash(file_path):
    """Generate a hash for a file to detect changes"""
    try:
        with open(file_path, 'rb') as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        return file_hash
    except Exception:
        return None

def save_processed_data(data, cache_file):
    """Save processed data to cache"""
    try:
        with open(cache_file, 'wb') as f:
            pickle.dump(data, f)
        return True
    except Exception as e:
        print(f"    ⚠️  Could not save cache: {e}")
        return False

def load_processed_data(cache_file):
    """Load processed data from cache"""
    try:
        with open(cache_file, 'rb') as f:
            data = pickle.load(f)
        return data
    except Exception:
        return None

def check_cache_validity(input_folder, cache_folder):
    """Check if cached data is still valid by comparing file hashes"""
    cache_info_file = os.path.join(cache_folder, 'cache_info.pkl')
    
    if not os.path.exists(cache_info_file):
        return False, {}
    
    try:
        with open(cache_info_file, 'rb') as f:
            cached_info = pickle.load(f)
    except Exception:
        return False, {}
    
    # Check if all files still exist and have same hash
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    current_info = {}
    
    for file in excel_files:
        file_path = os.path.join(input_folder, file)
        current_info[file] = get_file_hash(file_path)
    
    # Compare with cached info
    if current_info == cached_info:
        return True, current_info
    else:
        return False, current_info

def load_bom_data(file_path):
    """Load and preprocess BOM data from Excel file"""
    try:
        # Read the second sheet (index 1) which contains BOM data
        xls = pd.ExcelFile(file_path)
        if len(xls.sheet_names) < 2:
            print(f"Warning: {file_path} doesn't have a second sheet")
            return None

        df = pd.read_excel(file_path, sheet_name=1)

        # Keep only the first 4 columns
        if len(df.columns) >= 4:
            df = df.iloc[:, :4]
            # Rename columns for consistency
            df.columns = ['Level', 'Item ID', 'F-E-Line', 'Item Description']

            print(f"    📊 Original rows: {len(df)}")

            # Remove rows with ANY empty values in ALL 4 columns
            df_before = len(df)
            df = df.dropna(subset=['Level', 'Item ID', 'F-E-Line', 'Item Description'])
            df_after = len(df)

            # Additional cleaning: remove rows where any column is just whitespace
            df = df[df['Level'].astype(str).str.strip() != '']
            df = df[df['Item ID'].astype(str).str.strip() != '']
            df = df[df['F-E-Line'].astype(str).str.strip() != '']
            df = df[df['Item Description'].astype(str).str.strip() != '']

            df_after_cleaning = len(df)

            print(f"    🧹 After removing incomplete rows: {df_after} (removed {df_before - df_after})")
            print(f"    🧹 After removing empty/whitespace: {df_after_cleaning} (removed {df_after - df_after_cleaning})")

            # Filter to only Level 1, 2, and 3
            df_before_level_filter = len(df)
            # Convert Level to string and extract numeric part for comparison
            df['Level_Numeric'] = df['Level'].astype(str).str.extract('(\d+)').astype(float)
            df = df[df['Level_Numeric'].isin([1.0, 2.0, 3.0])]
            df_after_level_filter = len(df)
            
            print(f"    🎯 After filtering to Level 1, 2 & 3: {df_after_level_filter} (removed {df_before_level_filter - df_after_level_filter})")

            if df_after_level_filter == 0:
                print(f"    ❌ No valid Level 1, 2 or 3 rows found")
                return None

            # Clean and normalize descriptions for better matching
            df['Item Description'] = df['Item Description'].apply(clean_description)

            # Remove rows where description becomes empty after cleaning
            df = df[df['Item Description'].str.len() > 0]

            df_final = len(df)
            if df_final == 0:
                print(f"    ❌ No valid descriptions after cleaning")
                return None

            print(f"    ✅ Final valid Level 1,2&3 rows: {df_final}")
            return df
        else:
            print(f"Warning: {file_path} doesn't have at least 4 columns")
            return None
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def fast_sample_items(all_bom_data, max_items=10000):
    """Fast and efficient sampling of items across all files"""
    print(f"\n🎯 Fast sampling to {max_items} items for analysis...")
    
    # Combine all items with file info
    all_items = []
    for file_name, df in all_bom_data.items():
        for idx, row in df.iterrows():
            item_info = {
                'file': file_name,
                'item_id': str(row['Item ID']),
                'level': row['Level'],
                'fe_line': str(row['F-E-Line']),
                'description': str(row['Item Description']),
                'level_numeric': row['Level_Numeric'],
                'original_index': idx
            }
            all_items.append(item_info)
    
    print(f"    📊 Total items across all files: {len(all_items)}")
    
    if len(all_items) <= max_items:
        print(f"    ✅ Dataset size acceptable, using all {len(all_items)} items")
        return all_items
    
    # Fast sampling strategy: 
    # 1. Sample proportionally from each file
    # 2. Prioritize Level 1 and 2 items
    # 3. Use random sampling within constraints
    
    print(f"    🔄 Applying fast sampling...")
    
    # Group by file
    file_groups = {}
    for item in all_items:
        file_key = item['file']
        if file_key not in file_groups:
            file_groups[file_key] = []
        file_groups[file_key].append(item)
    
    # Calculate items per file (proportional)
    items_per_file = max_items // len(file_groups)
    sampled_items = []
    
    for file_key, file_items in file_groups.items():
        if len(file_items) <= items_per_file:
            # Use all items from this file
            sampled_items.extend(file_items)
        else:
            # Prioritize Level 1 and 2, then sample
            level_1_2 = [item for item in file_items if item['level_numeric'] in [1.0, 2.0]]
            level_3 = [item for item in file_items if item['level_numeric'] == 3.0]
            
            # Take more Level 1&2 items
            level_1_2_count = min(len(level_1_2), int(items_per_file * 0.7))
            level_3_count = min(len(level_3), items_per_file - level_1_2_count)
            
            if level_1_2_count > 0:
                sampled_items.extend(random.sample(level_1_2, level_1_2_count))
            if level_3_count > 0:
                sampled_items.extend(random.sample(level_3, level_3_count))
    
    print(f"    ✅ Fast sampling complete: {len(sampled_items)} items selected")
    return sampled_items

def create_fast_similarity_analysis(sampled_items, threshold=0.6):
    """Fast similarity analysis using optimized algorithms"""
    print(f"\n🚀 Fast similarity analysis on {len(sampled_items)} items...")
    
    # Extract features
    item_ids = [item['item_id'] for item in sampled_items]
    descriptions = [item['description'] for item in sampled_items]
    fe_lines = [item['fe_line'] for item in sampled_items]
    
    # Optimized TF-IDF with limited features
    print("    🔍 Creating optimized TF-IDF vectors...")
    desc_vectorizer = TfidfVectorizer(
        min_df=2, max_df=0.8, ngram_range=(1, 2), 
        stop_words='english', lowercase=True, strip_accents='unicode',
        max_features=5000  # Limit features for speed
    )
    
    id_vectorizer = TfidfVectorizer(
        min_df=1, analyzer='char_wb', ngram_range=(2, 3), lowercase=True,
        max_features=2000  # Limit features for IDs
    )
    
    try:
        desc_tfidf_matrix = desc_vectorizer.fit_transform(descriptions)
        id_tfidf_matrix = id_vectorizer.fit_transform(item_ids)
        print(f"    ✅ TF-IDF matrices created: {desc_tfidf_matrix.shape}, {id_tfidf_matrix.shape}")
        
        # Calculate similarities efficiently
        print("    🔍 Calculating similarity matrices...")
        desc_similarity_matrix = cosine_similarity(desc_tfidf_matrix)
        id_similarity_matrix = cosine_similarity(id_tfidf_matrix)
        
        # Find similar pairs efficiently
        print(f"    🔍 Finding similar pairs (threshold: {threshold})...")
        similarity_records = []
        n_items = len(sampled_items)
        
        for i in range(n_items):
            for j in range(i + 1, n_items):  # Only upper triangle
                desc_sim = desc_similarity_matrix[i, j]
                id_sim = id_similarity_matrix[i, j]
                
                # Quick filtering
                if desc_sim < 0.1 and id_sim < 0.1:
                    continue
                
                # Check exact matches
                exact_id_match = item_ids[i].lower().strip() == item_ids[j].lower().strip()
                exact_fe_match = fe_lines[i].lower().strip() == fe_lines[j].lower().strip()
                
                # Combined score
                combined_score = (desc_sim * 0.5) + (id_sim * 0.35)
                if exact_id_match:
                    combined_score += 0.3
                if exact_fe_match:
                    combined_score += 0.15
                combined_score = min(combined_score, 1.0)
                
                if combined_score >= threshold:
                    item1 = sampled_items[i]
                    item2 = sampled_items[j]
                    
                    record = {
                        'Item_ID_1': item1['item_id'],
                        'File_1': item1['file'],
                        'Level_1': item1['level'],
                        'FE_Line_1': item1['fe_line'],
                        'Description_1': item1['description'],
                        'Item_ID_2': item2['item_id'],
                        'File_2': item2['file'],
                        'Level_2': item2['level'],
                        'FE_Line_2': item2['fe_line'],
                        'Description_2': item2['description'],
                        'Similarity_Score': round(combined_score, 4),
                        'Resemblance_Percentage': round(combined_score * 100, 2),
                        'Same_File': item1['file'] == item2['file'],
                        'Same_Level': item1['level'] == item2['level'],
                        'Exact_ID_Match': exact_id_match,
                        'Exact_FE_Match': exact_fe_match,
                        'Cross_File_Match': item1['file'] != item2['file']
                    }
                    similarity_records.append(record)
        
        similarity_df = pd.DataFrame(similarity_records)
        
        if len(similarity_df) > 0:
            similarity_df = similarity_df.sort_values('Similarity_Score', ascending=False)
            print(f"    ✅ Found {len(similarity_df)} similar item pairs")
            print(f"    📊 Cross-file matches: {similarity_df['Cross_File_Match'].sum()}")
            print(f"    📊 Same-file matches: {(~similarity_df['Cross_File_Match']).sum()}")
        else:
            print(f"    ⚠️  No similar items found above threshold {threshold}")
        
        return similarity_df, sampled_items

    except Exception as e:
        print(f"    ❌ Error during similarity calculation: {e}")
        return None, None

def create_simple_dendrogram(similarity_df, sampled_items, output_folder):
    """Create a simple dendrogram from top similar items"""
    if similarity_df is None or len(similarity_df) == 0:
        print("    ⚠️  No similarity data for dendrogram")
        return

    try:
        print("    🌳 Creating similarity dendrogram...")

        # Get top similar items for dendrogram
        top_items = similarity_df.head(50)  # Use top 50 for visualization

        # Create a simple distance matrix from similarity scores
        unique_items = set()
        for _, row in top_items.iterrows():
            unique_items.add(row['Item_ID_1'])
            unique_items.add(row['Item_ID_2'])

        unique_items = list(unique_items)[:20]  # Limit to 20 items for readability

        if len(unique_items) < 3:
            print("    ⚠️  Not enough unique items for dendrogram")
            return

        # Create distance matrix
        n = len(unique_items)
        distance_matrix = np.ones((n, n))
        np.fill_diagonal(distance_matrix, 0)

        # Fill in known similarities
        for _, row in top_items.iterrows():
            if row['Item_ID_1'] in unique_items and row['Item_ID_2'] in unique_items:
                i = unique_items.index(row['Item_ID_1'])
                j = unique_items.index(row['Item_ID_2'])
                distance = 1 - row['Similarity_Score']
                distance_matrix[i, j] = distance
                distance_matrix[j, i] = distance

        # Create linkage matrix
        condensed_distances = squareform(distance_matrix, checks=False)
        linkage_matrix = linkage(condensed_distances, method='ward')

        # Plot dendrogram
        plt.figure(figsize=(15, 8))
        dendrogram(
            linkage_matrix,
            labels=[item[:15] + "..." if len(item) > 15 else item for item in unique_items],
            leaf_rotation=45,
            leaf_font_size=10
        )
        plt.title('BOM Items Similarity Dendrogram\n(Top Similar Items)', fontsize=14)
        plt.xlabel('Items', fontsize=12)
        plt.ylabel('Distance (1 - Similarity)', fontsize=12)
        plt.tight_layout()

        # Save dendrogram
        plt.savefig(os.path.join(output_folder, 'fast_bom_similarity_dendrogram.png'), dpi=300, bbox_inches='tight')
        plt.close()
        print(f"    ✅ Dendrogram saved: fast_bom_similarity_dendrogram.png")

    except Exception as e:
        print(f"    ⚠️  Could not create dendrogram: {e}")

def main():
    print("🚀 Starting Fast Global BOM Similarity Analysis with Caching")
    print("=" * 70)
    print(f"⏰ Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Analysis Focus: Level 1, 2, 3 items with fast global analysis")

    input_folder = 'input'
    output_folder = 'output'
    cache_folder = 'cache'

    # Check if input folder exists
    if not os.path.exists(input_folder):
        print(f"❌ Error: Input folder '{input_folder}' not found!")
        print("Please create the 'input' folder and place your Excel files there.")
        return

    # Create folders if they don't exist
    os.makedirs(output_folder, exist_ok=True)
    os.makedirs(cache_folder, exist_ok=True)
    print(f"📁 Output folder: {os.path.abspath(output_folder)}")
    print(f"💾 Cache folder: {os.path.abspath(cache_folder)}")

    # Get all Excel files
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    print(f"📊 Found {len(excel_files)} Excel files to analyze")

    if len(excel_files) == 0:
        print("❌ No Excel files found in the input folder!")
        print("Please add .xlsx or .xls files to the 'input' folder.")
        return

    # Check cache validity
    print("\n🔍 Checking cache validity...")
    cache_valid, file_info = check_cache_validity(input_folder, cache_folder)
    cache_data_file = os.path.join(cache_folder, 'bom_data.pkl')

    if cache_valid and os.path.exists(cache_data_file):
        print("✅ Cache is valid! Loading preprocessed data...")
        bom_data = load_processed_data(cache_data_file)
        if bom_data is not None:
            print(f"📈 Loaded {len(bom_data)} files from cache")
            total_items = sum(len(df) for df in bom_data.values())
            print(f"📊 Total Level 1,2,3 items: {total_items}")
        else:
            print("⚠️  Cache corrupted, will reload from files")
            cache_valid = False

    if not cache_valid:
        print("🔄 Loading and preprocessing BOM data from files...")
        bom_data = {}
        failed_files = []

        for i, file in enumerate(excel_files, 1):
            print(f"  [{i}/{len(excel_files)}] Loading: {file}")
            file_path = os.path.join(input_folder, file)

            try:
                df = load_bom_data(file_path)
                if df is not None:
                    bom_data[file] = df
                    print(f"    ✅ Loaded {len(df)} Level 1,2,3 items")
                else:
                    failed_files.append(file)
                    print(f"    ❌ Failed to load (check file format)")
            except Exception as e:
                failed_files.append(file)
                print(f"    ❌ Error: {str(e)}")

        print(f"\n📈 Successfully loaded {len(bom_data)} files")
        if failed_files:
            print(f"⚠️  Failed to load {len(failed_files)} files: {failed_files}")

        # Save to cache
        print("💾 Saving preprocessed data to cache...")
        if save_processed_data(bom_data, cache_data_file):
            # Save file info for cache validation
            cache_info_file = os.path.join(cache_folder, 'cache_info.pkl')
            save_processed_data(file_info, cache_info_file)
            print("✅ Data cached successfully!")
        else:
            print("⚠️  Could not save to cache, but continuing with analysis...")

    if len(bom_data) < 1:
        print("❌ Need at least 1 file with valid data to perform analysis!")
        return

    # FAST GLOBAL ANALYSIS
    print(f"\n🚀 FAST GLOBAL SIMILARITY ANALYSIS")
    print("=" * 50)

    # Fast sampling
    sampled_items = fast_sample_items(bom_data, max_items=10000)

    # Fast similarity analysis
    similarity_df, item_metadata = create_fast_similarity_analysis(sampled_items, threshold=0.6)

    if similarity_df is None:
        print("❌ Could not perform similarity analysis!")
        return

    # Create simple dendrogram
    create_simple_dendrogram(similarity_df, sampled_items, output_folder)

    # Generate reports
    print(f"\n📝 Generating fast analysis reports...")

    # Text summary report
    with open(os.path.join(output_folder, 'fast_similarity_summary.txt'), 'w', encoding='utf-8') as f:
        f.write("Fast Global BOM Similarity Analysis Summary\n")
        f.write("=" * 50 + "\n")
        f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Analysis Method: Fast Sampling + TF-IDF Similarity\n")
        f.write(f"Total files analyzed: {len(bom_data)}\n")
        f.write(f"Total items sampled: {len(sampled_items)}\n")
        f.write(f"Similar pairs found: {len(similarity_df) if similarity_df is not None else 0}\n\n")

        if similarity_df is not None and len(similarity_df) > 0:
            f.write("Top 20 Similar Item Pairs\n")
            f.write("=" * 25 + "\n")
            top_pairs = similarity_df.head(20)
            for i, (_, pair) in enumerate(top_pairs.iterrows(), 1):
                f.write(f"{i:2d}. {pair['Item_ID_1']} ↔ {pair['Item_ID_2']} ({pair['Resemblance_Percentage']:.1f}%)\n")
                f.write(f"    Files: {pair['File_1']} ↔ {pair['File_2']}\n")
                f.write(f"    Cross-file: {pair['Cross_File_Match']}\n\n")

    # Excel report
    try:
        with pd.ExcelWriter(os.path.join(output_folder, 'fast_bom_similarity_analysis.xlsx'), engine='openpyxl') as writer:
            if similarity_df is not None and len(similarity_df) > 0:
                similarity_df.to_excel(writer, sheet_name='Similarities', index=False)

            # File summary
            file_summary = []
            for file_name, df in bom_data.items():
                file_info = {
                    'File_Name': file_name,
                    'Total_Items': len(df),
                    'Level_1_Items': len(df[df['Level_Numeric'] == 1.0]),
                    'Level_2_Items': len(df[df['Level_Numeric'] == 2.0]),
                    'Level_3_Items': len(df[df['Level_Numeric'] == 3.0])
                }
                file_summary.append(file_info)

            file_summary_df = pd.DataFrame(file_summary)
            file_summary_df.to_excel(writer, sheet_name='File_Summary', index=False)

        print(f"\n📊 Excel report saved: fast_bom_similarity_analysis.xlsx")

    except Exception as e:
        print(f"\n❌ Error saving Excel report: {str(e)}")

    # Final summary
    print(f"\n🎉 Fast Global BOM Similarity Analysis Complete!")
    print("=" * 60)
    print(f"⏰ End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Reports saved to: {os.path.abspath(output_folder)}")
    print(f"📊 Total files analyzed: {len(bom_data)}")
    print(f"📊 Items sampled for analysis: {len(sampled_items)}")
    print(f"🎯 Similar pairs found: {len(similarity_df) if similarity_df is not None else 0}")
    print("\n📋 Generated files:")
    print("  • fast_similarity_summary.txt - Human-readable analysis summary")
    print("  • fast_bom_similarity_analysis.xlsx - Excel report with similarities")
    print("  • fast_bom_similarity_dendrogram.png - Similarity tree visualization")

    if similarity_df is not None and len(similarity_df) > 0:
        print(f"\n🏆 Top 5 most similar items found:")
        top_matches = similarity_df.head(5)
        for i, (_, match) in enumerate(top_matches.iterrows(), 1):
            cross_file = " [Cross-File]" if match['Cross_File_Match'] else " [Same-File]"
            print(f"  {i}. {match['Item_ID_1']} ↔ {match['Item_ID_2']} ({match['Resemblance_Percentage']:.1f}%){cross_file}")
    else:
        print("\n⚠️  No similar items found with current threshold (0.6)")

if __name__ == "__main__":
    main()
