# Evaluation
`sentence_transformers.evaluation` defines different classes, that can be used to evaluate the model during training.

## BinaryClassificationEvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.BinaryClassificationEvaluator
```

## EmbeddingSimilarityEvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.EmbeddingSimilarityEvaluator
```

## InformationRetrievalEvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.InformationRetrievalEvaluator
```

## NanoBEIREvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.NanoBEIREvaluator
```

## MSEEvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.MSEEvaluator
```

## ParaphraseMiningEvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.ParaphraseMiningEvaluator
```

## RerankingEvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.RerankingEvaluator
```

## SentenceEvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.SentenceEvaluator
```

## SequentialEvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.SequentialEvaluator
```

## TranslationEvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.TranslationEvaluator
```

## TripletEvaluator
```{eval-rst}
.. autoclass:: sentence_transformers.evaluation.TripletEvaluator
```
