def create_graph(bom_data):
    import networkx as nx

    G = nx.DiGraph()  # Create a directed graph

    for index, row in bom_data.iterrows():
        item = row['Item']
        parent_item = row['Parent Item']

        # Add nodes for each item
        G.add_node(item)

        # Add an edge from the parent item to the current item
        if pd.notna(parent_item):
            G.add_edge(parent_item, item)

    return G

def add_edges(G, relationships):
    for relationship in relationships:
        source = relationship['source']
        target = relationship['target']
        G.add_edge(source, target)

def get_graph_info(G):
    return {
        'number_of_nodes': G.number_of_nodes(),
        'number_of_edges': G.number_of_edges(),
        'nodes': list(G.nodes),
        'edges': list(G.edges)
    }