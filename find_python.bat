@echo off
echo 🔍 Searching for Python installations...
echo ========================================
echo.

echo 📍 Checking common Python locations:
echo.

REM Check if python is in PATH
echo 1. Checking if 'python' command works:
python --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ 'python' command found in PATH
    where python 2>nul
) else (
    echo ❌ 'python' command not found in PATH
)
echo.

echo 2. Checking if 'py' launcher works:
py --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ 'py' launcher found
    py -0 2>nul
) else (
    echo ❌ 'py' launcher not found
)
echo.

echo 3. Searching in Program Files:
if exist "C:\Program Files\Python*" (
    dir "C:\Program Files\Python*" /b
) else (
    echo ❌ No Python found in C:\Program Files\
)
echo.

echo 4. Searching in User AppData:
if exist "C:\Users\<USER>\AppData\Local\Programs\Python*" (
    dir "C:\Users\<USER>\AppData\Local\Programs\Python*" /b
) else (
    echo ❌ No Python found in User AppData
)
echo.

echo 5. Searching in Microsoft Store Apps:
if exist "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe" (
    echo ✅ Found Microsoft Store Python
    echo C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe
) else (
    echo ❌ No Microsoft Store Python found
)
echo.

echo 6. Searching for any python.exe files:
echo Searching... (this may take a moment)
for /f "delims=" %%i in ('dir C:\python.exe /s /b 2^>nul') do echo Found: %%i
for /f "delims=" %%i in ('dir "C:\Program Files\python.exe" /s /b 2^>nul') do echo Found: %%i
for /f "delims=" %%i in ('dir "C:\Users\<USER>\python.exe" /s /b 2^>nul') do echo Found: %%i
echo.

echo 7. Checking existing virtual environment:
if exist "C:\Users\<USER>\OneDrive - GE HealthCare\virtualenv\Scripts\python.exe" (
    echo ✅ Found existing virtual environment
    echo C:\Users\<USER>\OneDrive - GE HealthCare\virtualenv\Scripts\python.exe
    "C:\Users\<USER>\OneDrive - GE HealthCare\virtualenv\Scripts\python.exe" --version 2>nul
    if %errorlevel% equ 0 (
        echo ✅ This Python works!
        echo.
        echo 🎯 SOLUTION: You can use this existing Python!
        echo To activate this environment, run:
        echo "C:\Users\<USER>\OneDrive - GE HealthCare\virtualenv\Scripts\activate.bat"
    )
) else (
    echo ❌ No existing virtual environment found
)
echo.

echo ========================================
echo 💡 Recommendations:
echo.
echo If Python was found above:
echo   • Try using the full path to python.exe
echo   • Add Python to your PATH environment variable
echo.
echo If no Python was found:
echo   • Install Python from https://python.org/downloads/
echo   • Make sure to check "Add Python to PATH" during installation
echo.
pause
