from typing import Any, Dict, List
import matplotlib.pyplot as plt
import networkx as nx

def visualize_graph(graph: nx.Graph, labels: Dict[Any, str]) -> None:
    plt.figure(figsize=(12, 8))
    
    # Draw the graph
    pos = nx.spring_layout(graph)  # positions for all nodes
    nx.draw(graph, pos, with_labels=True, node_size=2000, node_color='lightblue', font_size=10, font_weight='bold')

    # Draw labels with tooltips
    for node, label in labels.items():
        x, y = pos[node]
        plt.text(x, y, label, fontsize=12, ha='center', va='center', color='black')

    plt.title("Bill of Materials Graph Visualization")
    plt.xlabel("X-axis")
    plt.ylabel("Y-axis")
    plt.grid()
    plt.show()