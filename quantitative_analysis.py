#!/usr/bin/env python3
"""
Analyse quantitative complète des résultats BOM-PCM Voyager
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class QuantitativeAnalyzer:
    def __init__(self):
        """Initialize l'analyseur quantitatif"""
        self.df = None
        self.stats_summary = {}
        
    def load_data(self):
        """Charger les données"""
        try:
            self.df = pd.read_excel('output/voyager_complete_bom_pcm_table.xlsx', sheet_name='Complete_Table')
            print(f"✅ Données chargées: {len(self.df)} lignes")
            return True
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def basic_statistics(self):
        """Statistiques de base"""
        print("\n📊 STATISTIQUES DE BASE")
        print("=" * 50)
        
        total_items = len(self.df)
        with_pcm = len(self.df[self.df['Item_ID_PCM'] != ''])
        without_pcm = total_items - with_pcm
        
        # Métriques principales
        coverage_rate = (with_pcm / total_items) * 100
        
        stats = {
            'total_bom_items': total_items,
            'matched_items': with_pcm,
            'unmatched_items': without_pcm,
            'coverage_rate_percent': coverage_rate,
            'unique_pcm_codes': len(self.df[self.df['Item_ID_PCM'] != '']['Item_ID_PCM'].unique()),
            'avg_similarity_score': self.df[self.df['Similarity_Score'] > 0]['Similarity_Score'].mean(),
            'median_similarity_score': self.df[self.df['Similarity_Score'] > 0]['Similarity_Score'].median(),
            'std_similarity_score': self.df[self.df['Similarity_Score'] > 0]['Similarity_Score'].std()
        }
        
        print(f"Total BOM Items: {stats['total_bom_items']:,}")
        print(f"Matched with PCM: {stats['matched_items']:,} ({stats['coverage_rate_percent']:.2f}%)")
        print(f"Unmatched: {stats['unmatched_items']:,} ({100-stats['coverage_rate_percent']:.2f}%)")
        print(f"Unique PCM Codes: {stats['unique_pcm_codes']:,}")
        print(f"Avg Similarity Score: {stats['avg_similarity_score']:.3f} ± {stats['std_similarity_score']:.3f}")
        print(f"Median Similarity Score: {stats['median_similarity_score']:.3f}")
        
        self.stats_summary['basic'] = stats
        return stats
    
    def mandatoriness_analysis(self):
        """Analyse quantitative par mandatoriness"""
        print("\n🎨 ANALYSE QUANTITATIVE MANDATORINESS")
        print("=" * 50)
        
        # Filtrer les items avec PCM
        with_pcm = self.df[self.df['Item_ID_PCM'] != ''].copy()
        
        mandatoriness_stats = {}
        
        for mandatoriness in ['Blue', 'Purple', 'Red']:
            subset = with_pcm[with_pcm['Mandatoriness'] == mandatoriness]
            
            if len(subset) > 0:
                stats = {
                    'count': len(subset),
                    'percentage_of_matched': (len(subset) / len(with_pcm)) * 100,
                    'percentage_of_total': (len(subset) / len(self.df)) * 100,
                    'avg_similarity': subset['Similarity_Score'].mean(),
                    'median_similarity': subset['Similarity_Score'].median(),
                    'std_similarity': subset['Similarity_Score'].std(),
                    'min_similarity': subset['Similarity_Score'].min(),
                    'max_similarity': subset['Similarity_Score'].max(),
                    'unique_pages': subset['Page'].nunique(),
                    'avg_position_x': subset['Position_X'].mean(),
                    'avg_position_y': subset['Position_Y'].mean()
                }
                
                mandatoriness_stats[mandatoriness] = stats
                
                print(f"\n📊 {mandatoriness} (Mandatory: {'Yes' if mandatoriness == 'Blue' else 'Selectable' if mandatoriness == 'Purple' else 'Optional'})")
                print(f"   Count: {stats['count']:,} ({stats['percentage_of_matched']:.1f}% of matched)")
                print(f"   Similarity: {stats['avg_similarity']:.3f} ± {stats['std_similarity']:.3f}")
                print(f"   Range: [{stats['min_similarity']:.3f}, {stats['max_similarity']:.3f}]")
                print(f"   Pages: {stats['unique_pages']} unique pages")
                print(f"   Avg Position: ({stats['avg_position_x']:.0f}, {stats['avg_position_y']:.0f})")
        
        self.stats_summary['mandatoriness'] = mandatoriness_stats
        return mandatoriness_stats
    
    def level_analysis(self):
        """Analyse quantitative par niveau BOM"""
        print("\n📊 ANALYSE QUANTITATIVE PAR NIVEAU BOM")
        print("=" * 50)
        
        level_stats = self.df.groupby('Level').agg({
            'Item_ID': 'count',
            'Item_ID_PCM': lambda x: (x != '').sum(),
            'Similarity_Score': ['mean', 'median', 'std', 'count']
        }).round(3)
        
        # Flatten column names
        level_stats.columns = ['Total_Items', 'Matched_Items', 'Avg_Similarity', 'Median_Similarity', 'Std_Similarity', 'Similarity_Count']
        level_stats['Match_Rate_%'] = (level_stats['Matched_Items'] / level_stats['Total_Items'] * 100).round(2)
        
        # Calculer les statistiques par niveau
        level_analysis = {}
        for level in level_stats.index[:10]:  # Top 10 levels
            stats = level_stats.loc[level]
            level_analysis[level] = {
                'total_items': int(stats['Total_Items']),
                'matched_items': int(stats['Matched_Items']),
                'match_rate': float(stats['Match_Rate_%']),
                'avg_similarity': float(stats['Avg_Similarity']) if not pd.isna(stats['Avg_Similarity']) else 0,
                'median_similarity': float(stats['Median_Similarity']) if not pd.isna(stats['Median_Similarity']) else 0
            }
        
        print("Top 10 Levels by Item Count:")
        print(level_stats.head(10).to_string())
        
        self.stats_summary['levels'] = level_analysis
        return level_analysis
    
    def similarity_distribution_analysis(self):
        """Analyse de la distribution des scores de similarité"""
        print("\n📈 ANALYSE DISTRIBUTION SCORES SIMILARITÉ")
        print("=" * 50)
        
        similarity_scores = self.df[self.df['Similarity_Score'] > 0]['Similarity_Score']
        
        # Statistiques descriptives
        desc_stats = {
            'count': len(similarity_scores),
            'mean': similarity_scores.mean(),
            'median': similarity_scores.median(),
            'std': similarity_scores.std(),
            'min': similarity_scores.min(),
            'max': similarity_scores.max(),
            'q25': similarity_scores.quantile(0.25),
            'q75': similarity_scores.quantile(0.75),
            'iqr': similarity_scores.quantile(0.75) - similarity_scores.quantile(0.25)
        }
        
        print(f"Descriptive Statistics:")
        print(f"  Count: {desc_stats['count']:,}")
        print(f"  Mean: {desc_stats['mean']:.3f}")
        print(f"  Median: {desc_stats['median']:.3f}")
        print(f"  Std Dev: {desc_stats['std']:.3f}")
        print(f"  Min: {desc_stats['min']:.3f}")
        print(f"  Max: {desc_stats['max']:.3f}")
        print(f"  Q1: {desc_stats['q25']:.3f}")
        print(f"  Q3: {desc_stats['q75']:.3f}")
        print(f"  IQR: {desc_stats['iqr']:.3f}")
        
        # Distribution par tranches
        bins = [0.6, 0.7, 0.8, 0.9, 1.0]
        labels = ['0.6-0.7', '0.7-0.8', '0.8-0.9', '0.9-1.0']
        
        score_distribution = pd.cut(similarity_scores, bins=bins, labels=labels, include_lowest=True).value_counts()
        
        print(f"\nDistribution by Ranges:")
        for label, count in score_distribution.items():
            percentage = (count / len(similarity_scores)) * 100
            print(f"  {label}: {count:,} ({percentage:.1f}%)")
        
        # Test de normalité
        shapiro_stat, shapiro_p = stats.shapiro(similarity_scores.sample(min(5000, len(similarity_scores))))
        
        print(f"\nNormality Test (Shapiro-Wilk):")
        print(f"  Statistic: {shapiro_stat:.3f}")
        print(f"  P-value: {shapiro_p:.6f}")
        print(f"  Normal Distribution: {'No' if shapiro_p < 0.05 else 'Yes'}")
        
        self.stats_summary['similarity_distribution'] = {
            'descriptive': desc_stats,
            'distribution': dict(score_distribution),
            'normality_test': {'statistic': shapiro_stat, 'p_value': shapiro_p}
        }
        
        return desc_stats, score_distribution
    
    def correlation_analysis(self):
        """Analyse des corrélations"""
        print("\n🔗 ANALYSE DES CORRÉLATIONS")
        print("=" * 40)
        
        # Préparer les données numériques
        numeric_data = self.df[['Level', 'Similarity_Score', 'Position_X', 'Position_Y', 'Page']].copy()
        numeric_data = numeric_data[numeric_data['Similarity_Score'] > 0]
        
        # Matrice de corrélation
        correlation_matrix = numeric_data.corr()
        
        print("Correlation Matrix:")
        print(correlation_matrix.round(3).to_string())
        
        # Corrélations significatives
        significant_correlations = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_value = correlation_matrix.iloc[i, j]
                if abs(corr_value) > 0.1:  # Seuil de corrélation
                    var1 = correlation_matrix.columns[i]
                    var2 = correlation_matrix.columns[j]
                    significant_correlations.append({
                        'var1': var1,
                        'var2': var2,
                        'correlation': corr_value
                    })
        
        print(f"\nSignificant Correlations (|r| > 0.1):")
        for corr in significant_correlations:
            print(f"  {corr['var1']} ↔ {corr['var2']}: {corr['correlation']:.3f}")
        
        self.stats_summary['correlations'] = {
            'matrix': correlation_matrix.to_dict(),
            'significant': significant_correlations
        }
        
        return correlation_matrix
    
    def pcm_similarity_analysis(self):
        """Analyse des similarités dans le PCM"""
        print("\n🔗 ANALYSE SIMILARITÉS DANS LE PCM")
        print("=" * 45)
        
        # Items avec similarités PCM
        with_similar = self.df[(self.df['Similar_in_PCM'] != '') & (self.df['Similar_in_PCM'].notna())]
        
        similarity_stats = {
            'total_with_similar': len(with_similar),
            'percentage_with_similar': (len(with_similar) / len(self.df[self.df['Item_ID_PCM'] != ''])) * 100,
            'avg_similarity_score_with_similar': with_similar['Similarity_Score'].mean(),
            'avg_similarity_score_without_similar': self.df[
                (self.df['Item_ID_PCM'] != '') & 
                ((self.df['Similar_in_PCM'] == '') | (self.df['Similar_in_PCM'].isna()))
            ]['Similarity_Score'].mean()
        }
        
        print(f"Items with PCM Similarities: {similarity_stats['total_with_similar']:,}")
        print(f"Percentage: {similarity_stats['percentage_with_similar']:.1f}%")
        print(f"Avg Similarity Score (with similar): {similarity_stats['avg_similarity_score_with_similar']:.3f}")
        print(f"Avg Similarity Score (without similar): {similarity_stats['avg_similarity_score_without_similar']:.3f}")
        
        # Analyse du nombre de similarités par item
        similar_counts = []
        for similar_str in with_similar['Similar_in_PCM']:
            if pd.notna(similar_str) and similar_str != '':
                count = len(similar_str.split(';'))
                similar_counts.append(count)
        
        if similar_counts:
            print(f"\nSimilarity Count Distribution:")
            print(f"  Mean similar items per PCM code: {np.mean(similar_counts):.1f}")
            print(f"  Max similar items: {max(similar_counts)}")
            print(f"  Min similar items: {min(similar_counts)}")
        
        self.stats_summary['pcm_similarities'] = similarity_stats
        return similarity_stats
    
    def generate_summary_report(self):
        """Générer un rapport de synthèse quantitatif"""
        print("\n📋 RAPPORT DE SYNTHÈSE QUANTITATIF")
        print("=" * 60)
        
        report_lines = []
        report_lines.append("ANALYSE QUANTITATIVE VOYAGER BOM-PCM")
        report_lines.append("=" * 50)
        report_lines.append("")
        
        # Métriques principales
        basic = self.stats_summary.get('basic', {})
        report_lines.append("📊 MÉTRIQUES PRINCIPALES")
        report_lines.append("-" * 30)
        report_lines.append(f"Total BOM Items: {basic.get('total_bom_items', 0):,}")
        report_lines.append(f"Coverage Rate: {basic.get('coverage_rate_percent', 0):.2f}%")
        report_lines.append(f"Average Similarity: {basic.get('avg_similarity_score', 0):.3f}")
        report_lines.append(f"Unique PCM Codes: {basic.get('unique_pcm_codes', 0):,}")
        report_lines.append("")
        
        # Mandatoriness
        mandatoriness = self.stats_summary.get('mandatoriness', {})
        report_lines.append("🎨 MANDATORINESS BREAKDOWN")
        report_lines.append("-" * 30)
        for color, stats in mandatoriness.items():
            report_lines.append(f"{color}: {stats['count']:,} items ({stats['percentage_of_matched']:.1f}%)")
            report_lines.append(f"  Similarity: {stats['avg_similarity']:.3f} ± {stats['std_similarity']:.3f}")
        report_lines.append("")
        
        # Distribution qualité
        similarity_dist = self.stats_summary.get('similarity_distribution', {})
        if 'distribution' in similarity_dist:
            report_lines.append("📈 QUALITY DISTRIBUTION")
            report_lines.append("-" * 25)
            for range_label, count in similarity_dist['distribution'].items():
                total_matches = basic.get('matched_items', 1)
                percentage = (count / total_matches) * 100
                report_lines.append(f"{range_label}: {count:,} ({percentage:.1f}%)")
        report_lines.append("")
        
        # Recommandations
        report_lines.append("🎯 RECOMMANDATIONS QUANTITATIVES")
        report_lines.append("-" * 35)
        
        coverage = basic.get('coverage_rate_percent', 0)
        if coverage < 5:
            report_lines.append("• CRITIQUE: Très faible couverture PCM (<5%)")
        elif coverage < 20:
            report_lines.append("• ATTENTION: Couverture PCM limitée (<20%)")
        else:
            report_lines.append("• BON: Couverture PCM acceptable")
        
        avg_sim = basic.get('avg_similarity_score', 0)
        if avg_sim > 0.8:
            report_lines.append("• EXCELLENT: Haute qualité de matching (>0.8)")
        elif avg_sim > 0.6:
            report_lines.append("• BON: Qualité de matching acceptable (>0.6)")
        else:
            report_lines.append("• ATTENTION: Qualité de matching à améliorer (<0.6)")
        
        # Sauvegarder le rapport
        with open('output/quantitative_analysis_report.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print('\n'.join(report_lines))
        print(f"\n✅ Rapport sauvegardé: output/quantitative_analysis_report.txt")

def main():
    """Fonction principale"""
    print("📊 ANALYSE QUANTITATIVE COMPLÈTE")
    print("=" * 60)
    
    analyzer = QuantitativeAnalyzer()
    
    if not analyzer.load_data():
        return None
    
    # Analyses quantitatives
    analyzer.basic_statistics()
    analyzer.mandatoriness_analysis()
    analyzer.level_analysis()
    analyzer.similarity_distribution_analysis()
    analyzer.correlation_analysis()
    analyzer.pcm_similarity_analysis()
    
    # Rapport de synthèse
    analyzer.generate_summary_report()
    
    print(f"\n✅ ANALYSE QUANTITATIVE TERMINÉE")
    print(f"📊 Toutes les métriques calculées")
    print(f"📋 Rapport disponible: output/quantitative_analysis_report.txt")
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
