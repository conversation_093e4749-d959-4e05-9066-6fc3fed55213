#!/usr/bin/env python3
"""
Test the smart F-E-Line encoding
"""

import pandas as pd
import os

def test_fe_line_encoding():
    print("🧪 Testing Smart F-E-Line Encoding")
    print("=" * 40)
    
    # Import the encoding function
    try:
        from bom_similarity_analysis import encode_fe_line, load_bom_data
        print("✅ Successfully imported encoding functions")
    except Exception as e:
        print(f"❌ Error importing: {e}")
        return
    
    # Test with first Excel file
    input_folder = 'input'
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    
    if not excel_files:
        print("❌ No Excel files found!")
        return
    
    test_file = excel_files[0]
    print(f"\n📄 Testing with: {test_file}")
    
    file_path = os.path.join(input_folder, test_file)
    
    try:
        # Test the complete loading with encoding
        df = load_bom_data(file_path)
        
        if df is not None:
            print(f"\n✅ File loaded and processed successfully!")
            print(f"📊 Final shape: {df.shape}")
            print(f"📋 Columns: {list(df.columns)}")
            
            # Check F-E-Line encoding
            if 'F-E-Line_Encoded' in df.columns:
                print(f"\n🔧 F-E-Line Encoding Results:")
                
                # Show original vs encoded
                fe_sample = df[['F-E-Line', 'F-E-Line_Encoded']].head(10)
                print(f"📋 Sample F-E-Line encoding:")
                print(fe_sample.to_string(index=False))
                
                # Show encoding statistics
                unique_original = df['F-E-Line'].nunique()
                unique_encoded = df['F-E-Line_Encoded'].nunique()
                print(f"\n📊 Encoding statistics:")
                print(f"  Unique F-E-Line values: {unique_original}")
                print(f"  Unique encoded values: {unique_encoded}")
                print(f"  Encoding efficiency: {(unique_encoded/unique_original*100):.1f}%")
                
                # Check encoding info
                if hasattr(df, 'attrs') and 'fe_line_encoding' in df.attrs:
                    encoding_info = df.attrs['fe_line_encoding']
                    print(f"  Parent-child relationships: {len(encoding_info['parent_child_map'])}")
                    
                    # Show some parent-child relationships
                    if encoding_info['parent_child_map']:
                        print(f"\n🌳 Sample parent-child relationships:")
                        for child_code, parent_code in list(encoding_info['parent_child_map'].items())[:5]:
                            child_fe = encoding_info['code_to_fe_line'][child_code]
                            parent_fe = encoding_info['code_to_fe_line'][parent_code]
                            print(f"    {parent_fe} → {child_fe}")
                
                # Check data types
                print(f"\n📊 Data types after encoding:")
                print(f"  F-E-Line_Encoded: {df['F-E-Line_Encoded'].dtype}")
                print(f"  Level_Numeric: {df['Level_Numeric'].dtype}")
                
                # Memory usage
                memory_usage = df.memory_usage(deep=True).sum() / 1024**2
                print(f"  Total memory usage: {memory_usage:.2f} MB")
                
            else:
                print("❌ F-E-Line encoding not found")
            
            # Check optimization results
            if 'Occurrence_Count' in df.columns:
                total_original = df['Occurrence_Count'].sum()
                unique_items = len(df)
                print(f"\n💾 Deduplication results:")
                print(f"  Original items: {total_original}")
                print(f"  Unique items: {unique_items}")
                print(f"  Memory reduction: {((total_original-unique_items)/total_original*100):.1f}%")
            
        else:
            print("❌ Failed to load file")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fe_line_encoding()
