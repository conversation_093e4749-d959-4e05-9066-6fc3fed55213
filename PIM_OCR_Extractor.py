#!/usr/bin/env python3
"""
PIM OCR Extractor - Extraction de vocabulaire technique avec OCR
Pour les PDFs qui contiennent du texte sous forme d'images
"""

import fitz  # PyMuPDF
from PIL import Image
import pytesseract
import cv2
import numpy as np
import pandas as pd
import json
import re
from collections import defaultdict
import os

class PIMOCRExtractor:
    def __init__(self):
        """
        Initialise l'extracteur OCR pour les documents PIM
        """
        # Configuration OCR
        self.ocr_config = '--oem 3 --psm 6 -l eng'
        
        # Patterns pour codes techniques (anglais)
        self.part_patterns = [
            r'\b[A-Z]\d{4,6}[A-Z]{0,2}\b',  # Codes comme M7010FC, R4390JA
            r'\b\d{6,10}\b',  # Codes numériques longs
            r'\b[A-Z]{2,4}-\d{3,6}\b',  # Patterns comme ABC-1234
            r'\bP/N\s*[:\-]?\s*([A-Z0-9\-]+)\b',  # Part numbers
            r'\bS/N\s*[:\-]?\s*([A-Z0-9\-]+)\b',  # Serial numbers
        ]
        
        # Mots-clés techniques médicaux (anglais)
        self.medical_keywords = [
            'coil', 'magnet', 'gradient', 'shim', 'rf', 'transmit', 'receive',
            'cable', 'connector', 'board', 'module', 'system', 'subsystem',
            'interface', 'controller', 'upgrade', 'option', 'accessory',
            'phantom', 'test', 'patient', 'table', 'gantry', 'bore',
            'tesla', 'mri', 'magnetic', 'resonance', 'imaging', 'scan',
            'clinical', 'medical', 'diagnostic', 'procedure', 'protocol'
        ]
        
        # Vocabulaire technique accumulé
        self.vocabulary_base = {}
        
    def preprocess_image_for_ocr(self, image):
        """
        Préprocesse l'image pour améliorer l'OCR
        """
        # Convertir en niveaux de gris
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # Améliorer le contraste
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # Débruitage
        denoised = cv2.medianBlur(enhanced, 3)
        
        # Binarisation adaptative
        binary = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        return binary
    
    def extract_text_from_image(self, image):
        """
        Extrait le texte d'une image avec OCR
        """
        try:
            # Préprocesser l'image
            processed_image = self.preprocess_image_for_ocr(image)
            
            # OCR avec Tesseract
            text = pytesseract.image_to_string(processed_image, config=self.ocr_config)
            
            return text.strip()
        except Exception as e:
            print(f"⚠️ Erreur OCR: {e}")
            return ""
    
    def extract_vocabulary_from_pdf(self, pdf_path):
        """
        Extrait le vocabulaire technique complet du PDF avec OCR
        """
        print("🔍 EXTRACTION VOCABULAIRE TECHNIQUE AVEC OCR")
        print("=" * 60)
        print(f"📄 Traitement: {pdf_path}")
        
        doc = fitz.open(pdf_path)
        all_text = ""
        page_data = []
        
        print(f"📊 Total pages: {len(doc)}")
        
        for page_num in range(len(doc)):
            print(f"🔍 Page {page_num + 1}/{len(doc)}...", end=" ")
            
            try:
                page = doc.load_page(page_num)
                
                # Essayer d'abord l'extraction de texte normale
                text = page.get_text()
                
                # Si pas de texte, utiliser OCR
                if not text.strip():
                    # Convertir la page en image
                    mat = fitz.Matrix(2.0, 2.0)  # Zoom x2 pour meilleure qualité OCR
                    pix = page.get_pixmap(matrix=mat)
                    img_data = pix.samples
                    img = np.frombuffer(img_data, dtype=np.uint8).reshape(pix.h, pix.w, pix.n)
                    
                    # Convertir en format OpenCV
                    if pix.n == 4:  # RGBA
                        img = cv2.cvtColor(img, cv2.COLOR_RGBA2RGB)
                    elif pix.n == 3:  # RGB
                        img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                    
                    # OCR
                    text = self.extract_text_from_image(img)
                
                # Nettoyer le texte
                text = self.clean_extracted_text(text)
                
                page_info = {
                    'page_number': page_num + 1,
                    'text': text,
                    'word_count': len(text.split()),
                    'extraction_method': 'OCR' if not page.get_text().strip() else 'Direct'
                }
                
                page_data.append(page_info)
                all_text += text + "\n"
                
                print(f"✅ {page_info['word_count']} mots ({page_info['extraction_method']})")
                
            except Exception as e:
                print(f"❌ Erreur: {e}")
                page_data.append({
                    'page_number': page_num + 1,
                    'text': '',
                    'word_count': 0,
                    'extraction_method': 'Error'
                })
        
        doc.close()
        
        # Analyser le vocabulaire extrait
        print(f"\n📊 ANALYSE DU VOCABULAIRE EXTRAIT")
        vocabulary_analysis = self.analyze_technical_vocabulary(all_text)
        technical_terms = self.categorize_technical_terms(all_text)
        extracted_codes = self.extract_all_codes(all_text)
        
        # Créer le socle de vocabulaire
        self.vocabulary_base = {
            'document_info': {
                'pdf_path': pdf_path,
                'total_pages': len(page_data),
                'total_words': len(all_text.split()),
                'unique_words': len(set(all_text.lower().split())),
                'extraction_summary': self.get_extraction_summary(page_data)
            },
            'page_data': page_data,
            'vocabulary_analysis': vocabulary_analysis,
            'technical_terms': technical_terms,
            'extracted_codes': extracted_codes,
            'full_text': all_text
        }
        
        return self.vocabulary_base
    
    def clean_extracted_text(self, text):
        """
        Nettoie le texte extrait par OCR
        """
        if not text:
            return ""
        
        # Supprimer les caractères de contrôle
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', text)
        
        # Corriger les erreurs OCR communes
        corrections = {
            r'\b0\b': 'O',  # 0 -> O
            r'\bl\b': 'I',  # l -> I
            r'\brn\b': 'm',  # rn -> m
        }
        
        for pattern, replacement in corrections.items():
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
        
        # Normaliser les espaces
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def get_extraction_summary(self, page_data):
        """
        Résumé des méthodes d'extraction utilisées
        """
        methods = defaultdict(int)
        for page in page_data:
            methods[page['extraction_method']] += 1
        
        return dict(methods)
    
    def analyze_technical_vocabulary(self, text):
        """
        Analyse le vocabulaire technique
        """
        words = text.lower().split()
        word_freq = defaultdict(int)
        
        # Compter la fréquence des mots
        for word in words:
            clean_word = re.sub(r'[^\w]', '', word)
            if len(clean_word) > 2:
                word_freq[clean_word] += 1
        
        # Identifier les termes techniques
        technical_words = []
        for word, freq in word_freq.items():
            if (word in self.medical_keywords or 
                len(word) > 6 or
                word.endswith(('ing', 'tion', 'ment', 'ance', 'ence'))):
                technical_words.append({'word': word, 'frequency': freq})
        
        # Trier par fréquence
        technical_words.sort(key=lambda x: x['frequency'], reverse=True)
        most_frequent = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'total_unique_words': len(word_freq),
            'most_frequent': most_frequent[:100],
            'technical_words': technical_words[:100],
            'medical_terms_found': [w for w in self.medical_keywords if w in word_freq]
        }
    
    def categorize_technical_terms(self, text):
        """
        Catégorise les termes techniques par domaine
        """
        categories = {
            'imaging_system': ['mri', 'magnetic', 'resonance', 'imaging', 'scan', 'tesla', 'bore'],
            'hardware_components': ['coil', 'magnet', 'gradient', 'shim', 'cable', 'connector', 'board'],
            'rf_system': ['rf', 'transmit', 'receive', 'frequency', 'antenna', 'amplifier'],
            'patient_interface': ['patient', 'table', 'gantry', 'positioning', 'comfort'],
            'software_control': ['software', 'control', 'interface', 'system', 'protocol'],
            'maintenance_service': ['maintenance', 'service', 'repair', 'replacement', 'calibration'],
            'safety_compliance': ['safety', 'warning', 'caution', 'compliance', 'regulation']
        }
        
        categorized = {}
        text_lower = text.lower()
        
        for category, keywords in categories.items():
            found_terms = []
            term_counts = defaultdict(int)
            
            for keyword in keywords:
                # Chercher le mot et ses variations
                pattern = r'\b' + keyword + r'\w*\b'
                matches = re.findall(pattern, text_lower)
                found_terms.extend(matches)
                
                for match in matches:
                    term_counts[match] += 1
            
            categorized[category] = {
                'terms': list(set(found_terms)),
                'counts': dict(term_counts),
                'total_occurrences': len(found_terms)
            }
        
        return categorized
    
    def extract_all_codes(self, text):
        """
        Extrait tous les codes techniques
        """
        all_codes = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            for pattern in self.part_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    code_info = {
                        'code': match.group(0),
                        'line_number': line_num + 1,
                        'line_text': line,
                        'pattern_used': pattern
                    }
                    all_codes.append(code_info)
        
        return all_codes
    
    def save_vocabulary_base(self, output_folder='output'):
        """
        Sauvegarde le socle de vocabulaire
        """
        os.makedirs(output_folder, exist_ok=True)
        
        # JSON complet
        json_file = f'{output_folder}/pim_technical_vocabulary_base.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.vocabulary_base, f, indent=2, ensure_ascii=False, default=str)
        
        # CSV des termes par catégorie
        if self.vocabulary_base['technical_terms']:
            terms_data = []
            for category, data in self.vocabulary_base['technical_terms'].items():
                for term, count in data['counts'].items():
                    terms_data.append({
                        'category': category,
                        'term': term,
                        'count': count
                    })
            
            if terms_data:
                terms_df = pd.DataFrame(terms_data)
                terms_df.to_csv(f'{output_folder}/pim_technical_terms.csv', index=False)
        
        # CSV des codes extraits
        if self.vocabulary_base['extracted_codes']:
            codes_df = pd.DataFrame(self.vocabulary_base['extracted_codes'])
            codes_df.to_csv(f'{output_folder}/pim_extracted_codes.csv', index=False)
        
        # Rapport de synthèse
        self.generate_summary_report(output_folder)
        
        print(f"\n✅ SOCLE DE VOCABULAIRE SAUVEGARDÉ:")
        print(f"   📄 JSON: {json_file}")
        print(f"   📊 Termes: {output_folder}/pim_technical_terms.csv")
        print(f"   🏷️ Codes: {output_folder}/pim_extracted_codes.csv")
        print(f"   📋 Rapport: {output_folder}/pim_vocabulary_report.txt")
    
    def generate_summary_report(self, output_folder):
        """
        Génère un rapport de synthèse
        """
        report_file = f'{output_folder}/pim_vocabulary_report.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("RAPPORT D'EXTRACTION VOCABULAIRE TECHNIQUE PIM\n")
            f.write("=" * 60 + "\n\n")
            
            # Informations document
            doc_info = self.vocabulary_base['document_info']
            f.write("INFORMATIONS DOCUMENT\n")
            f.write("-" * 25 + "\n")
            f.write(f"Fichier: {doc_info['pdf_path']}\n")
            f.write(f"Pages totales: {doc_info['total_pages']}\n")
            f.write(f"Mots totaux: {doc_info['total_words']:,}\n")
            f.write(f"Mots uniques: {doc_info['unique_words']:,}\n\n")
            
            # Méthodes d'extraction
            f.write("MÉTHODES D'EXTRACTION\n")
            f.write("-" * 25 + "\n")
            for method, count in doc_info['extraction_summary'].items():
                f.write(f"{method}: {count} pages\n")
            f.write("\n")
            
            # Termes techniques par catégorie
            f.write("TERMES TECHNIQUES PAR CATÉGORIE\n")
            f.write("-" * 35 + "\n")
            for category, data in self.vocabulary_base['technical_terms'].items():
                if data['total_occurrences'] > 0:
                    f.write(f"{category}: {data['total_occurrences']} occurrences\n")
                    f.write(f"  Termes uniques: {len(data['terms'])}\n")
            f.write("\n")
            
            # Codes extraits
            f.write("CODES TECHNIQUES EXTRAITS\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total codes: {len(self.vocabulary_base['extracted_codes'])}\n")
            
            # Top 10 des codes
            if self.vocabulary_base['extracted_codes']:
                f.write("Exemples de codes:\n")
                for i, code in enumerate(self.vocabulary_base['extracted_codes'][:10]):
                    f.write(f"  {i+1}. {code['code']}\n")

def main():
    """
    Fonction principale - Extraction vocabulaire technique avec OCR
    """
    print("🔍 PIM OCR EXTRACTOR - CRÉATION SOCLE VOCABULAIRE")
    print("=" * 70)
    
    # Initialiser l'extracteur
    extractor = PIMOCRExtractor()
    
    # Chemin vers le PDF
    pdf_path = r"input\PIM\3.0T Signa PET MR.pdf"
    
    try:
        # Vérifier que le fichier existe
        if not os.path.exists(pdf_path):
            print(f"❌ Fichier non trouvé: {pdf_path}")
            print("📁 Fichiers PIM disponibles:")
            pim_folder = r"input\PIM"
            if os.path.exists(pim_folder):
                for file in os.listdir(pim_folder):
                    if file.endswith('.pdf'):
                        print(f"   - {file}")
            return
        
        # Extraction du vocabulaire
        vocabulary_base = extractor.extract_vocabulary_from_pdf(pdf_path)
        
        # Affichage des résultats
        print(f"\n📊 RÉSULTATS DE L'EXTRACTION:")
        doc_info = vocabulary_base['document_info']
        print(f"   📄 Pages: {doc_info['total_pages']}")
        print(f"   📝 Mots totaux: {doc_info['total_words']:,}")
        print(f"   🔤 Mots uniques: {doc_info['unique_words']:,}")
        print(f"   🏷️ Codes extraits: {len(vocabulary_base['extracted_codes'])}")
        
        # Méthodes d'extraction utilisées
        print(f"\n🔧 MÉTHODES D'EXTRACTION:")
        for method, count in doc_info['extraction_summary'].items():
            print(f"   {method}: {count} pages")
        
        # Top termes techniques
        print(f"\n🔝 TOP TERMES TECHNIQUES:")
        tech_words = vocabulary_base['vocabulary_analysis']['technical_words']
        for i, term in enumerate(tech_words[:10]):
            print(f"   {i+1}. {term['word']} ({term['frequency']} fois)")
        
        # Sauvegarder le socle
        extractor.save_vocabulary_base()
        
        print(f"\n🚀 SOCLE DE VOCABULAIRE TECHNIQUE CRÉÉ AVEC SUCCÈS!")
        print(f"📁 Utilisez ce socle pour analyser d'autres documents PIM")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
