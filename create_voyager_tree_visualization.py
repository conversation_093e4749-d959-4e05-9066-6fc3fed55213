import pandas as pd
import json
from anytree import Node, RenderTree, find

def load_tree_data():
    """Load the tree statistics and sample data"""
    
    # Load statistics
    with open('output/voyager_bom_tree_stats.json', 'r', encoding='utf-8') as f:
        stats = json.load(f)
    
    # Load tree sample
    with open('output/voyager_bom_tree_sample.json', 'r', encoding='utf-8') as f:
        tree_sample = json.load(f)
    
    return stats, tree_sample

def create_mermaid_diagram(tree_sample, max_depth=3, max_children=5):
    """Create a Mermaid diagram from tree sample"""
    
    mermaid_lines = ["graph TD"]
    node_counter = 0
    
    def process_node(node, parent_id=None, depth=0):
        nonlocal node_counter
        
        if depth > max_depth:
            return
        
        # Create node ID
        current_id = f"N{node_counter}"
        node_counter += 1
        
        # Clean node name for display
        item_id = node.get('item_id', 'Unknown')[:15]
        level = node.get('level', -1)
        f_e_line = node.get('f_e_line', '')[:10]
        
        # Create node label
        if level == -1:
            label = f"🏭 {item_id}"
            style = "fill:#e1f5fe,stroke:#01579b,stroke-width:3px"
        elif level == 0:
            label = f"📦 {item_id}<br/>L{level}"
            style = "fill:#f3e5f5,stroke:#4a148c,stroke-width:2px"
        elif level == 1:
            label = f"🔧 {item_id}<br/>L{level}"
            style = "fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px"
        elif level == 2:
            label = f"⚙️ {item_id}<br/>L{level}"
            style = "fill:#fff3e0,stroke:#e65100,stroke-width:2px"
        else:
            label = f"🔩 {item_id}<br/>L{level}"
            style = "fill:#fce4ec,stroke:#880e4f,stroke-width:1px"
        
        # Add node definition
        mermaid_lines.append(f'    {current_id}["{label}"]')
        mermaid_lines.append(f'    style {current_id} {style}')
        
        # Add edge from parent
        if parent_id is not None:
            mermaid_lines.append(f'    {parent_id} --> {current_id}')
        
        # Process children
        children = node.get('children', [])[:max_children]
        for child in children:
            if child.get('level', -1) != -999:  # Skip "more children" placeholder
                process_node(child, current_id, depth + 1)
        
        return current_id
    
    # Process the tree
    process_node(tree_sample)
    
    return '\n'.join(mermaid_lines)

def create_f_e_line_summary(stats):
    """Create a summary of F-E-Line distribution"""
    
    f_e_lines = stats['f_e_line_distribution']
    
    summary = []
    summary.append("# 🌳 VOYAGER BOM TREE - F-E-LINE ANALYSIS")
    summary.append("=" * 60)
    summary.append("")
    
    # Overall statistics
    summary.append("## 📊 OVERALL STATISTICS:")
    summary.append(f"- **Total Nodes**: {stats['total_nodes']:,}")
    summary.append(f"- **Max Level**: {stats['max_level']}")
    summary.append(f"- **Total F-E-Lines**: {stats['total_f_e_lines']}")
    summary.append("")
    
    # Level distribution
    summary.append("## 🔢 LEVEL DISTRIBUTION:")
    level_dist = stats['level_distribution']
    for level in sorted([int(k) for k in level_dist.keys()]):
        count = level_dist[str(level)]
        percentage = (count / stats['total_nodes']) * 100
        if level == -1:
            summary.append(f"- **Root (Voyager)**: {count:,} items ({percentage:.1f}%)")
        else:
            summary.append(f"- **Level {level}**: {count:,} items ({percentage:.1f}%)")
    summary.append("")
    
    # Top F-E-Lines
    summary.append("## 🌿 TOP F-E-LINES BY ITEM COUNT:")
    sorted_f_e_lines = sorted(f_e_lines.items(), key=lambda x: x[1], reverse=True)
    
    for i, (f_e_line, count) in enumerate(sorted_f_e_lines[:15]):
        percentage = (count / stats['total_nodes']) * 100
        if f_e_line == "ROOT":
            summary.append(f"{i+1:2d}. **{f_e_line}** - {count:,} items ({percentage:.1f}%) - 🏭 Main Product")
        else:
            summary.append(f"{i+1:2d}. **{f_e_line}** - {count:,} items ({percentage:.1f}%)")
    
    summary.append("")
    
    # F-E-Line categories
    summary.append("## 🔍 F-E-LINE CATEGORIES:")
    
    # Group by first number
    categories = {}
    for f_e_line, count in f_e_lines.items():
        if f_e_line == "ROOT":
            continue
        
        parts = f_e_line.split('-')
        if len(parts) >= 1:
            category = parts[0]
            if category not in categories:
                categories[category] = []
            categories[category].append((f_e_line, count))
    
    for category in sorted(categories.keys()):
        items = categories[category]
        total_items = sum(count for _, count in items)
        summary.append(f"### Category {category}: {total_items:,} items")
        
        # Show top 5 in each category
        sorted_items = sorted(items, key=lambda x: x[1], reverse=True)[:5]
        for f_e_line, count in sorted_items:
            summary.append(f"  - {f_e_line}: {count:,} items")
        
        if len(items) > 5:
            summary.append(f"  - ... and {len(items) - 5} more F-E-Lines")
        summary.append("")
    
    return '\n'.join(summary)

def create_hierarchy_explanation():
    """Create an explanation of the hierarchy rules"""
    
    explanation = []
    explanation.append("# 🏗️ VOYAGER BOM HIERARCHICAL STRUCTURE RULES")
    explanation.append("=" * 60)
    explanation.append("")
    
    explanation.append("## 📋 HIERARCHY RULES IMPLEMENTED:")
    explanation.append("")
    explanation.append("### 🌳 **Root Level**")
    explanation.append("- **VOYAGER** = Main product (Level -1)")
    explanation.append("- All other components are children of Voyager")
    explanation.append("")
    
    explanation.append("### 🔄 **F-E-Line Changes**")
    explanation.append("- When F-E-Line changes → New direct child of Voyager")
    explanation.append("- Example: `1-1-1` → `1-1-3` = New branch from Voyager")
    explanation.append("")
    
    explanation.append("### 📊 **Level Hierarchy**")
    explanation.append("- **Level 0**: Direct children of Voyager")
    explanation.append("- **Level 1**: Major assemblies/components")
    explanation.append("- **Level 2+**: Sub-assemblies and parts")
    explanation.append("- **Level increase (+1)**: Child of previous item")
    explanation.append("- **Level decrease**: New branch at appropriate parent level")
    explanation.append("")
    
    explanation.append("### 🎯 **Parent-Child Relationships**")
    explanation.append("- Same F-E-Line + Level 1 = Parent/Father")
    explanation.append("- Level increases by 1 = Child of previous item")
    explanation.append("- Level decreases = Return to previous parent level")
    explanation.append("- F-E-Line change = New son of main product (Voyager)")
    explanation.append("")
    
    explanation.append("## 🔍 **STRUCTURE EXAMPLE:**")
    explanation.append("```")
    explanation.append("VOYAGER (Root)")
    explanation.append("├── F-E-Line: 1-1-1")
    explanation.append("│   ├── M7000JS (Level 1) - INHANCE SUITE")
    explanation.append("│   │   └── 5370796 (Level 2) - Inhance Option key")
    explanation.append("│   │       └── 5324610 (Level 2) - 15T Inhance")
    explanation.append("│   ├── G6001NA (Level 1) - 1.5T TDI Posterior Array")
    explanation.append("│   │   └── 5724203 (Level 2) - 1.5T KPA Coil")
    explanation.append("│   └── M70022HF-CAB (Level 1) - System Cabinet")
    explanation.append("├── F-E-Line: 1-1-3")
    explanation.append("│   └── [New branch components...]")
    explanation.append("└── F-E-Line: 2-2-72")
    explanation.append("    └── [Another branch components...]")
    explanation.append("```")
    explanation.append("")
    
    return '\n'.join(explanation)

def create_sankey_style_html(stats, tree_sample, max_levels=8):
    """Create an interactive Sankey-style HTML visualization"""

    # Prepare level data
    level_dist = stats['level_distribution']
    levels_data = []

    for level in range(-1, min(max_levels, stats['max_level'] + 1)):
        level_str = str(level)
        if level_str in level_dist:
            count = level_dist[level_str]
            if level == -1:
                levels_data.append({
                    'level': level,
                    'name': 'ROOT',
                    'count': count,
                    'color': '#e74c3c',
                    'description': 'VOYAGER Main Product'
                })
            elif level == 0:
                levels_data.append({
                    'level': level,
                    'name': f'Level {level}',
                    'count': count,
                    'color': '#9b59b6',
                    'description': 'Direct Components'
                })
            elif level == 1:
                levels_data.append({
                    'level': level,
                    'name': f'Level {level}',
                    'count': count,
                    'color': '#27ae60',
                    'description': 'Major Assemblies'
                })
            elif level == 2:
                levels_data.append({
                    'level': level,
                    'name': f'Level {level}',
                    'count': count,
                    'color': '#f39c12',
                    'description': 'Sub-assemblies'
                })
            elif level == 3:
                levels_data.append({
                    'level': level,
                    'name': f'Level {level}',
                    'count': count,
                    'color': '#3498db',
                    'description': 'Components'
                })
            else:
                levels_data.append({
                    'level': level,
                    'name': f'Level {level}',
                    'count': count,
                    'color': '#95a5a6',
                    'description': 'Detailed Parts'
                })

    # Create HTML content
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voyager BOM Hierarchical Levels - Interactive Sankey Style</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}

        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}

        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}

        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}

        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 1.1em;
        }}

        .stats {{
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }}

        .stat-item {{
            text-align: center;
        }}

        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }}

        .stat-label {{
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }}

        .visualization {{
            padding: 40px;
            min-height: 600px;
        }}

        .level-container {{
            display: flex;
            align-items: center;
            margin: 20px 0;
            position: relative;
        }}

        .level-bar {{
            height: 60px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }}

        .level-bar:hover {{
            transform: translateX(10px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }}

        .level-bar::before {{
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }}

        .level-bar:hover::before {{
            left: 100%;
        }}

        .level-info {{
            display: flex;
            align-items: center;
        }}

        .level-icon {{
            font-size: 1.5em;
            margin-right: 15px;
        }}

        .level-details {{
            display: flex;
            flex-direction: column;
        }}

        .level-name {{
            font-size: 1.2em;
            margin-bottom: 2px;
        }}

        .level-description {{
            font-size: 0.9em;
            opacity: 0.9;
        }}

        .level-count {{
            font-size: 1.5em;
            font-weight: bold;
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
        }}

        .connections {{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }}

        .connection-line {{
            stroke: #bdc3c7;
            stroke-width: 2;
            opacity: 0.3;
        }}

        .tooltip {{
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9em;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 1000;
        }}

        .f-e-line-info {{
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }}

        .f-e-line-title {{
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }}

        .f-e-line-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }}

        .f-e-line-item {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}

        .f-e-line-name {{
            font-weight: bold;
            color: #2c3e50;
        }}

        .f-e-line-count {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌳 VOYAGER BOM Hierarchical Structure</h1>
            <p>Interactive Level-based Visualization</p>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{stats['total_nodes']:,}</div>
                <div class="stat-label">Total Components</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{stats['max_level']}</div>
                <div class="stat-label">Max Depth</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{stats['total_f_e_lines']}</div>
                <div class="stat-label">F-E-Lines</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{len(levels_data)}</div>
                <div class="stat-label">Levels Shown</div>
            </div>
        </div>

        <div class="visualization">
            <div id="levels-container">
"""

    # Add level bars
    for i, level_data in enumerate(levels_data):
        percentage = (level_data['count'] / stats['total_nodes']) * 100
        width_percentage = max(20, min(95, percentage * 3))  # Scale for visibility

        icon = "🏭" if level_data['level'] == -1 else "📦" if level_data['level'] == 0 else "🔧" if level_data['level'] == 1 else "⚙️" if level_data['level'] == 2 else "🔩"

        html_content += f"""
                <div class="level-container">
                    <div class="level-bar"
                         style="background: {level_data['color']}; width: {width_percentage}%;"
                         data-level="{level_data['level']}"
                         data-count="{level_data['count']}"
                         data-percentage="{percentage:.1f}">
                        <div class="level-info">
                            <div class="level-icon">{icon}</div>
                            <div class="level-details">
                                <div class="level-name">{level_data['name']}</div>
                                <div class="level-description">{level_data['description']}</div>
                            </div>
                        </div>
                        <div class="level-count">{level_data['count']:,}</div>
                    </div>
                </div>
"""

    # Add F-E-Line information
    f_e_lines = stats['f_e_line_distribution']
    top_f_e_lines = sorted(f_e_lines.items(), key=lambda x: x[1], reverse=True)[:12]

    html_content += f"""
            </div>

            <div class="f-e-line-info">
                <div class="f-e-line-title">🌿 Top F-E-Lines Distribution</div>
                <div class="f-e-line-grid">
"""

    for f_e_line, count in top_f_e_lines:
        if f_e_line != "ROOT":
            percentage = (count / stats['total_nodes']) * 100
            html_content += f"""
                    <div class="f-e-line-item">
                        <div class="f-e-line-name">{f_e_line}</div>
                        <div class="f-e-line-count">{count:,} items ({percentage:.1f}%)</div>
                    </div>
"""

    html_content += """
                </div>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // Add interactivity
        const levelBars = document.querySelectorAll('.level-bar');
        const tooltip = document.getElementById('tooltip');

        levelBars.forEach(bar => {
            bar.addEventListener('mouseenter', function(e) {
                const level = this.dataset.level;
                const count = this.dataset.count;
                const percentage = this.dataset.percentage;

                tooltip.innerHTML = `
                    <strong>Level ${level}</strong><br>
                    Components: ${count}<br>
                    Percentage: ${percentage}%
                `;
                tooltip.style.opacity = '1';
            });

            bar.addEventListener('mousemove', function(e) {
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 10 + 'px';
            });

            bar.addEventListener('mouseleave', function() {
                tooltip.style.opacity = '0';
            });

            bar.addEventListener('click', function() {
                const level = this.dataset.level;
                alert(`Level ${level} details:\\n\\nThis level contains ${this.dataset.count} components representing ${this.dataset.percentage}% of the total BOM structure.`);
            });
        });

        // Add smooth animations on load
        window.addEventListener('load', function() {
            levelBars.forEach((bar, index) => {
                setTimeout(() => {
                    bar.style.opacity = '0';
                    bar.style.transform = 'translateX(-100px)';
                    bar.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        bar.style.opacity = '1';
                        bar.style.transform = 'translateX(0)';
                    }, 50);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
"""

    return html_content

def main():
    """Main function to create tree visualizations"""

    print("🎨 CREATING VOYAGER BOM TREE VISUALIZATIONS")
    print("=" * 60)

    # Load data
    stats, tree_sample = load_tree_data()

    # Create Mermaid diagram
    print("📊 Creating Mermaid diagram...")
    mermaid_diagram = create_mermaid_diagram(tree_sample, max_depth=4, max_children=8)

    with open('output/voyager_bom_tree_mermaid.md', 'w', encoding='utf-8') as f:
        f.write("# Voyager BOM Hierarchical Tree\n\n")
        f.write("```mermaid\n")
        f.write(mermaid_diagram)
        f.write("\n```\n")

    # Create Sankey-style HTML visualization
    print("🌊 Creating Sankey-style interactive visualization...")
    sankey_html = create_sankey_style_html(stats, tree_sample)

    with open('output/voyager_bom_sankey_levels.html', 'w', encoding='utf-8') as f:
        f.write(sankey_html)

    # Create F-E-Line summary
    print("📋 Creating F-E-Line analysis...")
    f_e_line_summary = create_f_e_line_summary(stats)

    with open('output/voyager_bom_f_e_line_analysis.md', 'w', encoding='utf-8') as f:
        f.write(f_e_line_summary)

    # Create hierarchy explanation
    print("📖 Creating hierarchy explanation...")
    hierarchy_explanation = create_hierarchy_explanation()

    with open('output/voyager_bom_hierarchy_rules.md', 'w', encoding='utf-8') as f:
        f.write(hierarchy_explanation)

    print("\n💾 VISUALIZATION FILES CREATED:")
    print("  📊 voyager_bom_tree_mermaid.md - Interactive tree diagram")
    print("  🌊 voyager_bom_sankey_levels.html - Sankey-style level visualization")
    print("  📋 voyager_bom_f_e_line_analysis.md - F-E-Line analysis")
    print("  📖 voyager_bom_hierarchy_rules.md - Hierarchy rules explanation")

    print(f"\n🎉 TREE VISUALIZATION COMPLETED!")
    print(f"📈 Processed {stats['total_nodes']:,} nodes across {stats['total_f_e_lines']} F-E-Lines")
    print(f"🏗️ Maximum depth: {stats['max_level']} levels")

if __name__ == "__main__":
    main()
