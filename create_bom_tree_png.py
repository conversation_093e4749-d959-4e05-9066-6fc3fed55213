import json
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np
from collections import defaultdict

def load_tree_data():
    """Load the tree statistics"""
    with open('output/voyager_bom_tree_stats.json', 'r', encoding='utf-8') as f:
        stats = json.load(f)
    
    with open('output/voyager_bom_tree_sample.json', 'r', encoding='utf-8') as f:
        tree_sample = json.load(f)
    
    return stats, tree_sample

def extract_tree_structure(node, level=0, max_level=5, parent_pos=None):
    """Extract tree structure for visualization"""
    if level > max_level:
        return []
    
    nodes = []
    
    # Current node
    current_node = {
        'name': node.get('item_id', 'Unknown')[:15],
        'level': node.get('level', -1),
        'f_e_line': node.get('f_e_line', ''),
        'description': node.get('description', '')[:30],
        'x': 0,  # Will be calculated
        'y': level,
        'parent_pos': parent_pos
    }
    nodes.append(current_node)
    
    # Children
    children = node.get('children', [])
    if children and level < max_level:
        # Limit children for better visualization
        max_children = 6 if level < 3 else 3
        for i, child in enumerate(children[:max_children]):
            child_nodes = extract_tree_structure(
                child, 
                level + 1, 
                max_level, 
                (0, level)  # Will be updated with actual position
            )
            nodes.extend(child_nodes)
    
    return nodes

def create_bom_tree_png():
    """Create a PNG visualization of the BOM tree"""
    
    # Load data
    stats, tree_sample = load_tree_data()
    
    # Extract tree structure
    tree_nodes = extract_tree_structure(tree_sample, max_level=5)
    
    # Organize nodes by level
    levels = defaultdict(list)
    for node in tree_nodes:
        levels[node['y']].append(node)
    
    # Calculate positions
    max_width = max(len(nodes) for nodes in levels.values())
    
    for level, nodes in levels.items():
        for i, node in enumerate(nodes):
            # Spread nodes horizontally within each level
            if len(nodes) == 1:
                node['x'] = max_width / 2
            else:
                node['x'] = (i / (len(nodes) - 1)) * max_width if len(nodes) > 1 else max_width / 2
    
    # Create the plot
    fig, ax = plt.subplots(1, 1, figsize=(20, 12))
    fig.patch.set_facecolor('white')
    
    # Define colors for each level
    level_colors = {
        -1: '#e74c3c',  # Red for root
        0: '#9b59b6',   # Purple
        1: '#27ae60',   # Green
        2: '#f39c12',   # Orange
        3: '#3498db',   # Blue
        4: '#1abc9c',   # Turquoise
        5: '#34495e',   # Dark gray
    }
    
    # Draw connections first (so they appear behind nodes)
    for node in tree_nodes:
        if node['parent_pos'] is not None:
            # Find parent node
            parent_level = node['y'] - 1
            if parent_level >= 0:
                parent_nodes = levels[parent_level]
                if parent_nodes:
                    # Connect to the closest parent (simplified)
                    parent = parent_nodes[0] if len(parent_nodes) == 1 else parent_nodes[min(len(parent_nodes)-1, int(node['x']))]
                    
                    # Draw connection line
                    ax.plot([parent['x'], node['x']], 
                           [parent['y'], node['y']], 
                           'k-', alpha=0.6, linewidth=2, zorder=1)
    
    # Draw nodes
    for node in tree_nodes:
        level = node['y']
        color = level_colors.get(node['level'], '#95a5a6')
        
        # Create fancy box for node
        box = FancyBboxPatch(
            (node['x'] - 0.4, level - 0.15),
            0.8, 0.3,
            boxstyle="round,pad=0.02",
            facecolor=color,
            edgecolor='black',
            linewidth=2,
            alpha=0.9,
            zorder=2
        )
        ax.add_patch(box)
        
        # Add text
        ax.text(node['x'], level, node['name'], 
               ha='center', va='center', 
               fontsize=8, fontweight='bold', 
               color='white' if node['level'] != 2 else 'black',
               zorder=3)
    
    # Customize the plot
    ax.set_xlim(-0.5, max_width + 0.5)
    ax.set_ylim(-0.5, len(levels) - 0.5)
    
    # Invert y-axis so root is at top
    ax.invert_yaxis()
    
    # Add level labels
    for level in range(len(levels)):
        if level == 0:
            label = "ROOT - VOYAGER"
        else:
            label = f"Level {level}"
        
        ax.text(-0.3, level, label, 
               ha='right', va='center', 
               fontsize=12, fontweight='bold',
               bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.7))
    
    # Remove axes
    ax.set_xticks([])
    ax.set_yticks([])
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_visible(False)
    ax.spines['left'].set_visible(False)
    
    # Add title
    plt.suptitle('🌳 VOYAGER BOM - Tree Arborescence Structure', 
                fontsize=24, fontweight='bold', y=0.95)
    
    # Add subtitle with statistics
    subtitle = f"Total Components: {stats['total_nodes']:,} | Max Depth: {stats['max_level']} | F-E-Lines: {stats['total_f_e_lines']}"
    plt.figtext(0.5, 0.91, subtitle, ha='center', fontsize=14, style='italic')
    
    # Add legend
    legend_elements = []
    for level, color in level_colors.items():
        if level == -1:
            label = "Root System"
        elif level == 0:
            label = "Direct Components"
        elif level == 1:
            label = "Major Assemblies"
        elif level == 2:
            label = "Sub-assemblies"
        elif level == 3:
            label = "Components"
        else:
            label = f"Level {level}"
        
        legend_elements.append(patches.Patch(color=color, label=label))
    
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))
    
    # Adjust layout
    plt.tight_layout()
    
    # Save as PNG
    output_file = 'output/voyager_bom_tree_arborescence.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ PNG tree visualization saved: {output_file}")
    
    # Also save as high-resolution version
    output_file_hd = 'output/voyager_bom_tree_arborescence_HD.png'
    plt.savefig(output_file_hd, dpi=600, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ HD PNG tree visualization saved: {output_file_hd}")
    
    plt.show()
    
    return output_file

def create_level_distribution_png():
    """Create a PNG showing level distribution"""
    
    # Load data
    stats, _ = load_tree_data()
    
    # Prepare level data
    levels = []
    counts = []
    colors = []
    
    level_colors = {
        -1: '#e74c3c', 0: '#9b59b6', 1: '#27ae60', 2: '#f39c12', 
        3: '#3498db', 4: '#1abc9c', 5: '#34495e'
    }
    
    for level_str, count in stats['level_distribution'].items():
        level = int(level_str)
        if level <= 5:  # Show only first 6 levels
            levels.append(f"Level {level}" if level >= 0 else "ROOT")
            counts.append(count)
            colors.append(level_colors.get(level, '#95a5a6'))
    
    # Create horizontal bar chart
    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    fig.patch.set_facecolor('white')
    
    bars = ax.barh(levels, counts, color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)
    
    # Add value labels on bars
    for i, (bar, count) in enumerate(zip(bars, counts)):
        width = bar.get_width()
        ax.text(width + max(counts) * 0.01, bar.get_y() + bar.get_height()/2, 
               f'{count:,}', ha='left', va='center', fontweight='bold', fontsize=11)
    
    # Customize
    ax.set_xlabel('Number of Components', fontsize=14, fontweight='bold')
    ax.set_ylabel('Hierarchy Level', fontsize=14, fontweight='bold')
    ax.set_title('🌳 VOYAGER BOM - Component Distribution by Level', 
                fontsize=18, fontweight='bold', pad=20)
    
    # Add grid
    ax.grid(axis='x', alpha=0.3, linestyle='--')
    ax.set_axisbelow(True)
    
    # Format x-axis
    ax.ticklabel_format(style='plain', axis='x')
    
    # Add total annotation
    total = sum(counts)
    ax.text(0.98, 0.02, f'Total Components: {total:,}', 
           transform=ax.transAxes, ha='right', va='bottom',
           bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7),
           fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    
    # Save
    output_file = 'output/voyager_bom_level_distribution.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ Level distribution PNG saved: {output_file}")
    
    plt.show()
    
    return output_file

def main():
    """Main function"""
    print("🎨 CREATING BOM TREE PNG VISUALIZATIONS")
    print("=" * 50)
    
    # Create tree arborescence PNG
    tree_file = create_bom_tree_png()
    
    # Create level distribution PNG
    dist_file = create_level_distribution_png()
    
    print("\n🎉 PNG VISUALIZATIONS COMPLETED!")
    print(f"📁 Tree structure: {tree_file}")
    print(f"📁 Level distribution: {dist_file}")
    print("\n📊 Both files saved in high resolution (300 DPI)")

if __name__ == "__main__":
    main()
