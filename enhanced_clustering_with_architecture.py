#!/usr/bin/env python3
"""
Enhanced Clustering with Architecture Features
Integrates PowerPoint architecture data into BOM clustering
"""

import os
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import silhouette_score, calinski_harabasz_score
import matplotlib.pyplot as plt
import json
import re
from collections import Counter

def load_architecture_mapping(mapping_file):
    """Load architecture mapping from Excel template"""
    print("🏗️ Loading architecture mapping...")
    
    if not os.path.exists(mapping_file):
        print(f"⚠️  Architecture mapping file not found: {mapping_file}")
        print("📋 Creating default mapping based on component analysis...")
        return create_default_architecture_mapping()
    
    try:
        # Load from Excel template
        mapping_df = pd.read_excel(mapping_file, sheet_name='Architecture_Mapping')
        
        # Filter out empty rows
        mapping_df = mapping_df.dropna(subset=['Component_Name', 'Component_Type'])
        
        print(f"✅ Loaded {len(mapping_df)} architecture mappings")
        return mapping_df
        
    except Exception as e:
        print(f"❌ Error loading architecture mapping: {e}")
        return create_default_architecture_mapping()

def create_default_architecture_mapping():
    """Create default architecture mapping based on component analysis"""
    print("🔧 Creating intelligent default architecture mapping...")
    
    # Default mapping based on common patterns
    default_mapping = [
        # Core mandatory components (Blue)
        {'Component_Name': 'Main Processing Unit', 'Item_ID_Pattern': '*PROCESS*,*CPU*,*MAIN*', 
         'Component_Type': 'Mandatory', 'Architecture_Level': 1, 'Shipping_Impact': 1, 'Creation_Impact': 1},
        {'Component_Name': 'Power Supply', 'Item_ID_Pattern': '*POWER*,*PSU*,*SUPPLY*', 
         'Component_Type': 'Mandatory', 'Architecture_Level': 1, 'Shipping_Impact': 1, 'Creation_Impact': 1},
        {'Component_Name': 'Control Electronics', 'Item_ID_Pattern': '*CONTROL*,*CTRL*,*ELECTRONIC*', 
         'Component_Type': 'Mandatory', 'Architecture_Level': 1, 'Shipping_Impact': 1, 'Creation_Impact': 1},
        
        # Selectable components (Purple)
        {'Component_Name': 'Display Module', 'Item_ID_Pattern': '*DISPLAY*,*MONITOR*,*SCREEN*', 
         'Component_Type': 'Mandatory_Selectable', 'Architecture_Level': 2, 'Shipping_Impact': 1, 'Creation_Impact': 0},
        {'Component_Name': 'Memory Configuration', 'Item_ID_Pattern': '*MEMORY*,*RAM*,*MEM*', 
         'Component_Type': 'Mandatory_Selectable', 'Architecture_Level': 2, 'Shipping_Impact': 0, 'Creation_Impact': 1},
        {'Component_Name': 'Interface Module', 'Item_ID_Pattern': '*INTERFACE*,*I/O*,*CONNECTOR*', 
         'Component_Type': 'Mandatory_Selectable', 'Architecture_Level': 2, 'Shipping_Impact': 1, 'Creation_Impact': 0},
        
        # Optional components (Red)
        {'Component_Name': 'Extended Storage', 'Item_ID_Pattern': '*STORAGE*,*DISK*,*SSD*', 
         'Component_Type': 'Optional', 'Architecture_Level': 3, 'Shipping_Impact': 0, 'Creation_Impact': 0},
        {'Component_Name': 'Accessories', 'Item_ID_Pattern': '*ACCESSORY*,*OPTION*,*UPGRADE*', 
         'Component_Type': 'Optional', 'Architecture_Level': 4, 'Shipping_Impact': 0, 'Creation_Impact': 0},
        {'Component_Name': 'Software Licenses', 'Item_ID_Pattern': '*SOFTWARE*,*LICENSE*,*SW*', 
         'Component_Type': 'Optional', 'Architecture_Level': 4, 'Shipping_Impact': 0, 'Creation_Impact': 1},
    ]
    
    return pd.DataFrame(default_mapping)

def match_bom_to_architecture(bom_df, architecture_mapping):
    """Match BOM components to architecture mapping"""
    print("🔗 Matching BOM components to architecture...")
    
    # Initialize architecture features
    bom_df['Component_Type'] = 'Unclassified'
    bom_df['Architecture_Level'] = 0
    bom_df['Shipping_Impact'] = 0
    bom_df['Creation_Impact'] = 0
    bom_df['Architecture_Context'] = 'Unknown'
    
    matched_count = 0
    
    for _, arch_row in architecture_mapping.iterrows():
        patterns = str(arch_row['Item_ID_Pattern']).split(',')
        
        for pattern in patterns:
            pattern = pattern.strip()
            if not pattern:
                continue
                
            # Create regex pattern
            regex_pattern = pattern.replace('*', '.*').upper()
            
            # Match against Item_ID and Description
            id_mask = bom_df['Item_ID'].astype(str).str.upper().str.contains(regex_pattern, na=False, regex=True)
            desc_mask = bom_df['Item_Description'].astype(str).str.upper().str.contains(regex_pattern, na=False, regex=True)
            
            combined_mask = id_mask | desc_mask
            
            if combined_mask.any():
                # Update matched components
                bom_df.loc[combined_mask, 'Component_Type'] = arch_row['Component_Type']
                bom_df.loc[combined_mask, 'Architecture_Level'] = arch_row['Architecture_Level']
                bom_df.loc[combined_mask, 'Shipping_Impact'] = arch_row['Shipping_Impact']
                bom_df.loc[combined_mask, 'Creation_Impact'] = arch_row['Creation_Impact']
                bom_df.loc[combined_mask, 'Architecture_Context'] = arch_row.get('Context', 'Matched')
                
                matched_count += combined_mask.sum()
    
    print(f"✅ Matched {matched_count} components to architecture")
    
    # Show distribution
    type_counts = bom_df['Component_Type'].value_counts()
    print("📊 Architecture distribution:")
    for comp_type, count in type_counts.items():
        percentage = (count / len(bom_df)) * 100
        print(f"   {comp_type}: {count} ({percentage:.1f}%)")
    
    return bom_df

def create_enhanced_features(bom_df):
    """Create enhanced features including architecture data"""
    print("🔧 Creating enhanced features with architecture data...")
    
    # Original features (from previous analysis)
    # F-E-Line features
    bom_df['FE_Level_1'] = bom_df['F_E_Line'].astype(str).str.split('-').str[0].astype(float)
    bom_df['FE_Level_2'] = bom_df['F_E_Line'].astype(str).str.split('-').str[1].astype(float)
    bom_df['FE_Level_3'] = bom_df['F_E_Line'].astype(str).str.split('-').str[2].astype(float)
    
    # Item ID features
    bom_df['Item_Length'] = bom_df['Item_ID'].astype(str).str.len()
    bom_df['Item_Has_Numbers'] = bom_df['Item_ID'].astype(str).str.contains(r'\d').astype(int)
    bom_df['Item_Has_Letters'] = bom_df['Item_ID'].astype(str).str.contains(r'[A-Za-z]').astype(int)
    
    # Description features
    descriptions = bom_df['Item_Description'].astype(str).str.lower()
    bom_df['Desc_Length'] = descriptions.str.len()
    bom_df['Desc_Word_Count'] = descriptions.str.split().str.len()
    
    # NEW: Architecture features
    # Encode component type
    type_encoder = LabelEncoder()
    bom_df['Component_Type_Encoded'] = type_encoder.fit_transform(bom_df['Component_Type'])
    
    # Architecture level (already numeric)
    bom_df['Architecture_Level_Normalized'] = bom_df['Architecture_Level'] / 4.0  # Normalize to 0-1
    
    # Impact features (already binary)
    # Shipping_Impact and Creation_Impact already added
    
    # Combined impact score
    bom_df['Total_Impact_Score'] = (
        bom_df['Shipping_Impact'] * 0.6 + 
        bom_df['Creation_Impact'] * 0.4
    )
    
    # Criticality score based on component type
    criticality_map = {
        'Mandatory': 1.0,
        'Mandatory_Selectable': 0.7,
        'Optional': 0.3,
        'Unclassified': 0.1
    }
    bom_df['Criticality_Score'] = bom_df['Component_Type'].map(criticality_map)
    
    # Context encoding
    context_encoder = LabelEncoder()
    bom_df['Architecture_Context_Encoded'] = context_encoder.fit_transform(bom_df['Architecture_Context'])
    
    print("✅ Enhanced features created with architecture integration")
    return bom_df

def perform_enhanced_clustering(bom_df):
    """Perform clustering with enhanced architecture features"""
    print("🎯 Performing enhanced clustering with architecture features...")
    
    # Select features for clustering
    feature_columns = [
        # Original structural features
        'FE_Level_1', 'FE_Level_2', 'FE_Level_3',
        'Item_Length', 'Item_Has_Numbers', 'Item_Has_Letters',
        'Desc_Length', 'Desc_Word_Count',
        
        # NEW: Architecture features
        'Component_Type_Encoded',
        'Architecture_Level_Normalized',
        'Shipping_Impact',
        'Creation_Impact',
        'Total_Impact_Score',
        'Criticality_Score',
        'Architecture_Context_Encoded'
    ]
    
    # Prepare data
    X = bom_df[feature_columns].fillna(0)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Test multiple algorithms
    algorithms = {
        'enhanced_kmeans_8': KMeans(n_clusters=8, random_state=42, n_init=10),
        'enhanced_kmeans_12': KMeans(n_clusters=12, random_state=42, n_init=10),
        'enhanced_hierarchical_10': AgglomerativeClustering(n_clusters=10),
        'enhanced_dbscan_05': DBSCAN(eps=0.5, min_samples=5),
        'enhanced_dbscan_08': DBSCAN(eps=0.8, min_samples=8),
    }
    
    results = {}
    
    for algo_name, algorithm in algorithms.items():
        try:
            labels = algorithm.fit_predict(X_scaled)
            n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
            
            if n_clusters > 1:
                silhouette = silhouette_score(X_scaled, labels)
                calinski = calinski_harabasz_score(X_scaled, labels)
                
                results[algo_name] = {
                    'labels': labels,
                    'n_clusters': n_clusters,
                    'silhouette_score': silhouette,
                    'calinski_score': calinski,
                    'noise_points': sum(labels == -1) if -1 in labels else 0
                }
                
                print(f"  {algo_name}: {n_clusters} clusters, silhouette={silhouette:.3f}")
        
        except Exception as e:
            print(f"  ⚠️  {algo_name} failed: {e}")
    
    # Select best result
    if results:
        best_algo = max(results.keys(), key=lambda k: results[k]['silhouette_score'])
        best_result = results[best_algo]
        
        bom_df['Enhanced_Cluster'] = best_result['labels']
        
        print(f"\n🏆 Best algorithm: {best_algo}")
        print(f"📊 Clusters: {best_result['n_clusters']}")
        print(f"🎯 Silhouette score: {best_result['silhouette_score']:.3f}")
        
        return bom_df, best_result, results
    
    return bom_df, None, results

def analyze_enhanced_clusters(bom_df):
    """Analyze enhanced clusters with architecture context"""
    print("\n🔍 Analyzing enhanced clusters...")
    
    cluster_analysis = {}
    
    for cluster_id in sorted(bom_df['Enhanced_Cluster'].unique()):
        if cluster_id == -1:  # Skip noise
            continue
        
        cluster_data = bom_df[bom_df['Enhanced_Cluster'] == cluster_id]
        
        # Architecture analysis
        type_dist = dict(cluster_data['Component_Type'].value_counts())
        level_dist = dict(cluster_data['Architecture_Level'].value_counts())
        
        analysis = {
            'cluster_id': cluster_id,
            'size': len(cluster_data),
            'percentage': len(cluster_data) / len(bom_df) * 100,
            
            # Architecture characteristics
            'component_types': type_dist,
            'dominant_type': cluster_data['Component_Type'].mode().iloc[0] if len(cluster_data) > 0 else 'Unknown',
            'architecture_levels': level_dist,
            'avg_criticality': cluster_data['Criticality_Score'].mean(),
            'shipping_impact_rate': cluster_data['Shipping_Impact'].mean(),
            'creation_impact_rate': cluster_data['Creation_Impact'].mean(),
            
            # Traditional characteristics
            'top_fe_lines': dict(cluster_data['F_E_Line'].value_counts().head(3)),
            'sample_descriptions': list(cluster_data['Item_Description'].head(3)),
            
            # Business implications
            'acv_priority': 'High' if cluster_data['Criticality_Score'].mean() > 0.7 else 'Medium' if cluster_data['Criticality_Score'].mean() > 0.4 else 'Low',
            'circularity_strategy': determine_circularity_strategy(type_dist, cluster_data['Criticality_Score'].mean())
        }
        
        cluster_analysis[f'Enhanced_Cluster_{cluster_id}'] = analysis
    
    return cluster_analysis

def determine_circularity_strategy(type_dist, avg_criticality):
    """Determine circularity strategy based on component types"""
    dominant_type = max(type_dist.keys(), key=lambda k: type_dist[k]) if type_dist else 'Unknown'
    
    strategies = {
        'Mandatory': 'Standardization + Platform Reuse',
        'Mandatory_Selectable': 'Modularization + Interchangeability', 
        'Optional': 'Secondary Market + Upgrade Path',
        'Unclassified': 'Analysis Required'
    }
    
    base_strategy = strategies.get(dominant_type, 'Analysis Required')
    
    if avg_criticality > 0.8:
        return f"{base_strategy} (High Priority)"
    elif avg_criticality > 0.5:
        return f"{base_strategy} (Medium Priority)"
    else:
        return f"{base_strategy} (Low Priority)"

def save_enhanced_results(bom_df, cluster_analysis, results, output_folder):
    """Save enhanced clustering results"""
    print("\n💾 Saving enhanced clustering results...")
    
    # Save enhanced BOM data
    enhanced_file = os.path.join(output_folder, 'enhanced_bom_with_architecture.xlsx')
    bom_df.to_excel(enhanced_file, index=False)
    
    # Save cluster analysis
    if cluster_analysis:
        cluster_summary = []
        for cluster_name, analysis in cluster_analysis.items():
            summary_row = {
                'Cluster_ID': analysis['cluster_id'],
                'Size': analysis['size'],
                'Percentage': analysis['percentage'],
                'Dominant_Type': analysis['dominant_type'],
                'Avg_Criticality': analysis['avg_criticality'],
                'Shipping_Impact_Rate': analysis['shipping_impact_rate'],
                'Creation_Impact_Rate': analysis['creation_impact_rate'],
                'ACV_Priority': analysis['acv_priority'],
                'Circularity_Strategy': analysis['circularity_strategy']
            }
            cluster_summary.append(summary_row)
        
        cluster_file = os.path.join(output_folder, 'enhanced_cluster_analysis.xlsx')
        pd.DataFrame(cluster_summary).to_excel(cluster_file, index=False)
    
    # Save comprehensive report
    report_file = os.path.join(output_folder, 'enhanced_clustering_report.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("ENHANCED CLUSTERING WITH ARCHITECTURE INTEGRATION\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("METHODOLOGY\n")
        f.write("-" * 15 + "\n")
        f.write("Enhanced clustering combining:\n")
        f.write("- Original BOM features (structural, semantic)\n")
        f.write("- Architecture features (criticality, impact)\n")
        f.write("- PowerPoint color-coding logic\n")
        f.write("- Business context integration\n\n")
        
        if cluster_analysis:
            f.write("ENHANCED CLUSTER ANALYSIS\n")
            f.write("-" * 30 + "\n")
            for cluster_name, analysis in cluster_analysis.items():
                f.write(f"\n{cluster_name}:\n")
                f.write(f"  Size: {analysis['size']} items ({analysis['percentage']:.1f}%)\n")
                f.write(f"  Dominant Type: {analysis['dominant_type']}\n")
                f.write(f"  Criticality Score: {analysis['avg_criticality']:.2f}\n")
                f.write(f"  ACV Priority: {analysis['acv_priority']}\n")
                f.write(f"  Circularity Strategy: {analysis['circularity_strategy']}\n")
                f.write(f"  Component Types: {analysis['component_types']}\n")
    
    print("✅ Enhanced results saved")

def main():
    """Main enhanced clustering function"""
    print("🚀 ENHANCED CLUSTERING WITH ARCHITECTURE INTEGRATION")
    print("=" * 70)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Load Artist BOM data
    artist_file = r"C:\Users\<USER>\OneDrive - GE HealthCare\Documents\BOM_Cluster\input\4516256_0d1582e3-6687-4d88-94c5-4c98b07e76cb_1747378299412.xlsx"
    
    try:
        df = pd.read_excel(artist_file, sheet_name=1)
        df = df.iloc[:, :4]
        df.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
        df = df.dropna(subset=['F_E_Line', 'Item_ID', 'Item_Description'])
        df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
        
        print(f"✅ Loaded {len(df)} Artist BOM items")
        
    except Exception as e:
        print(f"❌ Error loading BOM data: {e}")
        return
    
    # Load architecture mapping
    mapping_file = os.path.join(output_folder, 'architecture_mapping_template.xlsx')
    architecture_mapping = load_architecture_mapping(mapping_file)
    
    # Match BOM to architecture
    df_enhanced = match_bom_to_architecture(df, architecture_mapping)
    
    # Create enhanced features
    df_features = create_enhanced_features(df_enhanced)
    
    # Perform enhanced clustering
    df_clustered, best_result, all_results = perform_enhanced_clustering(df_features)
    
    # Analyze clusters
    if best_result:
        cluster_analysis = analyze_enhanced_clusters(df_clustered)
        
        # Save results
        save_enhanced_results(df_clustered, cluster_analysis, all_results, output_folder)
        
        print(f"\n🎉 Enhanced Clustering Complete!")
        print("=" * 70)
        print(f"🏆 Best silhouette score: {best_result['silhouette_score']:.3f}")
        print(f"📊 Clusters created: {best_result['n_clusters']}")
        print(f"📁 Results saved in: {os.path.abspath(output_folder)}")

if __name__ == "__main__":
    main()
