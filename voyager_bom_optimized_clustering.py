import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from sklearn.preprocessing import StandardScaler
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re
import json

# OPTIMIZED DATA LOADING AND PREPROCESSING

def load_voyager_bom_data():
    """Load Voyager BOM data from available sources"""
    
    try:
        df = pd.read_excel("output/voyager_5408509_bom_pcm_aggregated.xlsx")
        print(f"Loaded enhanced BOM data: {len(df)} rows")
        
        column_mapping = {
            'F_E_Line': 'F-E-Line',
            'Item_Number': 'Item Number',
            'Item_Description': 'Item Description'
        }
        df = df.rename(columns=column_mapping)
        return df
    except Exception as e:
        print(f"Enhanced BOM loading failed: {e}")
        
        with open('output/voyager_bom_tree_stats.json', 'r') as f:
            stats = json.load(f)
        
        rows = []
        for level_str, count in stats['level_distribution'].items():
            level = int(level_str)
            for i in range(min(count, 1000)):  # Limit for testing
                rows.append({
                    'Level': level,
                    'Item Number': f'ITEM_{level}_{i+1:06d}',
                    'Item Description': f'Component Level {level} Item {i+1}',
                    'F-E-Line': f'1-1-{level+1}' if level >= 0 else 'ROOT'
                })
        
        df = pd.DataFrame(rows)
        print(f"Created synthetic BOM data: {len(df)} rows")
        return df

def remove_accessories_and_non_physical_items(df):
    """Remove accessories, manuals, software, and non-physical items"""
    
    exclusion_patterns = [
        r'manual|documentation|doc|guide|instruction',
        r'software|firmware|program|application|app',
        r'paper|label|sticker|decal|marking',
        r'accessory|accessories|optional|spare',
        r'packaging|box|container|bag|wrap',
        r'cable tie|zip tie|fastener|screw|bolt|nut',
        r'warranty|service|maintenance|support',
        r'training|education|course|tutorial',
        r'license|subscription|agreement|contract'
    ]
    
    combined_pattern = '|'.join(exclusion_patterns)
    initial_count = len(df)
    
    mask = ~df['Item Description'].str.contains(combined_pattern, case=False, na=False)
    df_filtered = df[mask].copy()
    
    item_exclusion_patterns = [r'^DOC', r'^MAN', r'^SW', r'^LIC', r'^ACC']
    
    for pattern in item_exclusion_patterns:
        mask = ~df_filtered['Item Number'].str.contains(pattern, case=False, na=False)
        df_filtered = df_filtered[mask]
    
    removed_count = initial_count - len(df_filtered)
    removed_items = df[~df.index.isin(df_filtered.index)]
    
    print(f"Removed {removed_count} non-physical items:")
    for pattern in exclusion_patterns:
        count = df['Item Description'].str.contains(pattern, case=False, na=False).sum()
        if count > 0:
            print(f"  - {pattern}: {count} items")
    
    return df_filtered, removed_items

# OPTIMIZED EMBEDDING GENERATION

def generate_optimized_embeddings(descriptions, method='tfidf'):
    """Generate embeddings using optimized methods"""
    
    if method == 'sbert':
        model = SentenceTransformer('all-MiniLM-L6-v2')
        embeddings = model.encode(descriptions.tolist(), batch_size=32, show_progress_bar=True)
        return embeddings
    
    elif method == 'tfidf':
        vectorizer = TfidfVectorizer(max_features=1000, stop_words='english', ngram_range=(1, 2))
        embeddings = vectorizer.fit_transform(descriptions).toarray()
        return embeddings, vectorizer
    
    else:
        raise ValueError("Method must be 'sbert' or 'tfidf'")

# CLUSTERING FUNCTIONS

def cluster_by_function(df, method='tfidf'):
    """Cluster components by their function/action"""
    
    functional_keywords = []
    for desc in df['Item Description']:
        words = re.findall(r'\b(?:pump|valve|sensor|motor|drive|control|filter|heat|cool|measure|detect|connect|support|mount|seal|protect)\b', 
                          desc.lower())
        functional_keywords.append(' '.join(words) if words else desc.lower())
    
    if method == 'tfidf':
        embeddings, vectorizer = generate_optimized_embeddings(pd.Series(functional_keywords), method='tfidf')
    else:
        embeddings = generate_optimized_embeddings(pd.Series(functional_keywords), method='sbert')
    
    results = {}
    
    for n_clusters in [5, 8, 10]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(embeddings)
        
        silhouette = silhouette_score(embeddings, labels)
        calinski = calinski_harabasz_score(embeddings, labels)
        davies_bouldin = davies_bouldin_score(embeddings, labels)
        
        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    for n_clusters in [5, 8, 10]:
        hierarchical = AgglomerativeClustering(n_clusters=n_clusters)
        labels = hierarchical.fit_predict(embeddings)
        
        silhouette = silhouette_score(embeddings, labels)
        calinski = calinski_harabasz_score(embeddings, labels)
        davies_bouldin = davies_bouldin_score(embeddings, labels)
        
        results[f'hierarchical_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    return results, embeddings

def cluster_by_structure(df, method='tfidf'):
    """Cluster components by their structural characteristics"""
    
    structural_features = []
    
    for idx, row in df.iterrows():
        features = []
        features.append(f"level_{row['Level']}")
        
        if 'F-E-Line' in row and pd.notna(row['F-E-Line']):
            fe_parts = str(row['F-E-Line']).split('-')
            for i, part in enumerate(fe_parts):
                features.append(f"fe_{i}_{part}")
        
        desc = str(row['Item Description']).lower()
        structural_terms = re.findall(r'\b(?:assembly|sub|component|part|module|unit|system|housing|frame|bracket|plate|panel)\b', desc)
        features.extend(structural_terms)
        
        structural_features.append(' '.join(features))
    
    if method == 'tfidf':
        embeddings, vectorizer = generate_optimized_embeddings(pd.Series(structural_features), method='tfidf')
    else:
        embeddings = generate_optimized_embeddings(pd.Series(structural_features), method='sbert')
    
    results = {}
    
    for n_clusters in [5, 8, 10]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(embeddings)
        
        silhouette = silhouette_score(embeddings, labels)
        calinski = calinski_harabasz_score(embeddings, labels)
        davies_bouldin = davies_bouldin_score(embeddings, labels)
        
        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    for n_clusters in [5, 8, 10]:
        hierarchical = AgglomerativeClustering(n_clusters=n_clusters)
        labels = hierarchical.fit_predict(embeddings)
        
        silhouette = silhouette_score(embeddings, labels)
        calinski = calinski_harabasz_score(embeddings, labels)
        davies_bouldin = davies_bouldin_score(embeddings, labels)
        
        results[f'hierarchical_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    return results, embeddings

def cluster_by_physical_assembly(df, method='tfidf'):
    """Cluster components by physical assembly characteristics"""
    
    assembly_features = []
    
    for idx, row in df.iterrows():
        features = []
        desc = str(row['Item Description']).lower()
        
        assembly_types = re.findall(r'\b(?:mechanical|electrical|hydraulic|pneumatic|electronic|optical|thermal)\b', desc)
        features.extend([f"type_{t}" for t in assembly_types])
        
        assembly_functions = re.findall(r'\b(?:rotating|linear|static|dynamic|fixed|movable|flexible|rigid)\b', desc)
        features.extend([f"function_{f}" for f in assembly_functions])
        
        physical_chars = re.findall(r'\b(?:metal|plastic|rubber|ceramic|composite|steel|aluminum|copper|brass)\b', desc)
        features.extend([f"material_{m}" for m in physical_chars])
        
        size_indicators = re.findall(r'\b(?:small|medium|large|micro|mini|standard|heavy|light)\b', desc)
        features.extend([f"size_{s}" for s in size_indicators])
        
        assembly_features.append(' '.join(features) if features else desc)
    
    if method == 'tfidf':
        embeddings, vectorizer = generate_optimized_embeddings(pd.Series(assembly_features), method='tfidf')
    else:
        embeddings = generate_optimized_embeddings(pd.Series(assembly_features), method='sbert')
    
    results = {}
    
    for n_clusters in [6, 8, 10]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(embeddings)
        
        silhouette = silhouette_score(embeddings, labels)
        calinski = calinski_harabasz_score(embeddings, labels)
        davies_bouldin = davies_bouldin_score(embeddings, labels)
        
        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    for n_clusters in [6, 8, 10]:
        hierarchical = AgglomerativeClustering(n_clusters=n_clusters)
        labels = hierarchical.fit_predict(embeddings)
        
        silhouette = silhouette_score(embeddings, labels)
        calinski = calinski_harabasz_score(embeddings, labels)
        davies_bouldin = davies_bouldin_score(embeddings, labels)
        
        results[f'hierarchical_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    return results, embeddings

def cluster_by_weight(df, method='tfidf'):
    """Cluster components by weight characteristics"""
    
    weight_features = []
    
    for desc in df['Item Description']:
        desc_lower = str(desc).lower()
        weight_terms = re.findall(r'\b(?:heavy|light|lightweight|massive|compact|dense|thin|thick)\b', desc_lower)
        size_weight = re.findall(r'\b(?:large|small|big|tiny|huge|miniature|standard|oversized)\b', desc_lower)
        density_materials = re.findall(r'\b(?:steel|iron|aluminum|titanium|plastic|carbon|composite)\b', desc_lower)
        
        features = weight_terms + size_weight + density_materials
        weight_features.append(' '.join(features) if features else 'standard_weight')
    
    if method == 'tfidf':
        embeddings, vectorizer = generate_optimized_embeddings(pd.Series(weight_features), method='tfidf')
    else:
        embeddings = generate_optimized_embeddings(pd.Series(weight_features), method='sbert')
    
    results = {}
    
    for n_clusters in [4, 6, 8]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(embeddings)
        
        silhouette = silhouette_score(embeddings, labels)
        calinski = calinski_harabasz_score(embeddings, labels)
        davies_bouldin = davies_bouldin_score(embeddings, labels)
        
        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    return results, embeddings

def cluster_by_material(df, method='tfidf'):
    """Cluster components by material characteristics"""
    
    material_features = []
    
    for desc in df['Item Description']:
        desc_lower = str(desc).lower()
        materials = re.findall(r'\b(?:steel|aluminum|plastic|rubber|ceramic|glass|carbon|titanium|brass|copper|bronze|iron|wood|fabric|leather|silicon|polymer)\b', desc_lower)
        properties = re.findall(r'\b(?:stainless|galvanized|anodized|coated|treated|hardened|tempered|reinforced|laminated)\b', desc_lower)
        categories = re.findall(r'\b(?:metal|metallic|non-metal|composite|synthetic|natural|organic|inorganic)\b', desc_lower)
        
        features = materials + properties + categories
        material_features.append(' '.join(features) if features else 'standard_material')
    
    if method == 'tfidf':
        embeddings, vectorizer = generate_optimized_embeddings(pd.Series(material_features), method='tfidf')
    else:
        embeddings = generate_optimized_embeddings(pd.Series(material_features), method='sbert')
    
    results = {}
    
    for n_clusters in [5, 7, 9]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(embeddings)
        
        silhouette = silhouette_score(embeddings, labels)
        calinski = calinski_harabasz_score(embeddings, labels)
        davies_bouldin = davies_bouldin_score(embeddings, labels)
        
        results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    return results, embeddings

def main():
    """Main execution function"""
    
    print("LOADING VOYAGER BOM DATA")
    df = load_voyager_bom_data()
    
    print("REMOVING NON-PHYSICAL ITEMS")
    df_clean, removed_items = remove_accessories_and_non_physical_items(df)
    
    print("CLUSTERING BY FUNCTION")
    func_results, func_embeddings = cluster_by_function(df_clean, method='tfidf')
    
    print("CLUSTERING BY STRUCTURE")
    struct_results, struct_embeddings = cluster_by_structure(df_clean, method='tfidf')
    
    print("CLUSTERING BY PHYSICAL ASSEMBLY")
    assembly_results, assembly_embeddings = cluster_by_physical_assembly(df_clean, method='tfidf')
    
    print("CLUSTERING BY WEIGHT")
    weight_results, weight_embeddings = cluster_by_weight(df_clean, method='tfidf')
    
    print("CLUSTERING BY MATERIAL")
    material_results, material_embeddings = cluster_by_material(df_clean, method='tfidf')
    
    return df_clean, removed_items, func_results, struct_results, assembly_results, weight_results, material_results

if __name__ == "__main__":
    df_clean, removed_items, func_results, struct_results, assembly_results, weight_results, material_results = main()
