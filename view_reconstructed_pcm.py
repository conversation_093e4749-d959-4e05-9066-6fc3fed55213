#!/usr/bin/env python3
"""
View Reconstructed PCM Extended BOM Sample
"""

import pandas as pd

def view_reconstructed_pcm():
    """View sample of reconstructed PCM"""
    
    print("📊 RECONSTRUCTED PCM EXTENDED BOM SAMPLE")
    print("=" * 60)
    
    try:
        # Load the reconstructed PCM
        df = pd.read_excel('output/reconstructed_pcm_extended_bom.xlsx', sheet_name='Extended_BOM_PCM')
        
        print(f"📋 Total Components: {len(df)}")
        print(f"📊 Total Columns: {len(df.columns)}")
        print(f"📄 Column Names:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        print(f"\n📋 SAMPLE DATA (First 5 Components):")
        print("=" * 80)
        
        # Show key columns for first 5 components
        key_columns = [
            'product_id', 'description_pcm', 'description_bom', 
            'color_detected', 'level', 'page', 
            'coordinate_x', 'coordinate_y', 'prediction_score'
        ]
        
        sample_df = df[key_columns].head(5)
        
        for idx, row in sample_df.iterrows():
            print(f"\n🔧 Component {idx + 1}:")
            print(f"   Product ID: {row['product_id']}")
            print(f"   PCM Description: {row['description_pcm']}")
            print(f"   BOM Description: {row['description_bom']}")
            print(f"   Color/Type: {row['color_detected']}")
            print(f"   Level: {row['level']}")
            print(f"   Page: {row['page']}")
            print(f"   Coordinates: ({row['coordinate_x']}, {row['coordinate_y']})")
            print(f"   Prediction Score: {row['prediction_score']:.3f}")
        
        print(f"\n📊 SIMILAR PARTS EXAMPLE:")
        print("=" * 40)
        
        # Find components with similar parts
        has_similar = df[df['similar_part_1'] != ""].head(3)
        
        for idx, row in has_similar.iterrows():
            print(f"\n🔗 {row['product_id']} ({row['color_detected']}):")
            print(f"   Main: {row['description_pcm']}")
            if row['similar_part_1'] != "":
                print(f"   Similar 1: {row['similar_part_1']} - {row['similar_part_1_description']}")
            if row['similar_part_2'] != "":
                print(f"   Similar 2: {row['similar_part_2']} - {row['similar_part_2_description']}")
            if row['similar_part_3'] != "":
                print(f"   Similar 3: {row['similar_part_3']} - {row['similar_part_3_description']}")
        
        print(f"\n🛰️ SATELLITE ELEMENTS EXAMPLE:")
        print("=" * 40)
        
        # Find components with satellites
        has_satellites = df[df['elements_satellite'] != ""].head(3)
        
        for idx, row in has_satellites.iterrows():
            print(f"\n🛰️ {row['product_id']}:")
            print(f"   Main: {row['description_pcm']}")
            print(f"   Satellites: {row['elements_satellite']}")
        
        print(f"\n📈 COLOR DISTRIBUTION:")
        print("=" * 30)
        color_dist = df['color_detected'].value_counts()
        for color, count in color_dist.items():
            percentage = (count / len(df)) * 100
            mandatoriness = {
                'Blue': 'Mandatory',
                'Purple': 'Mandatory Selectable', 
                'Red': 'Optional'
            }.get(color, 'Unknown')
            print(f"   {color} ({mandatoriness}): {count} ({percentage:.1f}%)")
        
        print(f"\n✅ RECONSTRUCTION SUCCESS!")
        print(f"📊 Extended BOM with {len(df)} components created")
        print(f"📋 All requested columns implemented:")
        print(f"   ✅ Description matching (PCM vs BOM)")
        print(f"   ✅ Similar parts for optional components")
        print(f"   ✅ Satellite elements detection")
        print(f"   ✅ Page numbers and A4 coordinates")
        print(f"   ✅ Prediction scores")
        
    except Exception as e:
        print(f"❌ Error loading reconstructed PCM: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    view_reconstructed_pcm()
