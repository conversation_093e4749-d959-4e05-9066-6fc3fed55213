#!/usr/bin/env python3
"""
PCM Parameter Extractor
Extracts items, specifications, dimensions, compatibility, and figures from PCM documents
"""

import fitz  # PyMuPDF
import pandas as pd
import re
import json
from collections import defaultdict
import os

class PCMParameterExtractor:
    def __init__(self):
        """
        Initialize PCM parameter extractor
        """
        # Load predefined vocabulary
        self.vocabulary = pd.read_csv('output/pim_predefined_vocabulary.csv')
        
        # Define extraction patterns
        self.item_patterns = [
            r'\b[A-Z]\d{4,6}[A-Z]{0,2}\b',  # Product codes like M7010FC
            r'\bP/N\s*[:\-]?\s*([A-Z0-9\-]+)\b',  # Part numbers
            r'\bModel\s*[:\-]?\s*([A-Z0-9\-]+)\b',  # Model numbers
        ]
        
        self.spec_patterns = [
            r'(\d+\.?\d*)\s*(mm|cm|m|inch|in)',  # Dimensions
            r'(\d+\.?\d*)\s*(kg|lbs|g)',  # Weight
            r'(\d+\.?\d*)\s*(V|volt|A|amp|W|watt)',  # Electrical
            r'(\d+\.?\d*)\s*(T|tesla|MHz|Hz)',  # Magnetic/Frequency
            r'(\d+\.?\d*)\s*(°C|°F|celsius|fahrenheit)',  # Temperature
        ]
        
        self.compatibility_keywords = [
            'compatible', 'compatibility', 'works with', 'supports',
            'requires', 'optional', 'upgrade', 'accessory'
        ]
        
        self.figure_patterns = [
            r'Figure\s+(\d+)',
            r'Fig\.\s*(\d+)',
            r'Image\s+(\d+)',
            r'Diagram\s+(\d+)'
        ]
        
    def extract_document_structure(self, pdf_path):
        """
        Extract basic document structure and metadata
        """
        doc = fitz.open(pdf_path)
        
        structure = {
            'total_pages': len(doc),
            'has_text': 0,
            'has_images': 0,
            'page_info': []
        }
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            images = page.get_images()
            
            page_data = {
                'page_number': page_num + 1,
                'has_text': len(text.strip()) > 0,
                'text_length': len(text),
                'image_count': len(images),
                'text_preview': text[:200] if text else ""
            }
            
            structure['page_info'].append(page_data)
            
            if page_data['has_text']:
                structure['has_text'] += 1
            if page_data['image_count'] > 0:
                structure['has_images'] += 1
        
        doc.close()
        return structure
    
    def extract_items(self, text):
        """
        Extract item codes and product identifiers
        """
        items = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # Apply item patterns
            for pattern in self.item_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    item = {
                        'item_code': match.group(0),
                        'line_number': line_num + 1,
                        'context': line,
                        'pattern_type': 'product_code' if pattern == self.item_patterns[0] else 'part_number'
                    }
                    items.append(item)
        
        return items
    
    def extract_specifications(self, text):
        """
        Extract technical specifications and dimensions
        """
        specifications = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Apply specification patterns
            for pattern in self.spec_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    spec = {
                        'value': match.group(1),
                        'unit': match.group(2),
                        'full_match': match.group(0),
                        'line_number': line_num + 1,
                        'context': line,
                        'spec_type': self.classify_specification(match.group(2))
                    }
                    specifications.append(spec)
        
        return specifications
    
    def classify_specification(self, unit):
        """
        Classify specification type based on unit
        """
        unit_lower = unit.lower()
        
        if unit_lower in ['mm', 'cm', 'm', 'inch', 'in']:
            return 'dimension'
        elif unit_lower in ['kg', 'lbs', 'g']:
            return 'weight'
        elif unit_lower in ['v', 'volt', 'a', 'amp', 'w', 'watt']:
            return 'electrical'
        elif unit_lower in ['t', 'tesla', 'mhz', 'hz']:
            return 'magnetic_frequency'
        elif unit_lower in ['°c', '°f', 'celsius', 'fahrenheit']:
            return 'temperature'
        else:
            return 'other'
    
    def extract_compatibility_info(self, text):
        """
        Extract compatibility and relationship information
        """
        compatibility = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            line_lower = line.lower()
            
            for keyword in self.compatibility_keywords:
                if keyword in line_lower:
                    compat = {
                        'keyword': keyword,
                        'line_number': line_num + 1,
                        'context': line.strip(),
                        'compatibility_type': self.classify_compatibility(keyword)
                    }
                    compatibility.append(compat)
        
        return compatibility
    
    def classify_compatibility(self, keyword):
        """
        Classify compatibility type
        """
        if keyword in ['compatible', 'compatibility', 'works with']:
            return 'compatible_with'
        elif keyword in ['supports']:
            return 'supports'
        elif keyword in ['requires']:
            return 'requires'
        elif keyword in ['optional', 'accessory']:
            return 'optional'
        elif keyword in ['upgrade']:
            return 'upgrade'
        else:
            return 'other'
    
    def extract_figures(self, text):
        """
        Extract figure references and names
        """
        figures = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines):
            for pattern in self.figure_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    figure = {
                        'figure_number': match.group(1),
                        'figure_reference': match.group(0),
                        'line_number': line_num + 1,
                        'context': line.strip(),
                        'figure_name': self.extract_figure_name(line)
                    }
                    figures.append(figure)
        
        return figures
    
    def extract_figure_name(self, line):
        """
        Extract figure name/title from context
        """
        # Look for text after figure reference
        for pattern in self.figure_patterns:
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                remaining_text = line[match.end():].strip()
                # Remove common separators and get first meaningful part
                remaining_text = re.sub(r'^[:\-\.\s]+', '', remaining_text)
                # Take first sentence or up to 100 characters
                if '.' in remaining_text:
                    return remaining_text.split('.')[0]
                else:
                    return remaining_text[:100] if len(remaining_text) > 100 else remaining_text
        
        return ""
    
    def process_document(self, pdf_path):
        """
        Process complete document and extract all parameters
        """
        print(f"Processing document: {pdf_path}")
        
        # Extract document structure
        structure = self.extract_document_structure(pdf_path)
        print(f"Document has {structure['total_pages']} pages")
        print(f"Pages with text: {structure['has_text']}")
        print(f"Pages with images: {structure['has_images']}")
        
        # Extract text from all pages
        doc = fitz.open(pdf_path)
        all_text = ""
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            all_text += text + "\n"
        
        doc.close()
        
        # Extract parameters
        items = self.extract_items(all_text)
        specifications = self.extract_specifications(all_text)
        compatibility = self.extract_compatibility_info(all_text)
        figures = self.extract_figures(all_text)
        
        # Compile results
        results = {
            'document_info': {
                'file_path': pdf_path,
                'total_pages': structure['total_pages'],
                'pages_with_text': structure['has_text'],
                'pages_with_images': structure['has_images'],
                'total_text_length': len(all_text)
            },
            'extracted_items': items,
            'specifications': specifications,
            'compatibility_info': compatibility,
            'figures': figures,
            'summary': {
                'total_items': len(items),
                'total_specifications': len(specifications),
                'total_compatibility_refs': len(compatibility),
                'total_figures': len(figures)
            }
        }
        
        return results
    
    def create_summary_table(self, results):
        """
        Create summary table of extracted parameters
        """
        summary_data = []
        
        # Add items
        for item in results['extracted_items']:
            summary_data.append({
                'Parameter_Type': 'Item',
                'Value': item['item_code'],
                'Context': item['context'],
                'Page_Reference': f"Line {item['line_number']}",
                'Classification': item['pattern_type']
            })
        
        # Add specifications
        for spec in results['specifications']:
            summary_data.append({
                'Parameter_Type': 'Specification',
                'Value': f"{spec['value']} {spec['unit']}",
                'Context': spec['context'],
                'Page_Reference': f"Line {spec['line_number']}",
                'Classification': spec['spec_type']
            })
        
        # Add compatibility
        for compat in results['compatibility_info']:
            summary_data.append({
                'Parameter_Type': 'Compatibility',
                'Value': compat['keyword'],
                'Context': compat['context'],
                'Page_Reference': f"Line {compat['line_number']}",
                'Classification': compat['compatibility_type']
            })
        
        # Add figures
        for figure in results['figures']:
            summary_data.append({
                'Parameter_Type': 'Figure',
                'Value': figure['figure_reference'],
                'Context': figure['figure_name'] if figure['figure_name'] else figure['context'],
                'Page_Reference': f"Line {figure['line_number']}",
                'Classification': 'figure_reference'
            })
        
        return pd.DataFrame(summary_data)
    
    def export_results(self, results, output_folder='output'):
        """
        Export results to files
        """
        os.makedirs(output_folder, exist_ok=True)
        
        # Export complete results as JSON
        with open(f'{output_folder}/pcm_extraction_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Export summary table
        summary_df = self.create_summary_table(results)
        summary_df.to_csv(f'{output_folder}/pcm_parameters_summary.csv', index=False)
        
        # Export individual parameter types
        if results['extracted_items']:
            items_df = pd.DataFrame(results['extracted_items'])
            items_df.to_csv(f'{output_folder}/pcm_items.csv', index=False)
        
        if results['specifications']:
            specs_df = pd.DataFrame(results['specifications'])
            specs_df.to_csv(f'{output_folder}/pcm_specifications.csv', index=False)
        
        if results['compatibility_info']:
            compat_df = pd.DataFrame(results['compatibility_info'])
            compat_df.to_csv(f'{output_folder}/pcm_compatibility.csv', index=False)
        
        if results['figures']:
            figures_df = pd.DataFrame(results['figures'])
            figures_df.to_csv(f'{output_folder}/pcm_figures.csv', index=False)
        
        print(f"Results exported to {output_folder}/")
        print(f"Summary table: pcm_parameters_summary.csv")
        print(f"Complete results: pcm_extraction_results.json")

def main():
    """
    Main execution function
    """
    print("PCM Parameter Extraction")
    print("Extracting: Items, Specifications, Dimensions, Compatibility, Figures")
    
    # Initialize extractor
    extractor = PCMParameterExtractor()
    
    # Process document
    pdf_path = r"input\PIM\3.0T Signa PET MR.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"File not found: {pdf_path}")
        return
    
    # Extract parameters
    results = extractor.process_document(pdf_path)
    
    # Display summary
    print("\nExtraction Summary:")
    print(f"Items found: {results['summary']['total_items']}")
    print(f"Specifications found: {results['summary']['total_specifications']}")
    print(f"Compatibility references: {results['summary']['total_compatibility_refs']}")
    print(f"Figures found: {results['summary']['total_figures']}")
    
    # Export results
    extractor.export_results(results)
    
    print("\nExtraction complete. Files saved in output/ folder.")

if __name__ == "__main__":
    main()
