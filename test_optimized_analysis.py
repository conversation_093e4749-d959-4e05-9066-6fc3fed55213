#!/usr/bin/env python3
"""
Test script for the optimized BOM similarity analysis
Tests the deduplication optimization to prevent memory overflow
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

def test_single_file_optimization():
    """Test the optimization on a single file to see memory reduction"""
    print("🧪 Testing BOM Data Optimization")
    print("=" * 40)
    
    # Import the optimized load function
    try:
        from bom_similarity_analysis import load_bom_data
        print("✅ Successfully imported optimized load_bom_data function")
    except Exception as e:
        print(f"❌ Error importing function: {e}")
        return
    
    # Test with one of the Excel files
    input_folder = 'input'
    if not os.path.exists(input_folder):
        print("❌ Input folder not found!")
        return
    
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    if not excel_files:
        print("❌ No Excel files found!")
        return
    
    # Test with first file
    test_file = excel_files[0]
    print(f"\n📄 Testing with file: {test_file}")
    
    file_path = os.path.join(input_folder, test_file)
    
    try:
        # Load and process the file
        df = load_bom_data(file_path)
        
        if df is not None:
            print(f"\n✅ File processed successfully!")
            print(f"📊 Final dataset shape: {df.shape}")
            print(f"📋 Columns: {list(df.columns)}")
            
            if 'Occurrence_Count' in df.columns:
                total_occurrences = df['Occurrence_Count'].sum()
                unique_items = len(df)
                print(f"💾 Total original items: {total_occurrences}")
                print(f"💾 Unique items after deduplication: {unique_items}")
                print(f"💾 Memory reduction: {((total_occurrences - unique_items) / total_occurrences * 100):.1f}%")
                
                # Show some examples
                print(f"\n📋 Sample of deduplicated data:")
                sample_df = df.head(3)[['Item ID', 'Item Description', 'Occurrence_Count']]
                print(sample_df.to_string(index=False))
            else:
                print("⚠️  No occurrence count found - deduplication may not have worked")
        else:
            print("❌ Failed to process file")
            
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        import traceback
        traceback.print_exc()

def estimate_memory_usage():
    """Estimate memory usage for the full analysis"""
    print(f"\n🧮 Estimating Memory Usage")
    print("=" * 30)
    
    input_folder = 'input'
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    
    total_items_before = 0
    total_items_after = 0
    
    try:
        from bom_similarity_analysis import load_bom_data
        
        for i, file in enumerate(excel_files[:5], 1):  # Test first 5 files
            print(f"  [{i}/5] Analyzing: {file[:30]}...")
            file_path = os.path.join(input_folder, file)
            
            try:
                df = load_bom_data(file_path)
                if df is not None and 'Occurrence_Count' in df.columns:
                    items_before = df['Occurrence_Count'].sum()
                    items_after = len(df)
                    total_items_before += items_before
                    total_items_after += items_after
                    print(f"    📊 {items_before} → {items_after} items ({((items_before-items_after)/items_before*100):.1f}% reduction)")
            except Exception as e:
                print(f"    ❌ Error: {e}")
        
        if total_items_before > 0:
            print(f"\n📊 TOTAL ESTIMATION (first 5 files):")
            print(f"   Before optimization: {total_items_before:,} items")
            print(f"   After optimization:  {total_items_after:,} items")
            print(f"   Memory reduction:    {((total_items_before-total_items_after)/total_items_before*100):.1f}%")
            
            # Estimate for all files
            avg_reduction = (total_items_before - total_items_after) / total_items_before
            estimated_total_before = total_items_before * (len(excel_files) / 5)
            estimated_total_after = estimated_total_before * (1 - avg_reduction)
            
            print(f"\n🔮 PROJECTION FOR ALL {len(excel_files)} FILES:")
            print(f"   Estimated before: {estimated_total_before:,.0f} items")
            print(f"   Estimated after:  {estimated_total_after:,.0f} items")
            print(f"   Estimated memory reduction: {avg_reduction*100:.1f}%")
            
            # Memory usage estimation
            similarity_matrix_size = estimated_total_after ** 2 * 8  # 8 bytes per float64
            print(f"\n💾 MEMORY ESTIMATION:")
            print(f"   Similarity matrix size: {similarity_matrix_size / (1024**3):.2f} GB")
            
            if similarity_matrix_size > 8 * (1024**3):  # 8 GB
                print(f"   ⚠️  WARNING: Still very large! Consider further optimization.")
            else:
                print(f"   ✅ Should be manageable!")
                
    except Exception as e:
        print(f"❌ Error in estimation: {e}")

def main():
    print("🚀 BOM Analysis Optimization Test")
    print("=" * 50)
    print(f"⏰ Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test 1: Single file optimization
    test_single_file_optimization()
    
    # Test 2: Memory usage estimation
    estimate_memory_usage()
    
    print(f"\n🎯 Test Complete!")
    print("If the memory reduction is significant and the estimated")
    print("similarity matrix size is under 8GB, the analysis should work!")

if __name__ == "__main__":
    main()
