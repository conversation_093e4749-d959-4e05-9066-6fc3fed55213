#!/usr/bin/env python3
"""
BOM Graph Analysis and Visualization
Creates hierarchical graphs for each BOM file based on F-E-Line structure.
Treats F-E-Line as tree roots with Level 0 as grand father.
"""

import os
import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from collections import defaultdict, deque
import json
import re

def extract_level_number(level_str):
    """Extract the primary level number from level string"""
    try:
        match = re.search(r'^(\d+)', str(level_str))
        if match:
            return int(match.group(1))
        return 0
    except:
        return 0

def load_bom_file_for_graph(file_path):
    """Load a single BOM file and prepare it for graph analysis"""
    try:
        # Read the second sheet
        xls = pd.ExcelFile(file_path)
        if len(xls.sheet_names) < 2:
            return None
            
        df = pd.read_excel(file_path, sheet_name=1)
        
        if len(df.columns) < 4:
            return None
        
        # Use corrected column mapping: Level, Sequence, F-E-Line (Item_ID), Description
        df = df.iloc[:, :4]
        df.columns = ['Level', 'Sequence', 'F_E_Line', 'Item_Description']
        
        # Clean data
        df = df.dropna(subset=['Level', 'Sequence', 'F_E_Line', 'Item_Description'])
        df = df[df['Level'].astype(str).str.strip() != '']
        df = df[df['F_E_Line'].astype(str).str.strip() != '']
        df = df[df['Item_Description'].astype(str).str.strip() != '']
        
        # Remove numeric-only F-E-Line (these are sequence numbers)
        df = df[~df['F_E_Line'].astype(str).str.match(r'^\d+$')]
        
        # Extract level numbers
        df['Level_Number'] = df['Level'].apply(extract_level_number)
        
        # Add row index for tracking
        df['Row_Index'] = range(len(df))
        
        return df
        
    except Exception as e:
        print(f"Error loading file: {e}")
        return None

def create_bom_graph_structure(df, file_name):
    """Create graph structure for a BOM file"""
    print(f"🔍 Creating graph structure for: {file_name}")
    
    # Group by F-E-Line to create separate trees
    fe_line_groups = df.groupby('F_E_Line')
    
    file_graph = {
        'file_name': file_name,
        'fe_line_trees': {},
        'file_root': f"FILE_ROOT_{file_name}",
        'total_items': len(df),
        'unique_fe_lines': len(fe_line_groups)
    }
    
    for fe_line, group in fe_line_groups:
        print(f"  📊 Processing F-E-Line: {fe_line} ({len(group)} items)")
        
        # Sort by row index to maintain order
        group = group.sort_values('Row_Index')
        
        # Create tree structure for this F-E-Line
        tree = create_fe_line_tree(group, fe_line)
        file_graph['fe_line_trees'][fe_line] = tree
    
    return file_graph

def create_fe_line_tree(group, fe_line):
    """Create tree structure for a single F-E-Line"""
    tree = {
        'fe_line': fe_line,
        'root': f"FE_ROOT_{fe_line}",  # Level 0 (grand father)
        'nodes': {},
        'edges': [],
        'levels': defaultdict(list),
        'max_level': 0
    }
    
    # Add root node (Level 0 - grand father)
    root_id = tree['root']
    tree['nodes'][root_id] = {
        'id': root_id,
        'level': 0,
        'fe_line': fe_line,
        'description': f"F-E-Line Root: {fe_line}",
        'item_type': 'fe_line_root',
        'row_index': -1
    }
    tree['levels'][0].append(root_id)
    
    # Process items in order
    level_stacks = {}  # Track parent for each level
    level_stacks[0] = root_id
    
    for idx, (_, row) in enumerate(group.iterrows()):
        level = row['Level_Number']
        node_id = f"{fe_line}_L{level}_{idx}"
        
        # Create node
        tree['nodes'][node_id] = {
            'id': node_id,
            'level': level,
            'fe_line': fe_line,
            'description': str(row['Item_Description'])[:50],
            'full_description': str(row['Item_Description']),
            'sequence': str(row['Sequence']),
            'item_type': 'component',
            'row_index': row['Row_Index'],
            'original_level': str(row['Level'])
        }
        
        tree['levels'][level].append(node_id)
        tree['max_level'] = max(tree['max_level'], level)
        
        # Determine parent based on level hierarchy
        if level == 1:
            # Level 1 always connects to root (Level 0)
            parent_id = root_id
        else:
            # Find appropriate parent from previous levels
            parent_level = level - 1
            while parent_level >= 0:
                if parent_level in level_stacks:
                    parent_id = level_stacks[parent_level]
                    break
                parent_level -= 1
            else:
                parent_id = root_id  # Fallback to root
        
        # Create edge
        tree['edges'].append({
            'parent': parent_id,
            'child': node_id,
            'parent_level': tree['nodes'][parent_id]['level'],
            'child_level': level
        })
        
        # Update level stack for this level
        level_stacks[level] = node_id
    
    return tree

def create_networkx_graph(file_graph):
    """Convert our graph structure to NetworkX graph for visualization"""
    G = nx.DiGraph()
    
    # Add file root
    file_root = file_graph['file_root']
    G.add_node(file_root, 
               level=-1, 
               node_type='file_root',
               label=f"File: {file_graph['file_name'][:20]}...",
               description=f"BOM File Root")
    
    # Add all F-E-Line trees
    for fe_line, tree in file_graph['fe_line_trees'].items():
        # Add F-E-Line root
        fe_root = tree['root']
        G.add_node(fe_root,
                   level=0,
                   node_type='fe_line_root',
                   label=f"F-E: {fe_line}",
                   description=f"F-E-Line: {fe_line}")
        
        # Connect F-E-Line root to file root
        G.add_edge(file_root, fe_root)
        
        # Add all nodes in this tree
        for node_id, node_data in tree['nodes'].items():
            if node_id != fe_root:  # Skip root, already added
                G.add_node(node_id,
                           level=node_data['level'],
                           node_type=node_data['item_type'],
                           label=node_data['description'],
                           description=node_data.get('full_description', ''),
                           fe_line=node_data['fe_line'])
        
        # Add all edges in this tree
        for edge in tree['edges']:
            G.add_edge(edge['parent'], edge['child'])
    
    return G

def visualize_bom_graph(G, file_name, output_folder):
    """Create visualization of the BOM graph"""
    print(f"📊 Creating visualization for: {file_name}")
    
    plt.figure(figsize=(20, 16))
    
    # Create hierarchical layout
    pos = create_hierarchical_layout(G)
    
    # Define colors for different node types
    node_colors = []
    node_sizes = []
    
    for node in G.nodes():
        node_type = G.nodes[node].get('node_type', 'component')
        if node_type == 'file_root':
            node_colors.append('red')
            node_sizes.append(1000)
        elif node_type == 'fe_line_root':
            node_colors.append('orange')
            node_sizes.append(600)
        else:
            level = G.nodes[node].get('level', 1)
            if level == 1:
                node_colors.append('lightblue')
                node_sizes.append(300)
            elif level == 2:
                node_colors.append('lightgreen')
                node_sizes.append(200)
            elif level == 3:
                node_colors.append('lightyellow')
                node_sizes.append(150)
            else:
                node_colors.append('lightgray')
                node_sizes.append(100)
    
    # Draw the graph
    nx.draw(G, pos, 
            node_color=node_colors,
            node_size=node_sizes,
            with_labels=False,
            arrows=True,
            arrowsize=20,
            edge_color='gray',
            alpha=0.7)
    
    # Add labels for important nodes only
    important_nodes = {node: G.nodes[node]['label'] 
                      for node in G.nodes() 
                      if G.nodes[node].get('node_type') in ['file_root', 'fe_line_root']}
    
    nx.draw_networkx_labels(G, pos, important_nodes, font_size=8, font_weight='bold')
    
    # Create legend
    legend_elements = [
        mpatches.Patch(color='red', label='File Root'),
        mpatches.Patch(color='orange', label='F-E-Line Root (Level 0)'),
        mpatches.Patch(color='lightblue', label='Level 1 Components'),
        mpatches.Patch(color='lightgreen', label='Level 2 Components'),
        mpatches.Patch(color='lightyellow', label='Level 3 Components'),
        mpatches.Patch(color='lightgray', label='Other Levels')
    ]
    
    plt.legend(handles=legend_elements, loc='upper right')
    plt.title(f'BOM Hierarchical Graph: {file_name}', fontsize=16, fontweight='bold')
    
    # Save the visualization
    safe_filename = re.sub(r'[^\w\-_\.]', '_', file_name)
    viz_file = os.path.join(output_folder, f'bom_graph_{safe_filename}.png')
    plt.savefig(viz_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Visualization saved: {viz_file}")
    return viz_file

def create_hierarchical_layout(G):
    """Create hierarchical layout for the graph"""
    pos = {}
    
    # Group nodes by level
    levels = defaultdict(list)
    for node in G.nodes():
        level = G.nodes[node].get('level', 0)
        levels[level].append(node)
    
    # Position nodes level by level
    y_spacing = 2.0
    x_spacing = 1.5
    
    for level, nodes in levels.items():
        y = -level * y_spacing  # Higher levels go down
        
        # Distribute nodes horizontally
        if len(nodes) == 1:
            x_positions = [0]
        else:
            x_positions = [(i - (len(nodes) - 1) / 2) * x_spacing for i in range(len(nodes))]
        
        for i, node in enumerate(nodes):
            pos[node] = (x_positions[i], y)
    
    return pos

def save_graph_data(file_graph, output_folder):
    """Save graph data as JSON for further analysis"""
    safe_filename = re.sub(r'[^\w\-_\.]', '_', file_graph['file_name'])
    json_file = os.path.join(output_folder, f'bom_graph_data_{safe_filename}.json')
    
    # Convert to JSON-serializable format
    json_data = {
        'file_name': file_graph['file_name'],
        'file_root': file_graph['file_root'],
        'total_items': file_graph['total_items'],
        'unique_fe_lines': file_graph['unique_fe_lines'],
        'fe_line_trees': {}
    }
    
    for fe_line, tree in file_graph['fe_line_trees'].items():
        json_data['fe_line_trees'][fe_line] = {
            'fe_line': tree['fe_line'],
            'root': tree['root'],
            'max_level': tree['max_level'],
            'nodes': tree['nodes'],
            'edges': tree['edges'],
            'level_counts': {str(k): len(v) for k, v in tree['levels'].items()}
        }
    
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Graph data saved: {json_file}")
    return json_file

def create_summary_report(all_file_graphs, output_folder):
    """Create summary report of all BOM graphs"""
    print("📝 Creating summary report...")
    
    summary_file = os.path.join(output_folder, 'bom_graphs_summary.txt')
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("BOM HIERARCHICAL GRAPHS SUMMARY\n")
        f.write("=" * 60 + "\n")
        f.write(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Files Analyzed: {len(all_file_graphs)}\n\n")
        
        total_items = sum(graph['total_items'] for graph in all_file_graphs)
        total_fe_lines = sum(graph['unique_fe_lines'] for graph in all_file_graphs)
        
        f.write(f"OVERALL STATISTICS\n")
        f.write("=" * 20 + "\n")
        f.write(f"Total Items Across All Files: {total_items:,}\n")
        f.write(f"Total Unique F-E-Lines: {total_fe_lines:,}\n")
        f.write(f"Average Items per File: {total_items / len(all_file_graphs):.1f}\n")
        f.write(f"Average F-E-Lines per File: {total_fe_lines / len(all_file_graphs):.1f}\n\n")
        
        f.write("FILE-BY-FILE BREAKDOWN\n")
        f.write("=" * 25 + "\n")
        
        for i, graph in enumerate(all_file_graphs, 1):
            f.write(f"\n{i:2d}. {graph['file_name']}\n")
            f.write(f"    Total Items: {graph['total_items']:,}\n")
            f.write(f"    Unique F-E-Lines: {graph['unique_fe_lines']}\n")
            
            # F-E-Line details
            f.write(f"    F-E-Line Details:\n")
            for fe_line, tree in list(graph['fe_line_trees'].items())[:5]:  # Show first 5
                f.write(f"      • {fe_line}: {len(tree['nodes'])-1} items, max level {tree['max_level']}\n")
            
            if len(graph['fe_line_trees']) > 5:
                f.write(f"      ... and {len(graph['fe_line_trees'])-5} more F-E-Lines\n")
    
    print(f"✅ Summary report saved: {summary_file}")

def main():
    print("🚀 Starting BOM Graph Analysis and Visualization")
    print("=" * 70)
    print("🎯 Creating hierarchical graphs for each BOM file")
    print("📊 F-E-Line as tree roots with Level 0 as grand father")
    
    input_folder = 'input'
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Get all Excel files
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    print(f"📁 Found {len(excel_files)} Excel files to analyze")
    
    all_file_graphs = []
    
    # Process first 5 files for demonstration
    for i, file in enumerate(excel_files[:5], 1):
        print(f"\n[{i}/5] Processing: {file}")
        file_path = os.path.join(input_folder, file)
        
        # Load BOM data
        df = load_bom_file_for_graph(file_path)
        if df is None:
            print(f"❌ Failed to load {file}")
            continue
        
        print(f"✅ Loaded {len(df)} items")
        
        # Create graph structure
        file_graph = create_bom_graph_structure(df, file)
        all_file_graphs.append(file_graph)
        
        # Create NetworkX graph
        G = create_networkx_graph(file_graph)
        print(f"📊 Graph created: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
        
        # Create visualization
        visualize_bom_graph(G, file, output_folder)
        
        # Save graph data
        save_graph_data(file_graph, output_folder)
    
    # Create summary report
    create_summary_report(all_file_graphs, output_folder)
    
    print(f"\n🎉 BOM Graph Analysis Complete!")
    print("=" * 70)
    print(f"📊 Processed {len(all_file_graphs)} files")
    print(f"📁 Files created in: {os.path.abspath(output_folder)}")
    print(f"   • bom_graph_*.png - Hierarchical visualizations")
    print(f"   • bom_graph_data_*.json - Graph structure data")
    print(f"   • bom_graphs_summary.txt - Summary report")

if __name__ == "__main__":
    main()
