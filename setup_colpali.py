#!/usr/bin/env python3
"""
Setup ColPali Environment
Install and configure ColPali for PowerPoint extraction
"""

import subprocess
import sys
import os

def install_colpali_dependencies():
    """Install ColPali and related dependencies"""
    print("📦 Installing ColPali dependencies...")
    
    # List of required packages
    packages = [
        "torch",
        "torchvision", 
        "transformers>=4.30.0",
        "pillow",
        "opencv-python",
        "numpy",
        "pandas",
        "matplotlib",
        "python-pptx",
        "pdf2image",  # For PowerPoint to image conversion
        "pytesseract",  # OCR capabilities
        "colpali-engine"  # Main ColPali package
    ]
    
    print("🔧 Installing packages:")
    for package in packages:
        print(f"   📥 Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"   ✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"   ⚠️ Warning: Failed to install {package}: {e}")
            
            # Try alternative installation for some packages
            if package == "colpali-engine":
                print("   🔄 Trying alternative ColPali installation...")
                try:
                    # Install from GitHub if package not available on PyPI
                    subprocess.check_call([
                        sys.executable, "-m", "pip", "install", 
                        "git+https://github.com/illuin-tech/colpali.git"
                    ])
                    print("   ✅ ColPali installed from GitHub")
                except:
                    print("   ⚠️ ColPali installation failed - will use fallback methods")

def setup_colpali_config():
    """Setup ColPali configuration"""
    print("⚙️ Setting up ColPali configuration...")
    
    config = {
        "model_name": "vidore/colpali",
        "device": "cuda" if check_cuda_availability() else "cpu",
        "batch_size": 4,
        "max_length": 512,
        "confidence_threshold": 0.7
    }
    
    # Save config
    import json
    config_file = "colpali_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Configuration saved to {config_file}")
    return config

def check_cuda_availability():
    """Check if CUDA is available"""
    try:
        import torch
        return torch.cuda.is_available()
    except:
        return False

def test_colpali_installation():
    """Test ColPali installation"""
    print("🧪 Testing ColPali installation...")
    
    try:
        # Test basic imports
        import torch
        print("   ✅ PyTorch imported successfully")
        
        from transformers import AutoProcessor, AutoModel
        print("   ✅ Transformers imported successfully")
        
        from PIL import Image
        print("   ✅ PIL imported successfully")
        
        # Test ColPali specific imports
        try:
            # This might fail if ColPali isn't properly installed
            # We'll handle it gracefully
            print("   🔍 Testing ColPali model access...")
            
            # Try to load the model (this might take time on first run)
            model_name = "vidore/colpali"
            processor = AutoProcessor.from_pretrained(model_name)
            print("   ✅ ColPali processor loaded successfully")
            
            model = AutoModel.from_pretrained(model_name)
            print("   ✅ ColPali model loaded successfully")
            
            return True
            
        except Exception as e:
            print(f"   ⚠️ ColPali model loading failed: {e}")
            print("   📋 Will use fallback extraction methods")
            return False
            
    except Exception as e:
        print(f"   ❌ Basic dependencies test failed: {e}")
        return False

def create_colpali_demo():
    """Create a simple ColPali demo"""
    print("🎭 Creating ColPali demo...")
    
    demo_code = '''
import torch
from transformers import AutoProcessor, AutoModel
from PIL import Image
import numpy as np

def demo_colpali():
    """Demo ColPali functionality"""
    print("🤖 ColPali Demo")
    
    try:
        # Load model
        model_name = "vidore/colpali"
        processor = AutoProcessor.from_pretrained(model_name)
        model = AutoModel.from_pretrained(model_name)
        
        # Create sample image
        sample_image = Image.new('RGB', (800, 600), color='white')
        
        # Sample queries
        queries = [
            "blue colored components",
            "red optional parts", 
            "purple selectable components"
        ]
        
        # Process
        inputs = processor(
            text=queries,
            images=sample_image,
            return_tensors="pt",
            padding=True
        )
        
        # Get outputs
        with torch.no_grad():
            outputs = model(**inputs)
        
        print("✅ ColPali demo completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

if __name__ == "__main__":
    demo_colpali()
'''
    
    with open("colpali_demo.py", "w", encoding='utf-8') as f:
        f.write(demo_code)
    
    print("✅ Demo created: colpali_demo.py")

def main():
    """Main setup function"""
    print("🚀 COLPALI SETUP FOR POWERPOINT EXTRACTION")
    print("=" * 60)
    
    # Install dependencies
    install_colpali_dependencies()
    
    # Setup configuration
    config = setup_colpali_config()
    
    # Test installation
    success = test_colpali_installation()
    
    # Create demo
    create_colpali_demo()
    
    print(f"\n🎉 ColPali Setup Complete!")
    print("=" * 60)
    
    if success:
        print("✅ ColPali is ready for PowerPoint extraction!")
        print("🚀 You can now run: python colpali_powerpoint_extractor.py")
    else:
        print("⚠️ ColPali setup had issues - fallback methods will be used")
        print("📋 Manual extraction template is still available")
    
    print(f"\n📋 Next Steps:")
    print("1. Test ColPali: python colpali_demo.py")
    print("2. Run extraction: python colpali_powerpoint_extractor.py")
    print("3. If issues, use manual template: powerpoint_extraction_template.xlsx")
    
    print(f"\n🔧 Configuration:")
    print(f"   Device: {config['device']}")
    print(f"   Model: {config['model_name']}")
    print(f"   Batch size: {config['batch_size']}")

if __name__ == "__main__":
    main()
