#!/usr/bin/env python3
"""
PCM Data Reconstruction Notebook
Focus on descriptions matching engineering BOM with optional parts handling
"""

import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import re
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class PCMReconstructionEngine:
    def __init__(self):
        """Initialize PCM reconstruction engine"""
        self.colpali_data = None
        self.bom_integration_data = None
        self.bom_data = None
        self.sbert_model = None
        self.reconstructed_pcm = None
        
        print("🔧 PCM RECONSTRUCTION ENGINE INITIALIZED")
        print("=" * 50)
    
    def load_data(self):
        """Load all available data sources"""
        print("📊 LOADING DATA SOURCES...")
        
        # 1. Load ColPali extracted PCM data
        try:
            self.colpali_data = pd.read_csv('output/comprehensive_colpali_extraction.csv')
            print(f"✅ ColPali PCM Data: {self.colpali_data.shape}")
        except Exception as e:
            print(f"❌ ColPali data error: {e}")
        
        # 2. Load BOM integration data
        try:
            self.bom_integration_data = pd.read_csv('output/colpali_bom_integration.csv')
            print(f"✅ BOM Integration Data: {self.bom_integration_data.shape}")
        except Exception as e:
            print(f"❌ BOM integration error: {e}")
        
        # 3. Load aggregated BOM data
        try:
            self.bom_data = pd.read_excel('output/voyager_5408509_bom_pcm_aggregated.xlsx')
            print(f"✅ Aggregated BOM Data: {self.bom_data.shape}")
        except Exception as e:
            print(f"❌ Aggregated BOM error: {e}")
        
        return self.colpali_data is not None and self.bom_integration_data is not None
    
    def load_sbert_model(self):
        """Load SBERT model for similarity analysis"""
        if self.sbert_model is None:
            print("🧠 Loading SBERT model...")
            self.sbert_model = SentenceTransformer('all-MiniLM-L6-v2')
            print("✅ SBERT model loaded")
    
    def analyze_description_matches(self):
        """Analyze description matches between PCM and BOM"""
        print("\n🔍 ANALYZING DESCRIPTION MATCHES")
        print("=" * 45)
        
        if self.bom_integration_data is None:
            print("❌ No BOM integration data available")
            return None
        
        # Focus on components with good matches
        good_matches = self.bom_integration_data[
            self.bom_integration_data['match_quality'].isin(['High', 'Medium'])
        ].copy()
        
        print(f"📊 Good matches found: {len(good_matches)}")
        
        # Analyze by color (mandatoriness)
        color_analysis = good_matches.groupby('colpali_color').agg({
            'colpali_product_id': 'count',
            'match_score': 'mean',
            'bom_level': 'mean'
        }).round(2)
        
        print(f"\n🎨 Color Analysis:")
        print(color_analysis.to_string())
        
        return good_matches
    
    def find_similar_optional_parts(self, reference_component, similarity_threshold=0.7):
        """Find similar parts for optional components (purple/red)"""
        
        if self.colpali_data is None:
            return []
        
        self.load_sbert_model()
        
        # Get components of same color and level/position
        same_color_components = self.colpali_data[
            (self.colpali_data['color_detected'] == reference_component['colpali_color']) &
            (self.colpali_data['page_number'] == reference_component['colpali_slide'])
        ].copy()
        
        if len(same_color_components) <= 1:
            return []
        
        # Calculate semantic similarity
        reference_text = reference_component['colpali_component_text']
        component_texts = same_color_components['component_text'].tolist()
        
        embeddings = self.sbert_model.encode([reference_text] + component_texts)
        similarities = cosine_similarity([embeddings[0]], embeddings[1:])[0]
        
        # Find similar components
        similar_parts = []
        for i, similarity in enumerate(similarities):
            if similarity >= similarity_threshold:
                comp = same_color_components.iloc[i]
                similar_parts.append({
                    'product_id': comp['product_id'],
                    'component_text': comp['component_text'],
                    'similarity_score': similarity,
                    'page_number': comp['page_number'],
                    'x_position': comp['center_x'],
                    'y_position': comp['center_y']
                })
        
        return similar_parts
    
    def find_satellite_elements(self, reference_component, radius=300):
        """Find satellite elements around a component"""
        
        if self.colpali_data is None:
            return []
        
        ref_x = reference_component.get('colpali_slide', 0)  # Use slide as page
        ref_page = reference_component.get('colpali_slide', 2)
        
        # Get components from same page
        page_components = self.colpali_data[
            self.colpali_data['page_number'] == ref_page
        ].copy()
        
        if len(page_components) == 0:
            return []
        
        # Find reference component coordinates
        ref_comp = page_components[
            page_components['product_id'] == reference_component['colpali_product_id']
        ]
        
        if len(ref_comp) == 0:
            return []
        
        ref_x = ref_comp.iloc[0]['center_x']
        ref_y = ref_comp.iloc[0]['center_y']
        
        # Calculate distances
        page_components['distance'] = np.sqrt(
            (page_components['center_x'] - ref_x)**2 + 
            (page_components['center_y'] - ref_y)**2
        )
        
        # Find nearby components (satellites)
        satellites = page_components[
            (page_components['distance'] <= radius) &
            (page_components['product_id'] != reference_component['colpali_product_id'])
        ].copy()
        
        satellite_descriptions = []
        for _, sat in satellites.iterrows():
            satellite_descriptions.append({
                'product_id': sat['product_id'],
                'component_text': sat['component_text'],
                'distance': sat['distance'],
                'color': sat['color_detected']
            })
        
        return satellite_descriptions
    
    def reconstruct_extended_bom(self):
        """Reconstruct extended BOM with PCM data according to user specifications"""
        print("\n🔧 RECONSTRUCTING EXTENDED BOM")
        print("=" * 40)

        if self.bom_integration_data is None:
            print("❌ No integration data available")
            return None

        extended_bom = []

        for idx, row in self.bom_integration_data.iterrows():

            # Base component information - focusing on descriptions as requested
            base_component = {
                # Core identification
                'product_id': row['colpali_product_id'],
                'component_text': row['colpali_component_text'],
                'description_pcm': row['colpali_component_text'],  # PCM description column as requested
                'description_bom': row['bom_item_description'],    # BOM description for comparison

                # Mandatoriness and positioning
                'color_detected': row['colpali_color'],
                'component_type': row['colpali_type'],
                'level': row['bom_level'],
                'f_e_line': row['bom_f_e_line'],

                # Page and coordinates in A4 as requested
                'page': row['colpali_slide'],
                'coordinate_x': None,
                'coordinate_y': None,

                # Prediction score as requested
                'prediction_score': row['colpali_confidence'],
                'match_score': row['match_score']
            }

            # Add A4 coordinates if available
            if self.colpali_data is not None:
                coord_data = self.colpali_data[
                    self.colpali_data['product_id'] == row['colpali_product_id']
                ]
                if len(coord_data) > 0:
                    base_component['coordinate_x'] = coord_data.iloc[0]['center_x']
                    base_component['coordinate_y'] = coord_data.iloc[0]['center_y']

            # For optional parts (Purple/Red), find similar parts at same level/position
            similar_parts = []
            if row['colpali_color'] in ['Purple', 'Red']:
                similar_parts = self.find_similar_optional_parts(row)

            # Add similar parts columns (1, 2, 3...) as requested
            for i in range(3):  # Up to 3 similar parts
                if i < len(similar_parts):
                    base_component[f'similar_part_{i+1}'] = similar_parts[i]['product_id']
                    base_component[f'similar_part_{i+1}_description'] = similar_parts[i]['component_text']
                    base_component[f'similar_part_{i+1}_similarity'] = similar_parts[i]['similarity_score']
                else:
                    base_component[f'similar_part_{i+1}'] = ""  # Blank if not found as requested
                    base_component[f'similar_part_{i+1}_description'] = ""
                    base_component[f'similar_part_{i+1}_similarity'] = ""

            # Find satellite elements (elements orbiting around this part) as requested
            satellites = self.find_satellite_elements(row)
            satellite_descriptions = '; '.join([
                f"{sat['component_text']}"
                for sat in satellites[:3]  # Limit to 3 satellites
            ]) if satellites else ""

            base_component['elements_satellite'] = satellite_descriptions  # French column name as requested

            extended_bom.append(base_component)

        self.reconstructed_pcm = pd.DataFrame(extended_bom)

        print(f"✅ Extended BOM created: {len(extended_bom)} components")
        print(f"📊 Columns: {len(self.reconstructed_pcm.columns)}")

        return self.reconstructed_pcm
    
    def export_reconstructed_pcm(self, output_path='output/reconstructed_pcm_extended_bom.xlsx'):
        """Export reconstructed PCM to Excel"""
        
        if self.reconstructed_pcm is None:
            print("❌ No reconstructed PCM available")
            return
        
        print(f"\n💾 EXPORTING RECONSTRUCTED PCM")
        print("=" * 40)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            
            # Main reconstructed PCM
            self.reconstructed_pcm.to_excel(writer, sheet_name='Extended_BOM_PCM', index=False)
            
            # Summary by color
            color_summary = self.reconstructed_pcm.groupby('color_detected').agg({
                'product_id': 'count',
                'prediction_score': 'mean',
                'match_score': 'mean',
                'level': 'mean'
            }).round(3)
            color_summary.to_excel(writer, sheet_name='Color_Summary')
            
            # Components with similar parts
            has_similar = self.reconstructed_pcm[
                self.reconstructed_pcm['similar_part_1'] != ""
            ].copy()
            if len(has_similar) > 0:
                has_similar.to_excel(writer, sheet_name='Components_With_Similar', index=False)
            
            # Components with satellites
            has_satellites = self.reconstructed_pcm[
                self.reconstructed_pcm['elements_satellite'] != ""
            ].copy()
            if len(has_satellites) > 0:
                has_satellites.to_excel(writer, sheet_name='Components_With_Satellites', index=False)
            
            # Page-wise breakdown
            page_summary = self.reconstructed_pcm.groupby('page').agg({
                'product_id': 'count',
                'color_detected': lambda x: x.value_counts().to_dict()
            })
            page_summary.to_excel(writer, sheet_name='Page_Summary')
        
        print(f"✅ Reconstructed PCM exported: {output_path}")
        
        # Generate summary report
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """Generate summary report"""
        
        if self.reconstructed_pcm is None:
            return
        
        report_lines = []
        report_lines.append("PCM RECONSTRUCTION SUMMARY REPORT")
        report_lines.append("=" * 50)
        report_lines.append("")
        
        # Overall statistics
        report_lines.append("📊 OVERALL STATISTICS")
        report_lines.append("-" * 25)
        report_lines.append(f"Total Components: {len(self.reconstructed_pcm)}")
        report_lines.append(f"Pages Covered: {sorted(self.reconstructed_pcm['page'].unique())}")
        report_lines.append("")
        
        # Color distribution
        report_lines.append("🎨 COLOR DISTRIBUTION (MANDATORINESS)")
        report_lines.append("-" * 40)
        color_dist = self.reconstructed_pcm['color_detected'].value_counts()
        for color, count in color_dist.items():
            percentage = (count / len(self.reconstructed_pcm)) * 100
            mandatoriness = {
                'Blue': 'Mandatory',
                'Purple': 'Mandatory Selectable', 
                'Red': 'Optional'
            }.get(color, 'Unknown')
            report_lines.append(f"{color} ({mandatoriness}): {count} ({percentage:.1f}%)")
        report_lines.append("")
        
        # Similar parts analysis
        has_similar = (self.reconstructed_pcm['similar_part_1'] != "").sum()
        report_lines.append("🔗 SIMILAR PARTS ANALYSIS")
        report_lines.append("-" * 30)
        report_lines.append(f"Components with similar parts: {has_similar}")
        report_lines.append(f"Percentage: {(has_similar/len(self.reconstructed_pcm)*100):.1f}%")
        report_lines.append("")

        # Satellite elements analysis
        has_satellites = (self.reconstructed_pcm['elements_satellite'] != "").sum()
        report_lines.append("🛰️ SATELLITE ELEMENTS ANALYSIS")
        report_lines.append("-" * 35)
        report_lines.append(f"Components with satellites: {has_satellites}")
        report_lines.append(f"Percentage: {(has_satellites/len(self.reconstructed_pcm)*100):.1f}%")
        report_lines.append("")
        
        # Match quality analysis
        avg_match_score = self.reconstructed_pcm['match_score'].mean()
        avg_prediction_score = self.reconstructed_pcm['prediction_score'].mean()
        report_lines.append("📈 QUALITY METRICS")
        report_lines.append("-" * 20)
        report_lines.append(f"Average Match Score: {avg_match_score:.3f}")
        report_lines.append(f"Average Prediction Score: {avg_prediction_score:.3f}")
        report_lines.append("")
        
        # Recommendations
        report_lines.append("🚀 RECOMMENDATIONS")
        report_lines.append("-" * 20)
        report_lines.append("1. Focus on Purple/Red components for product customization")
        report_lines.append("2. Use similar parts for alternative configurations")
        report_lines.append("3. Consider satellite elements for complete system design")
        report_lines.append("4. Validate high-similarity matches for accuracy")
        
        # Save report
        with open('output/pcm_reconstruction_report.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print("✅ Summary report saved: output/pcm_reconstruction_report.txt")

def main():
    """Main execution function"""
    
    print("🔧 PCM DATA RECONSTRUCTION NOTEBOOK")
    print("=" * 60)
    print("Focus: Description matching with optional parts handling")
    print("")
    
    # Initialize engine
    engine = PCMReconstructionEngine()
    
    # Load data
    if not engine.load_data():
        print("❌ Failed to load required data")
        return None
    
    # Analyze description matches
    good_matches = engine.analyze_description_matches()
    
    # Reconstruct extended BOM
    reconstructed_pcm = engine.reconstruct_extended_bom()
    
    # Export results
    if reconstructed_pcm is not None:
        engine.export_reconstructed_pcm()
        
        print("\n✅ PCM RECONSTRUCTION COMPLETE!")
        print("📊 Check output/reconstructed_pcm_extended_bom.xlsx")
        print("📋 Check output/pcm_reconstruction_report.txt")
    
    return engine

if __name__ == "__main__":
    engine = main()
