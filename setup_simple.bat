@echo off
echo 🔧 Configuration Simple de l'Environnement BOM
echo =============================================
echo.

echo 1. Test de Python...
python --version
if %errorlevel% neq 0 (
    echo Essai avec py...
    py --version
    if %errorlevel% neq 0 (
        echo ❌ Python non trouvé. Redémarrez l'invite de commande après installation.
        pause
        exit /b 1
    )
    set PYTHON_CMD=py
) else (
    set PYTHON_CMD=python
)

echo ✅ Python trouvé!
echo.

echo 2. Création de l'environnement virtuel...
%PYTHON_CMD% -m venv bom_analysis_env
if %errorlevel% neq 0 (
    echo ❌ Échec de création de l'environnement
    pause
    exit /b 1
)

echo ✅ Environnement créé!
echo.

echo 3. Activation de l'environnement...
call bom_analysis_env\Scripts\activate.bat

echo 4. Mise à jour de pip...
python -m pip install --upgrade pip

echo 5. Installation des packages essentiels...
echo Installation de pandas, numpy, scikit-learn...
pip install pandas numpy scikit-learn

echo Installation d'openpyxl pour Excel...
pip install openpyxl

echo Installation de matplotlib pour les graphiques...
pip install matplotlib

echo Installation de sentence-transformers pour l'IA...
pip install sentence-transformers

echo Installation de jupyter pour l'analyse interactive...
pip install jupyter

echo.
echo 🎉 Configuration terminée!
echo.
echo 📋 Pour utiliser votre environnement:
echo 1. Exécutez: bom_analysis_env\Scripts\activate.bat
echo 2. Puis: python bom_similarity_analysis.py
echo.
echo 📁 Placez vos fichiers Excel dans le dossier 'input'
echo.
pause
