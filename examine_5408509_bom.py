#!/usr/bin/env python3
"""
Examine the 5408509 BOM file structure
"""

import pandas as pd
import os

def examine_bom_file():
    """Examine the BOM file starting with 5408509"""
    
    file_path = r"C:\Users\<USER>\OneDrive - GE HealthCare\Documents\BOM_Cluster\input\5408509_41c3917a-ff9f-427d-9862-2379b6e85a35_1750170742103.xlsx"
    
    print("📊 EXAMINING BOM FILE 5408509")
    print("=" * 50)
    
    try:
        # Load Excel file and examine structure
        xls = pd.ExcelFile(file_path)
        print(f"📋 Available sheets: {xls.sheet_names}")
        
        # Examine second sheet (index 1)
        if len(xls.sheet_names) >= 2:
            print(f"\n📄 Examining second sheet: {xls.sheet_names[1]}")
            df = pd.read_excel(file_path, sheet_name=1)
            
            print(f"📊 Shape: {df.shape}")
            print(f"📋 Columns: {list(df.columns)}")
            
            # Show first few rows of first 4 columns
            print(f"\n📋 First 10 rows of first 4 columns:")
            if len(df.columns) >= 4:
                df_sample = df.iloc[:10, :4]
                print(df_sample.to_string())
                
                # Check if first item starts with 5408509
                print(f"\n🔍 Checking first item ID...")
                first_item = df.iloc[0, 2] if len(df.columns) > 2 else df.iloc[0, 1]
                print(f"First item: {first_item}")
                
                # Look for 5408509 in the data
                print(f"\n🔍 Looking for 5408509 in the data...")
                for col_idx in range(min(4, len(df.columns))):
                    col_data = df.iloc[:, col_idx].astype(str)
                    matches = col_data[col_data.str.contains('5408509', na=False)]
                    if len(matches) > 0:
                        print(f"Found 5408509 in column {col_idx} ({df.columns[col_idx]}): {matches.iloc[0]}")
                        break
                
            else:
                print(f"⚠️  Only {len(df.columns)} columns available")
                print(df.head().to_string())
        else:
            print("❌ File doesn't have a second sheet")
            
    except Exception as e:
        print(f"❌ Error examining file: {e}")

if __name__ == "__main__":
    examine_bom_file()
