#!/usr/bin/env python3
"""
Extraction du vrai PCM Voyager depuis le PDF
input/Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pdf
"""

import pandas as pd
import numpy as np
import re
from collections import defaultdict
import fitz  # PyMuPDF
import os

class RealVoyagerPCMExtractor:
    def __init__(self):
        """Initialize pour extraire le vrai PCM Voyager"""
        self.pdf_path = "input/Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pdf"
        self.pcm_code_pattern = r'[A-Z]\d{4,5}[A-Z]{2}'  # M7010FC, R4390JA, etc.
        self.pages_data = []
        
    def check_pdf_exists(self):
        """Vérifier que le PDF existe"""
        if os.path.exists(self.pdf_path):
            print(f"✅ PDF trouvé: {self.pdf_path}")
            return True
        else:
            print(f"❌ PDF non trouvé: {self.pdf_path}")
            return False
    
    def extract_pdf_pages(self):
        """Extraire le contenu de chaque page du PDF"""
        print("📄 EXTRACTION DU PCM VOYAGER PDF")
        print("=" * 50)
        
        if not self.check_pdf_exists():
            return False
        
        try:
            # Ouvrir le PDF
            doc = fitz.open(self.pdf_path)
            print(f"📊 PDF ouvert: {len(doc)} pages")
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # Extraire le texte
                text = page.get_text()
                
                # Extraire les éléments avec positions
                text_dict = page.get_text("dict")
                
                page_info = {
                    'page_number': page_num + 1,
                    'text': text,
                    'text_dict': text_dict,
                    'page_rect': page.rect
                }
                
                self.pages_data.append(page_info)
                print(f"  Page {page_num + 1}: {len(text)} caractères extraits")
            
            doc.close()
            print(f"✅ {len(self.pages_data)} pages extraites")
            return True
            
        except Exception as e:
            print(f"❌ Erreur extraction PDF: {e}")
            return False
    
    def find_pcm_codes_in_pages(self):
        """Trouver les codes PCM dans chaque page"""
        print("\n🔍 RECHERCHE DES CODES PCM DANS LE VRAI DOCUMENT")
        print("=" * 60)
        print("Format recherché: M7010FC, R4390JA, M3340BZ, E45801DV")
        print("")
        
        all_codes = []
        
        for page_info in self.pages_data:
            page_num = page_info['page_number']
            text = page_info['text']
            
            # Chercher les codes PCM dans le texte
            codes_found = re.findall(self.pcm_code_pattern, text)
            
            if codes_found:
                print(f"📄 Page {page_num}: {len(codes_found)} codes trouvés")
                
                # Analyser chaque code trouvé
                for code in codes_found:
                    # Trouver le contexte autour du code
                    context = self.extract_context_around_code(text, code)
                    
                    code_info = {
                        'page': page_num,
                        'code': code,
                        'context': context,
                        'description': self.extract_description_from_context(context, code)
                    }
                    
                    all_codes.append(code_info)
                    
                # Afficher les codes de cette page
                unique_codes = list(set(codes_found))
                for code in unique_codes[:5]:  # Afficher les 5 premiers
                    context = self.extract_context_around_code(text, code)
                    print(f"    {code}: {context[:50]}...")
                
                if len(unique_codes) > 5:
                    print(f"    ... et {len(unique_codes) - 5} autres codes")
                print("")
        
        print(f"✅ Total: {len(all_codes)} codes PCM trouvés dans le vrai document")
        return all_codes
    
    def extract_context_around_code(self, text, code):
        """Extraire le contexte autour d'un code"""
        # Trouver la position du code
        code_pos = text.find(code)
        if code_pos == -1:
            return ""
        
        # Extraire 100 caractères avant et après
        start = max(0, code_pos - 100)
        end = min(len(text), code_pos + len(code) + 100)
        
        context = text[start:end]
        
        # Nettoyer le contexte
        context = re.sub(r'\s+', ' ', context).strip()
        
        return context
    
    def extract_description_from_context(self, context, code):
        """Extraire la description depuis le contexte"""
        if not context:
            return ""
        
        # Retirer le code du contexte
        clean_context = context.replace(code, '').strip()
        
        # Chercher des patterns de description
        # Souvent la description est avant ou après le code
        words = clean_context.split()
        
        # Prendre les mots qui semblent être une description
        description_words = []
        for word in words:
            # Éviter les mots trop courts ou qui semblent être des codes
            if len(word) > 2 and not re.match(r'^[A-Z0-9\-_]+$', word):
                description_words.append(word)
        
        description = ' '.join(description_words[:10])  # Limiter à 10 mots
        
        return description
    
    def analyze_codes_by_page(self, all_codes):
        """Analyser les codes par page"""
        print("\n📋 ANALYSE DES CODES PAR PAGE")
        print("=" * 50)
        
        pages = defaultdict(list)
        for code_info in all_codes:
            pages[code_info['page']].append(code_info)
        
        for page_num in sorted(pages.keys()):
            page_codes = pages[page_num]
            unique_codes = list(set(code['code'] for code in page_codes))
            
            print(f"\n📄 PAGE {page_num} - {len(unique_codes)} codes uniques")
            print("-" * 40)
            
            # Afficher les codes avec leurs descriptions
            for i, code in enumerate(unique_codes[:10], 1):  # Limiter à 10 par page
                # Trouver la meilleure description pour ce code
                code_entries = [c for c in page_codes if c['code'] == code]
                best_description = max(code_entries, key=lambda x: len(x['description']))['description']
                
                print(f"{i:2d}. {code}")
                print(f"    Description: {best_description[:60]}...")
                print("")
            
            if len(unique_codes) > 10:
                print(f"    ... et {len(unique_codes) - 10} autres codes")
    
    def save_extracted_codes(self, all_codes):
        """Sauvegarder les codes extraits"""
        print("\n💾 SAUVEGARDE DES CODES EXTRAITS")
        print("=" * 40)
        
        # Créer un DataFrame
        df_codes = pd.DataFrame(all_codes)
        
        # Sauvegarder en CSV
        output_path = "output/real_voyager_pcm_codes.csv"
        df_codes.to_csv(output_path, index=False)
        print(f"✅ Codes sauvegardés: {output_path}")
        
        # Créer un résumé
        summary = {
            'total_codes': len(all_codes),
            'unique_codes': len(set(code['code'] for code in all_codes)),
            'pages_with_codes': len(set(code['page'] for code in all_codes)),
            'codes_by_page': {}
        }
        
        # Compter par page
        for code_info in all_codes:
            page = code_info['page']
            if page not in summary['codes_by_page']:
                summary['codes_by_page'][page] = 0
            summary['codes_by_page'][page] += 1
        
        print(f"📊 Résumé:")
        print(f"  Total codes: {summary['total_codes']}")
        print(f"  Codes uniques: {summary['unique_codes']}")
        print(f"  Pages avec codes: {summary['pages_with_codes']}")
        
        return df_codes, summary

def main():
    """Fonction principale"""
    print("🔍 EXTRACTION DU VRAI PCM VOYAGER")
    print("=" * 70)
    print("Document: Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pdf")
    print("Recherche: Codes format M7010FC, R4390JA, M3340BZ, E45801DV")
    print("")
    
    extractor = RealVoyagerPCMExtractor()
    
    # 1. Extraire les pages du PDF
    if not extractor.extract_pdf_pages():
        print("❌ Échec extraction PDF")
        return None
    
    # 2. Chercher les codes PCM
    all_codes = extractor.find_pcm_codes_in_pages()
    
    if not all_codes:
        print("❌ Aucun code PCM trouvé")
        return None
    
    # 3. Analyser par page
    extractor.analyze_codes_by_page(all_codes)
    
    # 4. Sauvegarder
    df_codes, summary = extractor.save_extracted_codes(all_codes)
    
    print(f"\n✅ EXTRACTION TERMINÉE")
    print(f"📊 {summary['unique_codes']} codes PCM uniques trouvés")
    print(f"📋 Prêt pour la comparaison avec la BOM")
    
    return extractor, all_codes, summary

if __name__ == "__main__":
    extractor, codes, summary = main()
