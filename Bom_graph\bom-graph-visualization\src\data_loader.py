import pandas as pd

def load_bom_data(file_path):
    """
    Load the bill of materials data from a specified file path.
    
    Args:
        file_path (str): The path to the CSV or Excel file containing the BOM data.
        
    Returns:
        pd.DataFrame: A DataFrame containing the structured BOM data.
    """
    if file_path.endswith('.csv'):
        return pd.read_csv(file_path)
    elif file_path.endswith(('.xls', '.xlsx')):
        return pd.read_excel(file_path)
    else:
        raise ValueError("Unsupported file format. Please provide a CSV or Excel file.")