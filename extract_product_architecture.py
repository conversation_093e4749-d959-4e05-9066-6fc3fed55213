#!/usr/bin/env python3
"""
Product Architecture Extractor from PowerPoint
Extracts component information with color-coded logic:
- Blue = Mandatory component
- Purple = Mandatory Selectable  
- Red = Optional
"""

import os
import pandas as pd
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
import json
from collections import defaultdict
import re

def analyze_powerpoint_architecture(pptx_path):
    """Extract product architecture from PowerPoint with color coding"""
    print("🔍 Analyzing PowerPoint Product Architecture...")
    print(f"📁 File: {os.path.basename(pptx_path)}")
    
    try:
        prs = Presentation(pptx_path)
        print(f"📊 Total slides: {len(prs.slides)}")
        
        architecture_data = []
        slide_summaries = []
        
        for slide_idx, slide in enumerate(prs.slides, 1):
            print(f"\n🔍 Analyzing Slide {slide_idx}...")
            
            slide_components = []
            slide_title = ""
            
            # Extract slide title
            if slide.shapes.title:
                slide_title = slide.shapes.title.text.strip()
                print(f"📋 Title: {slide_title}")
            
            # Analyze all shapes in slide
            for shape_idx, shape in enumerate(slide.shapes):
                component_info = analyze_shape(shape, slide_idx, shape_idx)
                if component_info:
                    slide_components.append(component_info)
                    architecture_data.append(component_info)
            
            # Slide summary
            slide_summary = {
                'slide_number': slide_idx,
                'slide_title': slide_title,
                'total_components': len(slide_components),
                'mandatory_count': len([c for c in slide_components if c['component_type'] == 'Mandatory']),
                'mandatory_selectable_count': len([c for c in slide_components if c['component_type'] == 'Mandatory Selectable']),
                'optional_count': len([c for c in slide_components if c['component_type'] == 'Optional']),
                'unclassified_count': len([c for c in slide_components if c['component_type'] == 'Unclassified'])
            }
            slide_summaries.append(slide_summary)
            
            print(f"  📦 Components found: {len(slide_components)}")
            if slide_components:
                type_counts = {}
                for comp in slide_components:
                    comp_type = comp['component_type']
                    type_counts[comp_type] = type_counts.get(comp_type, 0) + 1
                print(f"  🏷️  Types: {dict(type_counts)}")
        
        print(f"\n✅ Analysis complete: {len(architecture_data)} total components extracted")
        return architecture_data, slide_summaries
        
    except Exception as e:
        print(f"❌ Error analyzing PowerPoint: {e}")
        return [], []

def analyze_shape(shape, slide_idx, shape_idx):
    """Analyze individual shape for component information"""
    
    # Skip shapes without text or fill
    if not hasattr(shape, 'text') and not hasattr(shape, 'fill'):
        return None
    
    component_info = {
        'slide_number': slide_idx,
        'shape_index': shape_idx,
        'shape_type': str(shape.shape_type),
        'component_text': '',
        'component_type': 'Unclassified',
        'color_rgb': None,
        'color_description': '',
        'has_text': False,
        'text_content': '',
        'position_x': 0,
        'position_y': 0,
        'width': 0,
        'height': 0
    }
    
    # Extract position and size
    try:
        component_info['position_x'] = shape.left.inches if hasattr(shape, 'left') else 0
        component_info['position_y'] = shape.top.inches if hasattr(shape, 'top') else 0
        component_info['width'] = shape.width.inches if hasattr(shape, 'width') else 0
        component_info['height'] = shape.height.inches if hasattr(shape, 'height') else 0
    except:
        pass
    
    # Extract text content
    if hasattr(shape, 'text') and shape.text.strip():
        component_info['has_text'] = True
        component_info['text_content'] = shape.text.strip()
        component_info['component_text'] = shape.text.strip()
    
    # Extract color information
    color_info = extract_color_info(shape)
    if color_info:
        component_info.update(color_info)
    
    # Classify component type based on color
    component_info['component_type'] = classify_component_type(component_info['color_rgb'])
    
    # Only return if we have meaningful information
    if component_info['has_text'] or component_info['component_type'] != 'Unclassified':
        return component_info
    
    return None

def extract_color_info(shape):
    """Extract color information from shape"""
    color_info = {
        'color_rgb': None,
        'color_description': 'No color detected'
    }
    
    try:
        # Try to get fill color
        if hasattr(shape, 'fill') and shape.fill.type == 1:  # Solid fill
            if hasattr(shape.fill, 'fore_color') and hasattr(shape.fill.fore_color, 'rgb'):
                rgb = shape.fill.fore_color.rgb
                color_info['color_rgb'] = f"({rgb.r}, {rgb.g}, {rgb.b})"
                color_info['color_description'] = describe_color(rgb.r, rgb.g, rgb.b)
        
        # Try to get line color if no fill color
        elif hasattr(shape, 'line') and hasattr(shape.line, 'color'):
            if hasattr(shape.line.color, 'rgb'):
                rgb = shape.line.color.rgb
                color_info['color_rgb'] = f"({rgb.r}, {rgb.g}, {rgb.b})"
                color_info['color_description'] = describe_color(rgb.r, rgb.g, rgb.b)
        
        # Try to get text color
        elif hasattr(shape, 'text_frame'):
            for paragraph in shape.text_frame.paragraphs:
                for run in paragraph.runs:
                    if hasattr(run.font, 'color') and hasattr(run.font.color, 'rgb'):
                        rgb = run.font.color.rgb
                        color_info['color_rgb'] = f"({rgb.r}, {rgb.g}, {rgb.b})"
                        color_info['color_description'] = f"Text: {describe_color(rgb.r, rgb.g, rgb.b)}"
                        break
                if color_info['color_rgb']:
                    break
    
    except Exception as e:
        color_info['color_description'] = f"Color extraction error: {str(e)}"
    
    return color_info

def describe_color(r, g, b):
    """Describe color in human-readable terms"""
    
    # Define color ranges for classification
    if r > 200 and g < 100 and b < 100:
        return "Red"
    elif r < 100 and g < 100 and b > 200:
        return "Blue"
    elif r > 150 and g < 150 and b > 150:
        return "Purple/Magenta"
    elif r > 200 and g > 200 and b < 100:
        return "Yellow"
    elif r < 100 and g > 200 and b < 100:
        return "Green"
    elif r > 200 and g > 150 and b < 100:
        return "Orange"
    elif r < 50 and g < 50 and b < 50:
        return "Black"
    elif r > 200 and g > 200 and b > 200:
        return "White"
    elif r > 100 and g > 100 and b > 100:
        return "Gray"
    else:
        return f"Custom RGB({r},{g},{b})"

def classify_component_type(color_rgb):
    """Classify component type based on color"""
    if not color_rgb:
        return "Unclassified"
    
    # Extract RGB values
    try:
        rgb_match = re.match(r'\((\d+), (\d+), (\d+)\)', color_rgb)
        if not rgb_match:
            return "Unclassified"
        
        r, g, b = map(int, rgb_match.groups())
        
        # Classification logic based on color codes
        # Blue = Mandatory
        if r < 100 and g < 150 and b > 150:
            return "Mandatory"
        
        # Purple/Magenta = Mandatory Selectable
        elif r > 100 and g < 150 and b > 100:
            return "Mandatory Selectable"
        
        # Red = Optional
        elif r > 150 and g < 100 and b < 100:
            return "Optional"
        
        else:
            return "Unclassified"
            
    except:
        return "Unclassified"

def create_component_mapping(architecture_data):
    """Create mapping for BOM enrichment"""
    print("\n🔗 Creating component mapping for BOM enrichment...")
    
    component_mapping = {}
    
    for component in architecture_data:
        if component['has_text'] and component['text_content']:
            # Clean component text for matching
            clean_text = component['text_content'].strip().upper()
            
            # Create multiple matching keys
            matching_keys = [
                clean_text,
                clean_text.replace(' ', ''),
                clean_text.replace('-', ''),
                clean_text.replace('_', ''),
            ]
            
            # Extract potential part numbers
            part_numbers = re.findall(r'[A-Z0-9]{3,}[-_]?[A-Z0-9]*', clean_text)
            matching_keys.extend(part_numbers)
            
            # Create mapping entry
            mapping_entry = {
                'component_type': component['component_type'],
                'slide_number': component['slide_number'],
                'color_description': component['color_description'],
                'original_text': component['text_content'],
                'position': f"({component['position_x']:.1f}, {component['position_y']:.1f})"
            }
            
            # Add all matching keys
            for key in matching_keys:
                if len(key) >= 3:  # Minimum length for meaningful matching
                    component_mapping[key] = mapping_entry
    
    print(f"✅ Created {len(component_mapping)} component mappings")
    return component_mapping

def save_architecture_results(architecture_data, slide_summaries, component_mapping, output_folder):
    """Save extracted architecture data"""
    print("\n💾 Saving architecture extraction results...")
    
    # Save detailed component data
    components_df = pd.DataFrame(architecture_data)
    components_file = os.path.join(output_folder, 'product_architecture_components.xlsx')
    components_df.to_excel(components_file, index=False)
    
    # Save slide summaries
    slides_df = pd.DataFrame(slide_summaries)
    slides_file = os.path.join(output_folder, 'product_architecture_slides.xlsx')
    slides_df.to_excel(slides_file, index=False)
    
    # Save component mapping for BOM enrichment
    mapping_file = os.path.join(output_folder, 'component_mapping.json')
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(component_mapping, f, indent=2, ensure_ascii=False)
    
    # Save comprehensive report
    report_file = os.path.join(output_folder, 'architecture_extraction_report.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("PRODUCT ARCHITECTURE EXTRACTION REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("OVERVIEW\n")
        f.write("-" * 10 + "\n")
        f.write(f"Total slides analyzed: {len(slide_summaries)}\n")
        f.write(f"Total components extracted: {len(architecture_data)}\n")
        f.write(f"Component mappings created: {len(component_mapping)}\n\n")
        
        # Component type distribution
        type_counts = {}
        for comp in architecture_data:
            comp_type = comp['component_type']
            type_counts[comp_type] = type_counts.get(comp_type, 0) + 1
        
        f.write("COMPONENT TYPE DISTRIBUTION\n")
        f.write("-" * 30 + "\n")
        for comp_type, count in sorted(type_counts.items()):
            percentage = (count / len(architecture_data)) * 100
            f.write(f"{comp_type}: {count} components ({percentage:.1f}%)\n")
        f.write("\n")
        
        # Slide-by-slide summary
        f.write("SLIDE-BY-SLIDE SUMMARY\n")
        f.write("-" * 25 + "\n")
        for slide in slide_summaries:
            f.write(f"\nSlide {slide['slide_number']}: {slide['slide_title']}\n")
            f.write(f"  Total components: {slide['total_components']}\n")
            f.write(f"  Mandatory: {slide['mandatory_count']}\n")
            f.write(f"  Mandatory Selectable: {slide['mandatory_selectable_count']}\n")
            f.write(f"  Optional: {slide['optional_count']}\n")
            f.write(f"  Unclassified: {slide['unclassified_count']}\n")
    
    print("✅ Architecture results saved:")
    print(f"   - {os.path.basename(components_file)}")
    print(f"   - {os.path.basename(slides_file)}")
    print(f"   - {os.path.basename(mapping_file)}")
    print(f"   - {os.path.basename(report_file)}")

def main():
    """Main extraction function"""
    print("🏗️ PRODUCT ARCHITECTURE EXTRACTOR")
    print("=" * 50)
    
    # File paths
    pptx_path = r"input\Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pptx"
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    if not os.path.exists(pptx_path):
        print(f"❌ PowerPoint file not found: {pptx_path}")
        return
    
    # Extract architecture data
    architecture_data, slide_summaries = analyze_powerpoint_architecture(pptx_path)
    
    if not architecture_data:
        print("❌ No architecture data extracted")
        return
    
    # Create component mapping
    component_mapping = create_component_mapping(architecture_data)
    
    # Save results
    save_architecture_results(architecture_data, slide_summaries, component_mapping, output_folder)
    
    print(f"\n🎉 Architecture Extraction Complete!")
    print("=" * 50)
    print(f"📊 {len(architecture_data)} components extracted")
    print(f"🎯 {len(component_mapping)} mappings created for BOM enrichment")
    print(f"📁 Results saved in: {os.path.abspath(output_folder)}")
    
    # Show component type summary
    type_counts = {}
    for comp in architecture_data:
        comp_type = comp['component_type']
        type_counts[comp_type] = type_counts.get(comp_type, 0) + 1
    
    print(f"\n📋 Component Types Found:")
    for comp_type, count in sorted(type_counts.items()):
        percentage = (count / len(architecture_data)) * 100
        print(f"   {comp_type}: {count} ({percentage:.1f}%)")

if __name__ == "__main__":
    main()
