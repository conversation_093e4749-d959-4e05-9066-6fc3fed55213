#!/usr/bin/env python3
"""
Link ColPali-extracted Product IDs to Voyager BOM Items
Integrates PowerPoint architecture components with BOM data
"""

import pandas as pd
import numpy as np
import re
from collections import defaultdict
import json
import difflib

def load_colpali_data():
    """Load ColPali extracted components from PowerPoint"""
    print("🤖 Loading ColPali extracted components...")

    try:
        # Try to load comprehensive extraction first
        colpali_df = pd.read_csv('output/comprehensive_colpali_extraction.csv')
        print(f"✅ Loaded {len(colpali_df)} ColPali components (COMPREHENSIVE)")
        print(f"📊 Columns: {list(colpali_df.columns)}")

        # Display sample
        print("\n🔍 ColPali Components Sample:")
        print(colpali_df[['component_text', 'product_id', 'color_detected', 'component_type']].head())

        # Show distribution by page
        page_dist = colpali_df['page_number'].value_counts().sort_index()
        print(f"\n📄 Components per page: {dict(page_dist.head(10))}")

        return colpali_df

    except Exception as e:
        print(f"❌ Error loading comprehensive ColPali data: {e}")
        # Fallback to original extraction
        try:
            colpali_df = pd.read_csv('output/colpali_powerpoint_extraction.csv')
            print(f"✅ Loaded {len(colpali_df)} ColPali components (FALLBACK)")
            return colpali_df
        except Exception as e2:
            print(f"❌ Error loading fallback ColPali data: {e2}")
            return None

def load_voyager_bom():
    """Load Voyager BOM data"""
    print("📊 Loading Voyager BOM data...")
    
    try:
        bom_df = pd.read_excel('output/voyager_5408509_bom_pcm_aggregated.xlsx')
        print(f"✅ Loaded {len(bom_df)} Voyager BOM items")
        
        # Display sample
        print("\n🔍 Voyager BOM Sample:")
        print(bom_df[['Item_Number', 'Item_Description', 'Level']].head())
        
        return bom_df
        
    except Exception as e:
        print(f"❌ Error loading Voyager BOM: {e}")
        return None

def create_matching_strategies():
    """Define multiple strategies for matching ColPali IDs to BOM items"""
    
    strategies = {
        'exact_match': {
            'description': 'Exact Product ID match with Item Number',
            'weight': 1.0
        },
        'substring_match': {
            'description': 'Product ID as substring of Item Number',
            'weight': 0.9
        },
        'reverse_substring': {
            'description': 'Item Number as substring of Product ID',
            'weight': 0.8
        },
        'fuzzy_description': {
            'description': 'Fuzzy match between component text and item description',
            'weight': 0.7,
            'threshold': 80
        },
        'keyword_match': {
            'description': 'Key words from component text found in item description',
            'weight': 0.6
        },
        'pattern_match': {
            'description': 'Similar alphanumeric patterns',
            'weight': 0.5
        }
    }
    
    return strategies

def extract_keywords(text):
    """Extract meaningful keywords from text"""
    if not text or pd.isna(text):
        return []
    
    text = str(text).upper()
    
    # Remove common words and extract meaningful terms
    stop_words = {'THE', 'AND', 'OR', 'WITH', 'FOR', 'TO', 'IN', 'ON', 'AT', 'BY'}
    
    # Extract words of 3+ characters
    words = re.findall(r'\b[A-Z0-9]{3,}\b', text)
    keywords = [word for word in words if word not in stop_words]
    
    return keywords

def match_colpali_to_bom(colpali_df, bom_df, strategies):
    """Match ColPali components to BOM items using multiple strategies"""
    print("🔗 Matching ColPali components to Voyager BOM items...")
    
    matches = []
    match_stats = defaultdict(int)
    
    for _, colpali_row in colpali_df.iterrows():
        product_id = str(colpali_row['product_id']).strip()
        component_text = str(colpali_row['component_text']).strip()
        
        print(f"\n🔍 Matching: {product_id} - {component_text}")
        
        best_matches = []
        
        for _, bom_row in bom_df.iterrows():
            item_number = str(bom_row['Item_Number']).strip()
            item_desc = str(bom_row['Item_Description']).strip()
            
            match_score = 0
            match_methods = []
            
            # Strategy 1: Exact match
            if product_id.upper() == item_number.upper():
                match_score += strategies['exact_match']['weight']
                match_methods.append('exact_match')
            
            # Strategy 2: Substring match (Product ID in Item Number)
            elif product_id.upper() in item_number.upper():
                match_score += strategies['substring_match']['weight']
                match_methods.append('substring_match')
            
            # Strategy 3: Reverse substring (Item Number in Product ID)
            elif item_number.upper() in product_id.upper():
                match_score += strategies['reverse_substring']['weight']
                match_methods.append('reverse_substring')
            
            # Strategy 4: Fuzzy description match using difflib
            if component_text and item_desc:
                fuzzy_score = difflib.SequenceMatcher(None, component_text.upper(), item_desc.upper()).ratio() * 100
                if fuzzy_score >= strategies['fuzzy_description']['threshold']:
                    match_score += strategies['fuzzy_description']['weight'] * (fuzzy_score / 100)
                    match_methods.append(f'fuzzy_description_{fuzzy_score:.0f}')
            
            # Strategy 5: Keyword match
            comp_keywords = extract_keywords(component_text)
            desc_keywords = extract_keywords(item_desc)
            
            if comp_keywords and desc_keywords:
                common_keywords = set(comp_keywords) & set(desc_keywords)
                if common_keywords:
                    keyword_score = len(common_keywords) / max(len(comp_keywords), len(desc_keywords))
                    match_score += strategies['keyword_match']['weight'] * keyword_score
                    match_methods.append(f'keyword_match_{len(common_keywords)}')
            
            # Strategy 6: Pattern match (similar alphanumeric patterns)
            if re.search(r'\d{4,}', product_id) and re.search(r'\d{4,}', item_number):
                prod_numbers = re.findall(r'\d{4,}', product_id)
                item_numbers = re.findall(r'\d{4,}', item_number)
                
                for pn in prod_numbers:
                    for in_num in item_numbers:
                        if pn in in_num or in_num in pn:
                            match_score += strategies['pattern_match']['weight']
                            match_methods.append('pattern_match')
                            break
            
            # Store potential matches
            if match_score > 0:
                best_matches.append({
                    'bom_item_number': item_number,
                    'bom_item_description': item_desc,
                    'bom_level': bom_row['Level'],
                    'bom_f_e_line': bom_row['F_E_Line'],
                    'match_score': match_score,
                    'match_methods': match_methods,
                    'bom_row_index': bom_row.name
                })
        
        # Sort by match score and take best matches
        best_matches.sort(key=lambda x: x['match_score'], reverse=True)
        
        # Create match record
        if best_matches:
            top_match = best_matches[0]
            match_quality = 'High' if top_match['match_score'] >= 0.8 else 'Medium' if top_match['match_score'] >= 0.5 else 'Low'
            
            match_record = {
                'colpali_product_id': product_id,
                'colpali_component_text': component_text,
                'colpali_color': colpali_row['color_detected'],
                'colpali_type': colpali_row['component_type'],
                'colpali_confidence': colpali_row['confidence_score'],
                'colpali_slide': colpali_row.get('slide_number', colpali_row.get('page_number', 'Unknown')),
                
                'bom_item_number': top_match['bom_item_number'],
                'bom_item_description': top_match['bom_item_description'],
                'bom_level': top_match['bom_level'],
                'bom_f_e_line': top_match['bom_f_e_line'],
                
                'match_score': top_match['match_score'],
                'match_quality': match_quality,
                'match_methods': ', '.join(top_match['match_methods']),
                'alternative_matches': len(best_matches) - 1,
                'bom_row_index': top_match['bom_row_index']
            }
            
            matches.append(match_record)
            match_stats[match_quality] += 1
            
            print(f"   ✅ Best match: {top_match['bom_item_number']} - {top_match['bom_item_description'][:50]}...")
            print(f"   📊 Score: {top_match['match_score']:.2f} ({match_quality})")
        else:
            # No match found
            match_record = {
                'colpali_product_id': product_id,
                'colpali_component_text': component_text,
                'colpali_color': colpali_row['color_detected'],
                'colpali_type': colpali_row['component_type'],
                'colpali_confidence': colpali_row['confidence_score'],
                'colpali_slide': colpali_row.get('slide_number', colpali_row.get('page_number', 'Unknown')),
                
                'bom_item_number': 'NO_MATCH',
                'bom_item_description': 'NO_MATCH',
                'bom_level': None,
                'bom_f_e_line': None,
                
                'match_score': 0,
                'match_quality': 'No_Match',
                'match_methods': 'None',
                'alternative_matches': 0,
                'bom_row_index': None
            }
            
            matches.append(match_record)
            match_stats['No_Match'] += 1
            
            print(f"   ❌ No match found")
    
    print(f"\n📊 Matching Statistics:")
    for quality, count in match_stats.items():
        print(f"   {quality}: {count}")
    
    return matches, match_stats

def analyze_matches(matches):
    """Analyze the matching results"""
    print("\n📈 Analyzing matching results...")
    
    matches_df = pd.DataFrame(matches)
    
    analysis = {
        'total_colpali_components': len(matches),
        'matched_components': len([m for m in matches if m['match_quality'] != 'No_Match']),
        'unmatched_components': len([m for m in matches if m['match_quality'] == 'No_Match']),
        'high_quality_matches': len([m for m in matches if m['match_quality'] == 'High']),
        'medium_quality_matches': len([m for m in matches if m['match_quality'] == 'Medium']),
        'low_quality_matches': len([m for m in matches if m['match_quality'] == 'Low']),
        'average_match_score': np.mean([m['match_score'] for m in matches if m['match_score'] > 0]),
        'coverage_rate': len([m for m in matches if m['match_quality'] != 'No_Match']) / len(matches) * 100
    }
    
    # Analyze by component type
    type_analysis = {}
    for comp_type in matches_df['colpali_type'].unique():
        type_matches = matches_df[matches_df['colpali_type'] == comp_type]
        type_analysis[comp_type] = {
            'total': len(type_matches),
            'matched': len(type_matches[type_matches['match_quality'] != 'No_Match']),
            'coverage': len(type_matches[type_matches['match_quality'] != 'No_Match']) / len(type_matches) * 100
        }
    
    analysis['by_component_type'] = type_analysis
    
    return analysis, matches_df

def save_integration_results(matches_df, analysis, output_folder='output'):
    """Save the integration results"""
    print("💾 Saving ColPali-BOM integration results...")
    
    # Save main results
    csv_file = f'{output_folder}/colpali_bom_integration.csv'
    matches_df.to_csv(csv_file, index=False)
    
    # Save detailed Excel
    excel_file = f'{output_folder}/colpali_bom_integration_detailed.xlsx'
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # Main matches
        matches_df.to_excel(writer, sheet_name='ColPali_BOM_Matches', index=False)
        
        # High quality matches only
        high_quality = matches_df[matches_df['match_quality'] == 'High']
        high_quality.to_excel(writer, sheet_name='High_Quality_Matches', index=False)
        
        # Unmatched components
        unmatched = matches_df[matches_df['match_quality'] == 'No_Match']
        unmatched.to_excel(writer, sheet_name='Unmatched_Components', index=False)
        
        # Analysis summary
        analysis_df = pd.DataFrame([
            {'Metric': 'Total ColPali Components', 'Value': analysis['total_colpali_components']},
            {'Metric': 'Matched Components', 'Value': analysis['matched_components']},
            {'Metric': 'Unmatched Components', 'Value': analysis['unmatched_components']},
            {'Metric': 'High Quality Matches', 'Value': analysis['high_quality_matches']},
            {'Metric': 'Medium Quality Matches', 'Value': analysis['medium_quality_matches']},
            {'Metric': 'Low Quality Matches', 'Value': analysis['low_quality_matches']},
            {'Metric': 'Average Match Score', 'Value': analysis['average_match_score']},
            {'Metric': 'Coverage Rate (%)', 'Value': analysis['coverage_rate']}
        ])
        analysis_df.to_excel(writer, sheet_name='Analysis_Summary', index=False)
    
    # Save analysis JSON
    json_file = f'{output_folder}/colpali_bom_integration_analysis.json'
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2, default=str)
    
    # Create summary report
    report_file = f'{output_folder}/colpali_bom_integration_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("COLPALI-BOM INTEGRATION REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("INTEGRATION SUMMARY\n")
        f.write("-" * 20 + "\n")
        f.write(f"Total ColPali Components: {analysis['total_colpali_components']}\n")
        f.write(f"Successfully Matched: {analysis['matched_components']}\n")
        f.write(f"Unmatched: {analysis['unmatched_components']}\n")
        f.write(f"Coverage Rate: {analysis['coverage_rate']:.1f}%\n")
        f.write(f"Average Match Score: {analysis['average_match_score']:.2f}\n\n")
        
        f.write("MATCH QUALITY BREAKDOWN\n")
        f.write("-" * 25 + "\n")
        f.write(f"High Quality: {analysis['high_quality_matches']}\n")
        f.write(f"Medium Quality: {analysis['medium_quality_matches']}\n")
        f.write(f"Low Quality: {analysis['low_quality_matches']}\n\n")
        
        f.write("BY COMPONENT TYPE\n")
        f.write("-" * 20 + "\n")
        for comp_type, stats in analysis['by_component_type'].items():
            f.write(f"{comp_type}:\n")
            f.write(f"  Total: {stats['total']}\n")
            f.write(f"  Matched: {stats['matched']}\n")
            f.write(f"  Coverage: {stats['coverage']:.1f}%\n\n")
    
    print(f"✅ Results saved:")
    print(f"   📄 CSV: {csv_file}")
    print(f"   📊 Excel: {excel_file}")
    print(f"   📋 Report: {report_file}")
    print(f"   📈 Analysis: {json_file}")

def main():
    """Main integration function"""
    print("🔗 COLPALI-VOYAGER BOM INTEGRATION")
    print("=" * 50)
    
    # Load data
    colpali_df = load_colpali_data()
    bom_df = load_voyager_bom()
    
    if colpali_df is None or bom_df is None:
        print("❌ Failed to load required data")
        return
    
    # Create matching strategies
    strategies = create_matching_strategies()
    
    # Perform matching
    matches, match_stats = match_colpali_to_bom(colpali_df, bom_df, strategies)
    
    # Analyze results
    analysis, matches_df = analyze_matches(matches)
    
    # Save results
    save_integration_results(matches_df, analysis)
    
    # Display summary
    print(f"\n🎉 Integration Complete!")
    print("=" * 50)
    print(f"📊 Total Components: {analysis['total_colpali_components']}")
    print(f"✅ Successfully Matched: {analysis['matched_components']}")
    print(f"📈 Coverage Rate: {analysis['coverage_rate']:.1f}%")
    print(f"🎯 Average Match Score: {analysis['average_match_score']:.2f}")
    
    print(f"\n🏆 Best Matches:")
    high_quality_matches = matches_df[matches_df['match_quality'] == 'High']
    for _, match in high_quality_matches.head(5).iterrows():
        print(f"   {match['colpali_product_id']} → {match['bom_item_number']}")
        print(f"     {match['colpali_component_text']} → {match['bom_item_description'][:60]}...")

if __name__ == "__main__":
    main()
