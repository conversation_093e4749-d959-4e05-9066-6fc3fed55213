#!/usr/bin/env python3
"""
Integrate PowerPoint Data with BOM Clustering
Uses extracted PowerPoint architecture data to enhance BOM analysis
"""

import pandas as pd
import numpy as np
import os
import re
from sklearn.preprocessing import LabelEncoder
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score
import matplotlib.pyplot as plt

def load_powerpoint_data(powerpoint_csv):
    """Load extracted PowerPoint data"""
    print("📊 Loading PowerPoint architecture data...")
    
    if not os.path.exists(powerpoint_csv):
        print(f"⚠️  PowerPoint data not found: {powerpoint_csv}")
        print("📋 Using sample data for demonstration...")
        return create_sample_powerpoint_data()
    
    try:
        ppt_df = pd.read_csv(powerpoint_csv)
        
        # Clean and validate data (adjust column names for PDF extraction)
        if 'component_text' in ppt_df.columns:
            # PDF extraction format
            ppt_df = ppt_df.rename(columns={
                'component_text': 'Component_Text',
                'color_detected': 'Color_Observed',
                'product_id': 'Product_ID',
                'component_type': 'Component_Type'
            })

        ppt_df = ppt_df.dropna(subset=['Component_Text', 'Color_Observed'])
        ppt_df = ppt_df[ppt_df['Component_Text'].astype(str).str.strip() != '']
        
        print(f"✅ Loaded {len(ppt_df)} PowerPoint components")
        return ppt_df
        
    except Exception as e:
        print(f"❌ Error loading PowerPoint data: {e}")
        return create_sample_powerpoint_data()

def create_sample_powerpoint_data():
    """Create sample PowerPoint data for demonstration"""
    sample_data = [
        {'Component_Text': 'Main Processing Unit', 'Product_ID': '4516256', 'Color_Observed': 'Blue', 'Component_Type': 'Mandatory'},
        {'Component_Text': 'Display Module', 'Product_ID': 'DISP-15', 'Color_Observed': 'Purple', 'Component_Type': 'Mandatory_Selectable'},
        {'Component_Text': 'Extended Memory', 'Product_ID': 'MEM-EXT', 'Color_Observed': 'Red', 'Component_Type': 'Optional'},
        {'Component_Text': 'Software License', 'Product_ID': 'SW-LIC', 'Color_Observed': 'Purple', 'Component_Type': 'Mandatory_Selectable'},
        {'Component_Text': 'Cooling System', 'Product_ID': 'COOL-SYS', 'Color_Observed': 'Blue', 'Component_Type': 'Mandatory'},
        {'Component_Text': 'Accessory Kit', 'Product_ID': 'ACC-KIT', 'Color_Observed': 'Red', 'Component_Type': 'Optional'},
        {'Component_Text': 'Interface Module', 'Product_ID': 'INT-MOD', 'Color_Observed': 'Purple', 'Component_Type': 'Mandatory_Selectable'},
        {'Component_Text': 'Power Supply', 'Product_ID': 'PSU-750', 'Color_Observed': 'Blue', 'Component_Type': 'Mandatory'},
        {'Component_Text': 'Backup Battery', 'Product_ID': 'BAT-BACKUP', 'Color_Observed': 'Red', 'Component_Type': 'Optional'},
        {'Component_Text': 'Control Software', 'Product_ID': 'SW-CTRL', 'Color_Observed': 'Blue', 'Component_Type': 'Mandatory'}
    ]
    
    print("📋 Using sample PowerPoint data (10 components)")
    return pd.DataFrame(sample_data)

def create_powerpoint_mapping(ppt_df):
    """Create mapping dictionary from PowerPoint data"""
    print("🔗 Creating PowerPoint component mapping...")
    
    mapping = {}
    
    for _, row in ppt_df.iterrows():
        component_text = str(row['Component_Text']).strip()
        product_id = str(row.get('Product_ID', '')).strip()
        
        # Create multiple matching keys
        matching_keys = []
        
        # Add component text variations
        if component_text and component_text != 'nan':
            matching_keys.extend([
                component_text.upper(),
                component_text.upper().replace(' ', ''),
                component_text.upper().replace('-', ''),
                component_text.upper().replace('_', '')
            ])
        
        # Add product ID variations
        if product_id and product_id != 'nan':
            matching_keys.extend([
                product_id.upper(),
                product_id.upper().replace('-', ''),
                product_id.upper().replace('_', '')
            ])
        
        # Extract keywords from component text
        if component_text and component_text != 'nan':
            words = re.findall(r'\b\w{3,}\b', component_text.upper())
            matching_keys.extend(words)
        
        # Create mapping entry
        mapping_entry = {
            'component_type': row.get('Component_Type', 'Unknown'),
            'color_observed': row.get('Color_Observed', 'Unknown'),
            'original_text': component_text,
            'product_id': product_id,
            'slide_number': row.get('Slide_Number', 0)
        }
        
        # Add all matching keys to mapping
        for key in matching_keys:
            if len(key) >= 3:  # Minimum length for meaningful matching
                mapping[key] = mapping_entry
    
    print(f"✅ Created {len(mapping)} mapping entries")
    return mapping

def match_bom_to_powerpoint(bom_df, ppt_mapping):
    """Match BOM components to PowerPoint architecture data"""
    print("🔍 Matching BOM components to PowerPoint data...")
    
    # Initialize new columns
    bom_df['PPT_Component_Type'] = 'Unmatched'
    bom_df['PPT_Color'] = 'Unknown'
    bom_df['PPT_Match_Confidence'] = 0.0
    bom_df['PPT_Match_Method'] = 'None'
    
    matched_count = 0
    
    for idx, row in bom_df.iterrows():
        item_id = str(row['Item_ID']).upper()
        description = str(row['Item_Description']).upper()
        
        best_match = None
        best_confidence = 0
        match_method = 'None'
        
        # Method 1: Exact Item_ID match
        if item_id in ppt_mapping:
            best_match = ppt_mapping[item_id]
            best_confidence = 1.0
            match_method = 'Exact_Item_ID'
        
        # Method 2: Item_ID substring match
        elif not best_match:
            for key, mapping_entry in ppt_mapping.items():
                if len(key) >= 4 and key in item_id:
                    if len(key) / len(item_id) > best_confidence:
                        best_match = mapping_entry
                        best_confidence = len(key) / len(item_id)
                        match_method = 'Item_ID_Substring'
        
        # Method 3: Description keyword match
        if not best_match or best_confidence < 0.7:
            description_words = re.findall(r'\b\w{3,}\b', description)
            for word in description_words:
                if word in ppt_mapping:
                    confidence = 0.6  # Lower confidence for keyword match
                    if confidence > best_confidence:
                        best_match = ppt_mapping[word]
                        best_confidence = confidence
                        match_method = 'Description_Keyword'
        
        # Method 4: Fuzzy description match
        if not best_match or best_confidence < 0.5:
            for key, mapping_entry in ppt_mapping.items():
                if len(key) >= 5 and key in description:
                    confidence = 0.4  # Lowest confidence for fuzzy match
                    if confidence > best_confidence:
                        best_match = mapping_entry
                        best_confidence = confidence
                        match_method = 'Description_Fuzzy'
        
        # Apply best match
        if best_match and best_confidence >= 0.3:  # Minimum confidence threshold
            bom_df.at[idx, 'PPT_Component_Type'] = best_match['component_type']
            bom_df.at[idx, 'PPT_Color'] = best_match['color_observed']
            bom_df.at[idx, 'PPT_Match_Confidence'] = best_confidence
            bom_df.at[idx, 'PPT_Match_Method'] = match_method
            matched_count += 1
    
    match_rate = matched_count / len(bom_df)
    print(f"✅ Matched {matched_count} components ({match_rate:.1%})")
    
    # Show match statistics
    type_counts = bom_df['PPT_Component_Type'].value_counts()
    print("📊 PowerPoint component type distribution:")
    for comp_type, count in type_counts.items():
        percentage = (count / len(bom_df)) * 100
        print(f"   {comp_type}: {count} ({percentage:.1f}%)")
    
    return bom_df

def create_enhanced_features_with_ppt(bom_df):
    """Create enhanced features including PowerPoint data"""
    print("🔧 Creating enhanced features with PowerPoint integration...")
    
    # Original BOM features
    bom_df['FE_Level_1'] = bom_df['F_E_Line'].astype(str).str.split('-').str[0].astype(float)
    bom_df['FE_Level_2'] = bom_df['F_E_Line'].astype(str).str.split('-').str[1].astype(float)
    bom_df['FE_Level_3'] = bom_df['F_E_Line'].astype(str).str.split('-').str[2].astype(float)
    
    bom_df['Item_Length'] = bom_df['Item_ID'].astype(str).str.len()
    bom_df['Desc_Length'] = bom_df['Item_Description'].astype(str).str.len()
    bom_df['Desc_Word_Count'] = bom_df['Item_Description'].astype(str).str.split().str.len()
    
    # PowerPoint-derived features
    # Encode component type
    type_encoder = LabelEncoder()
    bom_df['PPT_Type_Encoded'] = type_encoder.fit_transform(bom_df['PPT_Component_Type'])
    
    # Color encoding
    color_encoder = LabelEncoder()
    bom_df['PPT_Color_Encoded'] = color_encoder.fit_transform(bom_df['PPT_Color'])
    
    # Criticality score based on PowerPoint type
    criticality_map = {
        'Mandatory': 1.0,
        'Mandatory_Selectable': 0.7,
        'Optional': 0.3,
        'Unmatched': 0.1
    }
    bom_df['PPT_Criticality_Score'] = bom_df['PPT_Component_Type'].map(criticality_map)
    
    # Match quality features
    bom_df['PPT_Match_Quality'] = bom_df['PPT_Match_Confidence']
    bom_df['PPT_Has_Match'] = (bom_df['PPT_Component_Type'] != 'Unmatched').astype(int)
    
    # Business impact scores
    bom_df['PPT_Business_Impact'] = bom_df['PPT_Criticality_Score'] * bom_df['PPT_Match_Quality']
    
    print("✅ Enhanced features created with PowerPoint integration")
    return bom_df

def perform_ppt_enhanced_clustering(bom_df):
    """Perform clustering with PowerPoint-enhanced features"""
    print("🎯 Performing PowerPoint-enhanced clustering...")
    
    # Select features for clustering
    feature_columns = [
        # Original BOM features
        'FE_Level_1', 'FE_Level_2', 'FE_Level_3',
        'Item_Length', 'Desc_Length', 'Desc_Word_Count',
        
        # PowerPoint features
        'PPT_Type_Encoded',
        'PPT_Color_Encoded',
        'PPT_Criticality_Score',
        'PPT_Match_Quality',
        'PPT_Has_Match',
        'PPT_Business_Impact'
    ]
    
    # Prepare data
    X = bom_df[feature_columns].fillna(0)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Test multiple algorithms
    algorithms = {
        'ppt_kmeans_8': KMeans(n_clusters=8, random_state=42, n_init=10),
        'ppt_kmeans_12': KMeans(n_clusters=12, random_state=42, n_init=10),
        'ppt_dbscan_05': DBSCAN(eps=0.5, min_samples=5),
        'ppt_dbscan_08': DBSCAN(eps=0.8, min_samples=8),
    }
    
    results = {}
    
    for algo_name, algorithm in algorithms.items():
        try:
            labels = algorithm.fit_predict(X_scaled)
            n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
            
            if n_clusters > 1:
                silhouette = silhouette_score(X_scaled, labels)
                
                results[algo_name] = {
                    'labels': labels,
                    'n_clusters': n_clusters,
                    'silhouette_score': silhouette,
                    'noise_points': sum(labels == -1) if -1 in labels else 0
                }
                
                print(f"  {algo_name}: {n_clusters} clusters, silhouette={silhouette:.3f}")
        
        except Exception as e:
            print(f"  ⚠️  {algo_name} failed: {e}")
    
    # Select best result
    if results:
        best_algo = max(results.keys(), key=lambda k: results[k]['silhouette_score'])
        best_result = results[best_algo]
        
        bom_df['PPT_Enhanced_Cluster'] = best_result['labels']
        
        print(f"\n🏆 Best algorithm: {best_algo}")
        print(f"📊 Clusters: {best_result['n_clusters']}")
        print(f"🎯 Silhouette score: {best_result['silhouette_score']:.3f}")
        
        return bom_df, best_result
    
    return bom_df, None

def analyze_ppt_enhanced_clusters(bom_df):
    """Analyze PowerPoint-enhanced clusters"""
    print("\n🔍 Analyzing PowerPoint-enhanced clusters...")
    
    cluster_analysis = {}
    
    for cluster_id in sorted(bom_df['PPT_Enhanced_Cluster'].unique()):
        if cluster_id == -1:  # Skip noise
            continue
        
        cluster_data = bom_df[bom_df['PPT_Enhanced_Cluster'] == cluster_id]
        
        # PowerPoint characteristics
        ppt_type_dist = dict(cluster_data['PPT_Component_Type'].value_counts())
        ppt_color_dist = dict(cluster_data['PPT_Color'].value_counts())
        
        analysis = {
            'cluster_id': cluster_id,
            'size': len(cluster_data),
            'percentage': len(cluster_data) / len(bom_df) * 100,
            
            # PowerPoint characteristics
            'ppt_types': ppt_type_dist,
            'ppt_colors': ppt_color_dist,
            'dominant_ppt_type': cluster_data['PPT_Component_Type'].mode().iloc[0] if len(cluster_data) > 0 else 'Unknown',
            'avg_criticality': cluster_data['PPT_Criticality_Score'].mean(),
            'avg_match_quality': cluster_data['PPT_Match_Quality'].mean(),
            'match_rate': cluster_data['PPT_Has_Match'].mean(),
            
            # Traditional characteristics
            'top_fe_lines': dict(cluster_data['F_E_Line'].value_counts().head(3)),
            'sample_descriptions': list(cluster_data['Item_Description'].head(3)),
            
            # Business implications
            'business_priority': determine_business_priority(ppt_type_dist, cluster_data['PPT_Criticality_Score'].mean()),
            'acv_strategy': determine_acv_strategy(ppt_type_dist),
            'circularity_approach': determine_circularity_approach(ppt_type_dist, cluster_data['PPT_Criticality_Score'].mean())
        }
        
        cluster_analysis[f'PPT_Cluster_{cluster_id}'] = analysis
    
    return cluster_analysis

def determine_business_priority(type_dist, avg_criticality):
    """Determine business priority based on PowerPoint types"""
    mandatory_count = type_dist.get('Mandatory', 0)
    total_count = sum(type_dist.values())
    
    if mandatory_count / total_count > 0.7 or avg_criticality > 0.8:
        return 'Critical'
    elif avg_criticality > 0.5:
        return 'High'
    elif avg_criticality > 0.3:
        return 'Medium'
    else:
        return 'Low'

def determine_acv_strategy(type_dist):
    """Determine ACV strategy based on component types"""
    dominant_type = max(type_dist.keys(), key=lambda k: type_dist[k]) if type_dist else 'Unknown'
    
    strategies = {
        'Mandatory': 'Core Impact Assessment - High Priority',
        'Mandatory_Selectable': 'Scenario-Based Assessment',
        'Optional': 'Modular Impact Assessment',
        'Unmatched': 'Standard Assessment Required'
    }
    
    return strategies.get(dominant_type, 'Analysis Required')

def determine_circularity_approach(type_dist, avg_criticality):
    """Determine circularity approach"""
    dominant_type = max(type_dist.keys(), key=lambda k: type_dist[k]) if type_dist else 'Unknown'
    
    if dominant_type == 'Mandatory' and avg_criticality > 0.8:
        return 'Platform Standardization + Reuse'
    elif dominant_type == 'Mandatory_Selectable':
        return 'Modular Design + Interchangeability'
    elif dominant_type == 'Optional':
        return 'Secondary Market + Upgrade Path'
    else:
        return 'Standard Circularity Assessment'

def save_ppt_enhanced_results(bom_df, cluster_analysis, output_folder):
    """Save PowerPoint-enhanced results"""
    print("\n💾 Saving PowerPoint-enhanced results...")
    
    # Save enhanced BOM data
    enhanced_file = os.path.join(output_folder, 'bom_with_powerpoint_integration.xlsx')
    bom_df.to_excel(enhanced_file, index=False)
    
    # Save cluster analysis
    if cluster_analysis:
        cluster_summary = []
        for cluster_name, analysis in cluster_analysis.items():
            summary_row = {
                'Cluster_ID': analysis['cluster_id'],
                'Size': analysis['size'],
                'Percentage': analysis['percentage'],
                'Dominant_PPT_Type': analysis['dominant_ppt_type'],
                'Avg_Criticality': analysis['avg_criticality'],
                'Match_Rate': analysis['match_rate'],
                'Business_Priority': analysis['business_priority'],
                'ACV_Strategy': analysis['acv_strategy'],
                'Circularity_Approach': analysis['circularity_approach']
            }
            cluster_summary.append(summary_row)
        
        cluster_file = os.path.join(output_folder, 'ppt_enhanced_cluster_analysis.xlsx')
        pd.DataFrame(cluster_summary).to_excel(cluster_file, index=False)
    
    print("✅ PowerPoint-enhanced results saved")

def main():
    """Main PowerPoint integration function"""
    print("🎨 POWERPOINT DATA INTEGRATION WITH BOM CLUSTERING")
    print("=" * 70)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Load BOM data
    artist_file = r"C:\Users\<USER>\OneDrive - GE HealthCare\Documents\BOM_Cluster\input\4516256_0d1582e3-6687-4d88-94c5-4c98b07e76cb_1747378299412.xlsx"
    
    try:
        df = pd.read_excel(artist_file, sheet_name=1)
        df = df.iloc[:, :4]
        df.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
        df = df.dropna(subset=['F_E_Line', 'Item_ID', 'Item_Description'])
        df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
        
        print(f"✅ Loaded {len(df)} Artist BOM items")
        
    except Exception as e:
        print(f"❌ Error loading BOM data: {e}")
        return
    
    # Load PowerPoint data (from PDF extraction)
    ppt_csv = os.path.join(output_folder, 'pdf_powerpoint_components.csv')
    ppt_df = load_powerpoint_data(ppt_csv)
    
    # Create PowerPoint mapping
    ppt_mapping = create_powerpoint_mapping(ppt_df)
    
    # Match BOM to PowerPoint
    df_matched = match_bom_to_powerpoint(df, ppt_mapping)
    
    # Create enhanced features
    df_enhanced = create_enhanced_features_with_ppt(df_matched)
    
    # Perform enhanced clustering
    df_clustered, best_result = perform_ppt_enhanced_clustering(df_enhanced)
    
    # Analyze clusters
    if best_result:
        cluster_analysis = analyze_ppt_enhanced_clusters(df_clustered)
        
        # Save results
        save_ppt_enhanced_results(df_clustered, cluster_analysis, output_folder)
        
        print(f"\n🎉 PowerPoint Integration Complete!")
        print("=" * 70)
        print(f"🏆 Best silhouette score: {best_result['silhouette_score']:.3f}")
        print(f"📊 Clusters created: {best_result['n_clusters']}")
        print(f"📁 Results saved in: {os.path.abspath(output_folder)}")

if __name__ == "__main__":
    main()
