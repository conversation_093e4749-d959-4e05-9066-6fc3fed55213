name: Unit tests

on:
  push:
    branches:
      - master
      - v*-release
  pull_request:
    branches:
      - master
      - v*-release
  workflow_dispatch:

env:
  TRANSFORMERS_IS_CI: 1

jobs:

  test_sampling:
    name: Run unit tests
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11', '3.12']
        os: [ubuntu-latest, windows-latest]
      fail-fast: false
    runs-on: ${{ matrix.os }}
    steps:
      - name: Remove unnecessary files
        run: |
          df -h /
          # Remove software and language runtimes we're not using
          sudo rm -rf \
            "$AGENT_TOOLSDIRECTORY" \
            /opt/google/chrome \
            /opt/microsoft/msedge \
            /opt/microsoft/powershell \
            /opt/pipx \
            /usr/lib/mono \
            /usr/local/julia* \
            /usr/local/lib/android \
            /usr/local/lib/node_modules \
            /usr/local/share/chromium \
            /usr/local/share/powershell \
            /usr/share/dotnet \
            /usr/share/swift
          df -h /
        if: runner.os == 'Linux'

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python environment
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Try to load cached dependencies
        uses: actions/cache@v4
        id: restore-cache
        with:
          path: ${{ env.pythonLocation }}
          key: python-dependencies-${{ matrix.os }}-${{ matrix.python-version }}-${{ hashFiles('pyproject.toml') }}-${{ env.pythonLocation }}

      - name: Install dependencies
        run: |
          # The --break-system-packages flag is used to allow pip to upgrade or install packages
          # even in environments where system-managed packages might otherwise block such operations.
          python -m pip install --upgrade pip --break-system-packages
          python -m pip install '.[train, onnx, openvino, dev]'
          python -m pip install --upgrade 'huggingface_hub[hf_xet]'

      - name: Install model2vec
        run: python -m pip install model2vec
        if: ${{ contains(fromJSON('["3.10", "3.11", "3.12"]'), matrix.python-version) }}

      - name: Run unit tests
        run: |
          python -m pytest --durations 20 -sv tests/
