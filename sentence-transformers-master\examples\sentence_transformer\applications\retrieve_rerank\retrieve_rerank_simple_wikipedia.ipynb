{"cells": [{"cell_type": "markdown", "metadata": {"id": "ZyP3dXRfcXLa"}, "source": ["# Retrieve & Re-Rank Demo over Simple Wikipedia\n", "\n", "This examples demonstrates the Retrieve & Re-Rank Setup and allows to search over [Simple Wikipedia](https://simple.wikipedia.org/wiki/Main_Page).\n", "\n", "You can input a query or a question. The script then uses semantic search\n", "to find relevant passages in Simple English Wikipedia (as it is smaller and fits better in RAM).\n", "\n", "For semantic search, we use `SentenceTransformer('multi-qa-MiniLM-L6-cos-v1')` and retrieve\n", "32 potentially passages that answer the input query.\n", "\n", "Next, we use a more powerful CrossEncoder (`cross_encoder = CrossEncoder('cross-encoder/ms-marco-MiniLM-L6-v2')`) that\n", "scores the query and all retrieved passages for their relevancy. The cross-encoder further boost the performance,\n", "especially when you search over a corpus for which the bi-encoder was not trained for.\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "X2R9TjVzNV_E", "outputId": "97bda76a-a58e-471b-b305-c8022aaf9b84"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already up-to-date: sentence-transformers in /opt/conda/lib/python3.8/site-packages (2.0.0)\n", "Requirement already up-to-date: rank_bm25 in /opt/conda/lib/python3.8/site-packages (0.2.1)\n", "Requirement already satisfied, skipping upgrade: torch>=1.6.0 in /opt/conda/lib/python3.8/site-packages (from sentence-transformers) (1.8.1)\n", "Requirement already satisfied, skipping upgrade: tqdm in /opt/conda/lib/python3.8/site-packages (from sentence-transformers) (4.51.0)\n", "Requirement already satisfied, skipping upgrade: huggingface-hub in /opt/conda/lib/python3.8/site-packages (from sentence-transformers) (0.0.8)\n", "Requirement already satisfied, skipping upgrade: torchvision in /opt/conda/lib/python3.8/site-packages (from sentence-transformers) (0.9.1)\n", "Requirement already satisfied, skipping upgrade: transformers<5.0.0,>=4.6.0 in /opt/conda/lib/python3.8/site-packages (from sentence-transformers) (4.6.1)\n", "Requirement already satisfied, skipping upgrade: scipy in /opt/conda/lib/python3.8/site-packages (from sentence-transformers) (1.6.3)\n", "Requirement already satisfied, skipping upgrade: numpy in /opt/conda/lib/python3.8/site-packages (from sentence-transformers) (1.19.2)\n", "Requirement already satisfied, skipping upgrade: nltk in /opt/conda/lib/python3.8/site-packages (from sentence-transformers) (3.6.2)\n", "Requirement already satisfied, skipping upgrade: sentencepiece in /opt/conda/lib/python3.8/site-packages (from sentence-transformers) (0.1.95)\n", "Requirement already satisfied, skipping upgrade: scikit-learn in /opt/conda/lib/python3.8/site-packages (from sentence-transformers) (0.24.2)\n", "Requirement already satisfied, skipping upgrade: typing_extensions in /opt/conda/lib/python3.8/site-packages (from torch>=1.6.0->sentence-transformers) (*******)\n", "Requirement already satisfied, skipping upgrade: filelock in /opt/conda/lib/python3.8/site-packages (from huggingface-hub->sentence-transformers) (3.0.12)\n", "Requirement already satisfied, skipping upgrade: requests in /opt/conda/lib/python3.8/site-packages (from huggingface-hub->sentence-transformers) (2.24.0)\n", "Requirement already satisfied, skipping upgrade: pillow>=4.1.1 in /opt/conda/lib/python3.8/site-packages (from torchvision->sentence-transformers) (8.1.2)\n", "Requirement already satisfied, skipping upgrade: tokenizers<0.11,>=0.10.1 in /opt/conda/lib/python3.8/site-packages (from transformers<5.0.0,>=4.6.0->sentence-transformers) (0.10.2)\n", "Requirement already satisfied, skipping upgrade: sacremoses in /opt/conda/lib/python3.8/site-packages (from transformers<5.0.0,>=4.6.0->sentence-transformers) (0.0.45)\n", "Requirement already satisfied, skipping upgrade: regex!=2019.12.17 in /opt/conda/lib/python3.8/site-packages (from transformers<5.0.0,>=4.6.0->sentence-transformers) (2021.4.4)\n", "Requirement already satisfied, skipping upgrade: packaging in /opt/conda/lib/python3.8/site-packages (from transformers<5.0.0,>=4.6.0->sentence-transformers) (20.9)\n", "Requirement already satisfied, skipping upgrade: click in /opt/conda/lib/python3.8/site-packages (from nltk->sentence-transformers) (7.1.2)\n", "Requirement already satisfied, skipping upgrade: joblib in /opt/conda/lib/python3.8/site-packages (from nltk->sentence-transformers) (1.0.1)\n", "Requirement already satisfied, skipping upgrade: threadpoolctl>=2.0.0 in /opt/conda/lib/python3.8/site-packages (from scikit-learn->sentence-transformers) (2.1.0)\n", "Requirement already satisfied, skipping upgrade: idna<3,>=2.5 in /opt/conda/lib/python3.8/site-packages (from requests->huggingface-hub->sentence-transformers) (2.10)\n", "Requirement already satisfied, skipping upgrade: certifi>=2017.4.17 in /opt/conda/lib/python3.8/site-packages (from requests->huggingface-hub->sentence-transformers) (2020.12.5)\n", "Requirement already satisfied, skipping upgrade: chardet<4,>=3.0.2 in /opt/conda/lib/python3.8/site-packages (from requests->huggingface-hub->sentence-transformers) (3.0.4)\n", "Requirement already satisfied, skipping upgrade: urllib3!=1.25.0,!=1.25.1,<1.26,>=1.21.1 in /opt/conda/lib/python3.8/site-packages (from requests->huggingface-hub->sentence-transformers) (1.25.11)\n", "Requirement already satisfied, skipping upgrade: six in /opt/conda/lib/python3.8/site-packages (from sacremoses->transformers<5.0.0,>=4.6.0->sentence-transformers) (1.15.0)\n", "Requirement already satisfied, skipping upgrade: pyparsing>=2.0.2 in /opt/conda/lib/python3.8/site-packages (from packaging->transformers<5.0.0,>=4.6.0->sentence-transformers) (2.4.7)\n"]}], "source": ["!pip install -U sentence-transformers rank_bm25"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 431, "referenced_widgets": ["89533b9769bd4fe5be0068b381a7b554", "95bcab1f8336422aafa453f8412d9eed", "7e15806e1ab74d6893f9c5f4ede9b5af", "f9a01234d31e48cbb2b02f1b34acbbd1", "89d89e63246f4e1d80b8a2a05db28a19", "4d2cd27435f44a4f8bdbdd3e17c8c86a", "27d20db7a7f346ea81e3abe88868cfcd", "e47e734b2bbc42d1b9a32e1b1dca6451", "fae4387adeac4391ba73c21643e6034f", "be0fac5d7a974f47ac5b8a5e6ed9e4d6", "938510ed19db4fcbadbe730584ca5e58", "33b91b75207249a7b885c317c95503dd", "7d0105cc6432438581e71fad681ae2d8", "16d9436e5caf448a883bbdac3dc930dd", "614511ff768642299c1b9c19abfb56a6", "362ab62385884739ba1de446dddf1b87", "dd4f3ca182c54678887ff51f75877997", "bbfae35891b7490f8da4a60627fa5a16", "bf43844c257c4ee99d5f2cf181179c81", "aec7bd589fb04ecb999ef9a60dd60afa", "fc984d27599d41c2be86c13dcc66a171", "a6c3c35f17394bc4a66d460f91cd6833", "f10d1c0166f941bcba75f9470db86651", "10c12f39a3b340a98f6db972e9d37921", "1c198b0a475f42fe836c19813f838100", "1655064d0c164b778220e9a4da96d2ea", "4fd3399b50224953806f5a646d1ffd89", "fe98ad30ffaa4bcfa09c5c01accdd9b8", "cf475534803f42718e6a87e645585357", "88a71f6953af4259accf096fda7356b1", "f0dca993eee34ed8b9c0a9ead6cbbf68", "85a56dd0bd8549e790759f3f4ad0df7f", "11c91ed9d2f049c9aec2124c6c926f88", "0bee2cf221bf4497b527369b3905553e", "a39d7b521ea540cd8af250584340e427", "7ea6f545038f4422850e4a2d75345e65", "5d1b0a2aa5da48f3ad8e428d7b4ead27", "297a74178198439ab2847d90f2ee56e0", "3fd97a1c9ef74e80b999aba18dd20075", "581eef89f62245a0aaae36179e688843", "e45327599f404ae09e5452978695d4e8", "258161ce772d470fbc004f7736cea24e", "877f416c38944e3db52917a6497b3525", "9c35672b3e224a34b7070099c5067426", "e120277c4a7141fca20e0115486a0be1", "33bd233b64664e639dea85c60f0372dd", "4c308b4ed1644ae790c878e190642c68", "55fd02cec9a74a92a7c0bf4cf713bb28", "703fe20c0249435eb847fe29a5a23c52", "6bceb6fffeb84566a07aeb340d7c920f", "1390bf4686774d6581348f87f908a400", "f3ba3908cf1d4000b0da73107cee1526", "e6f53f85bbbb4e6caf992e913d9cce88", "64eb0085f9a84129937350004868ad54", "3b01947b6c50476db6c9bcb21b03491b", "bf2f3698d10f47fdb65dc82e54a9e566", "e00bfbac209b4caa92ac3c679381a5a5", "44d63b5526824e0f81d62dfba320bdc1", "442d3ee2958b4db999c5e1624986d201", "cc09077698c2445c8c7a7be99a5eb931", "ec1353a6b4e8450f84b4302a50e2fba6", "c119469e196f4ceca7b1368f2a38d18b", "67346148e616426c83f10f203be5da8d", "566d714778c040908ce443cc51c926bb"]}, "id": "D_hDi8KzNgMM", "outputId": "326a0b64-f3fd-4d28-b5f6-a18ace1debd9"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "350c9b9046e845ce81311ac05c38d523", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=737.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ca5065fb69bc4be8b5888898a8275079", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=9216.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cab22180e2944146a8793e552f543a46", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=612.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "85623323d08648eda0f84ad002319e29", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=116.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "67ddbc49a5c341ef8f5f8eee5171c686", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=25457.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2e6ea096bb9e4e058adcf5be79e53139", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=349.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cff9b5a83d1a435daa021d4fd2489eec", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=90888945.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "164b6918179c4d72ab2e545653356f81", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=53.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d09710be64b84512971b34ddae26adc5", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=112.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "17819727fb494f158069daa1bf8963cf", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=466247.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "914e5a8fd98d4c7a9722f1b05b24f53b", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=383.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "02c97554783545118ceab68cc66ad47b", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=13846.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "caf41586a73f4735a121e5cbe9b67606", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=231508.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c1059f1f0ef44c9db9e00d18afb23254", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Downloading'), FloatProgress(value=0.0, max=190.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Passages: 169597\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "778a221e446740239c9f9ba037b93c6e", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value='Batches'), FloatProgress(value=0.0, max=5300.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import json\n", "from sentence_transformers import SentenceTransformer, CrossEncoder, util\n", "import gzip\n", "import os\n", "import torch\n", "\n", "if not torch.cuda.is_available():\n", "    print(\"Warning: No GPU found. Please add GPU to your notebook\")\n", "\n", "\n", "#We use the Bi-Encoder to encode all passages, so that we can use it with semantic search\n", "bi_encoder = SentenceTransformer('multi-qa-MiniLM-L6-cos-v1')\n", "bi_encoder.max_seq_length = 256     #Truncate long passages to 256 tokens\n", "top_k = 32                          #Number of passages we want to retrieve with the bi-encoder\n", "\n", "#The bi-encoder will retrieve 100 documents. We use a cross-encoder, to re-rank the results list to improve the quality\n", "cross_encoder = CrossEncoder('cross-encoder/ms-marco-MiniLM-L6-v2')\n", "\n", "# As dataset, we use Simple English Wikipedia. Compared to the full English wikipedia, it has only\n", "# about 170k articles. We split these articles into paragraphs and encode them with the bi-encoder\n", "\n", "wikipedia_filepath = 'simplewiki-2020-11-01.jsonl.gz'\n", "\n", "if not os.path.exists(wikipedia_filepath):\n", "    util.http_get('http://sbert.net/datasets/simplewiki-2020-11-01.jsonl.gz', wikipedia_filepath)\n", "\n", "passages = []\n", "with gzip.open(wikipedia_filepath, 'rt', encoding='utf8') as fIn:\n", "    for line in fIn:\n", "        data = json.loads(line.strip())\n", "\n", "        #Add all paragraphs\n", "        #passages.extend(data['paragraphs'])\n", "\n", "        #Only add the first paragraph\n", "        passages.append(data['paragraphs'][0])\n", "\n", "print(\"Passages:\", len(passages))\n", "\n", "# We encode all passages into our vector space. This takes about 5 minutes (depends on your GPU speed)\n", "corpus_embeddings = bi_encoder.encode(passages, convert_to_tensor=True, show_progress_bar=True)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 122, "referenced_widgets": ["ab19ef7e9763402589b15b4ccd1b5d6b", "e5672d2910174d87be7434aa7bbe78c0", "399bf19c69ed4a1490c6609284bacf27", "c25ffe4acbac42ca9755248338ed864b", "79e0e838f33f4c51b24bdfd12c7d0a00", "e5595b23a62643aab844c875127a0a0a", "dee30a855454476aaa572af57e6b7405", "7d4f6eb85c0f4b06bf332c278da7f225"]}, "id": "0rueR6ovrs01", "outputId": "965b70da-90bb-4f6f-fc5a-fe44ba26c28c"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2d834f0d787744c88abb8876c338a3b9", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(HTML(value=''), FloatProgress(value=0.0, max=169597.0), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["# We also compare the results to lexical search (keyword search). Here, we use \n", "# the BM25 algorithm which is implemented in the rank_bm25 package.\n", "\n", "from rank_bm25 import BM25Okapi\n", "from sklearn.feature_extraction import _stop_words\n", "import string\n", "from tqdm.autonotebook import tqdm\n", "import numpy as np\n", "\n", "\n", "# We lower case our text and remove stop-words from indexing\n", "def bm25_tokenizer(text):\n", "    tokenized_doc = []\n", "    for token in text.lower().split():\n", "        token = token.strip(string.punctuation)\n", "\n", "        if len(token) > 0 and token not in _stop_words.ENGLISH_STOP_WORDS:\n", "            tokenized_doc.append(token)\n", "    return tokenized_doc\n", "\n", "\n", "tokenized_corpus = []\n", "for passage in tqdm(passages):\n", "    tokenized_corpus.append(bm25_tokenizer(passage))\n", "\n", "bm25 = BM25Okapi(tokenized_corpus)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "UlArb7kqN3Re"}, "outputs": [], "source": ["# This function will search all wikipedia articles for passages that\n", "# answer the query\n", "def search(query):\n", "    print(\"Input question:\", query)\n", "\n", "    ##### BM25 search (lexical search) #####\n", "    bm25_scores = bm25.get_scores(bm25_tokenizer(query))\n", "    top_n = np.argpartition(bm25_scores, -5)[-5:]\n", "    bm25_hits = [{'corpus_id': idx, 'score': bm25_scores[idx]} for idx in top_n]\n", "    bm25_hits = sorted(bm25_hits, key=lambda x: x['score'], reverse=True)\n", "    \n", "    print(\"Top-3 lexical search (BM25) hits\")\n", "    for hit in bm25_hits[0:3]:\n", "        print(\"\\t{:.3f}\\t{}\".format(hit['score'], passages[hit['corpus_id']].replace(\"\\n\", \" \")))\n", "\n", "    ##### Semantic Search #####\n", "    # Encode the query using the bi-encoder and find potentially relevant passages\n", "    question_embedding = bi_encoder.encode(query, convert_to_tensor=True)\n", "    question_embedding = question_embedding.cuda()\n", "    hits = util.semantic_search(question_embedding, corpus_embeddings, top_k=top_k)\n", "    hits = hits[0]  # Get the hits for the first query\n", "\n", "    ##### Re-Ranking #####\n", "    # Now, score all retrieved passages with the cross_encoder\n", "    cross_inp = [[query, passages[hit['corpus_id']]] for hit in hits]\n", "    cross_scores = cross_encoder.predict(cross_inp)\n", "\n", "    # Sort results by the cross-encoder scores\n", "    for idx in range(len(cross_scores)):\n", "        hits[idx]['cross-score'] = cross_scores[idx]\n", "\n", "    # Output of top-5 hits from bi-encoder\n", "    print(\"\\n-------------------------\\n\")\n", "    print(\"Top-3 Bi-Encoder Retrieval hits\")\n", "    hits = sorted(hits, key=lambda x: x['score'], reverse=True)\n", "    for hit in hits[0:3]:\n", "        print(\"\\t{:.3f}\\t{}\".format(hit['score'], passages[hit['corpus_id']].replace(\"\\n\", \" \")))\n", "\n", "    # Output of top-5 hits from re-ranker\n", "    print(\"\\n-------------------------\\n\")\n", "    print(\"Top-3 Cross-Encoder Re-ranker hits\")\n", "    hits = sorted(hits, key=lambda x: x['cross-score'], reverse=True)\n", "    for hit in hits[0:3]:\n", "        print(\"\\t{:.3f}\\t{}\".format(hit['cross-score'], passages[hit['corpus_id']].replace(\"\\n\", \" \")))\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2J0Zxgw0artg", "outputId": "91a700f5-bb26-444e-abd3-c41721a1b708"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: What is the capital of the United States?\n", "Top-3 lexical search (BM25) hits\n", "\t13.316\tCapital punishment (the death penalty) has existed in the United States since before the United States was a country. As of 2017, capital punishment is legal in 30 of the 50 states. The federal government (including the United States military) also uses capital punishment.\n", "\t11.434\tOhio is one of the 50 states in the United States. Its capital is Columbus. Columbus also is the largest city in Ohio.\n", "\t11.179\tNevada is one of the United States' states. Its capital is Carson City. Other big cities are Las Vegas and Reno.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.622\tCities in the United States:\n", "\t0.597\tThe United States Capitol is the building where the United States Congress meets. It is the center of the legislative branch of the U.S. federal government. It is in Washington, D.C., on top of Capitol Hill at the east end of the National Mall.\n", "\t0.596\tIn the United States:\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t8.906\tWashington, D.C. (also known as simply Washington or D.C., and officially as the District of Columbia) is the capital of the United States. It is a federal district. The President of the USA and many major national government offices are in the territory. This makes it the political center of the United States of America.\n", "\t3.755\tA capital city (or capital town or just capital) is a city or town, specified by law or constitution, by the government of a country, or part of a country, such as a state, province or county. It usually serves as the location of the government's central meeting place and offices. Most of the country's leaders and officials work in the capital city.\n", "\t3.681\tThe United States Capitol is the building where the United States Congress meets. It is the center of the legislative branch of the U.S. federal government. It is in Washington, D.C., on top of Capitol Hill at the east end of the National Mall.\n"]}], "source": ["search(query = \"What is the capital of the United States?\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WawjqQBJa3FP", "outputId": "95ce1de8-a937-4bcd-d07c-0eccb5f90af1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: What is the best orchestra in the world?\n", "Top-3 lexical search (BM25) hits\n", "\t15.328\tThe BBC Symphony Orchestra is the main orchestra of the British Broadcasting Corporation. It is one of the best orchestras in Britain.\n", "\t15.320\tThe NHK Symphony Orchestra is a Japanese orchestra based in Tokyo, Japan. In Japanese it is written: NHK交響楽団, pronounced: <PERSON><PERSON><PERSON><PERSON><PERSON>. When the orchestra was started in 1926 it was called \"New Symphony Orchestra\". It was the first large professional orchestra in Japan. Later, it changed its name to \"Japan Symphony Orchestra\". In 1951 it started to get money from the Japanese radio station NHK (Nippon Hōsō Kyōkai), so it changed its name again to the name it has now. It is thought of as the best orchestra in Japan. They have played in many parts of the world, including at the BBC Proms in London.\n", "\t14.079\tThe Bamberger Symphoniker (Bamberg Symphony Orchestra) is a world-famous orchestra from the city of Bamberg, Germany. It was formed in 1946. Most of the musicians who formed the orchestra were Germans who had been forced to leave Czechoslovakia after the World War II. Most of them had previously been members of the German Philharmonic Orchestra of Prague.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.701\tThe Vienna Philharmonic (in German: die Wiener Philharmoniker) is an orchestra based in Vienna, Austria. It is thought of as one of the greatest orchestras in the world.\n", "\t0.641\tThe Vienna Symphony () is an orchestra in Vienna, Austria.\n", "\t0.640\tThe Berlin Philharmonic (in German: Die Berliner Philharmoniker), is an orchestra from Berlin, Germany. It is one of the greatest orchestras in the world. The conductor of the orchestra is Sir <PERSON>.\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t5.952\tThe London Symphony Orchestra (LSO) is one of the most famous orchestras of the world. They are based in London's Barbican Centre, but they often tour to lots of different countries.\n", "\t5.794\tThe Vienna Philharmonic (in German: die Wiener Philharmoniker) is an orchestra based in Vienna, Austria. It is thought of as one of the greatest orchestras in the world.\n", "\t5.324\tThe Berlin Philharmonic (in German: Die Berliner Philharmoniker), is an orchestra from Berlin, Germany. It is one of the greatest orchestras in the world. The conductor of the orchestra is Sir <PERSON>.\n"]}], "source": ["search(query = \"What is the best orchestra in the world?\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Ei9Q1roSa9GE", "outputId": "7b4323f1-badf-4b11-aed4-a04ee7a7be4c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: Number countries Europe\n", "Top-3 lexical search (BM25) hits\n", "\t13.795\t<PERSON> is a Scottish singer and songwriter. She became famous in 2007 with her first album \"This Is The Life\" and her first single \"Poison Prince\". She has become even more successful in Europe since her single \"This Is The Life\" charted at number 1 in many European countries.\n", "\t13.758\tThe Croatian language is spoken mainly throughout the countries of Croatia and Bosnia and Herzegovina and in the surrounding countries of Europe.\n", "\t13.019\tOrganization for Security and Co-operation in Europe (OSCE) is an international organization for peace and human rights. Presently, it has 57 countries as its members. Most of the member countries of the OSCE are from Europe, the Caucasus, Central Asia and North America.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.538\tThe Council of Europe (, ) is an international organization of 47 member states in the European region. One of its first successes was the European Convention on Human Rights in 1950, which serves as the basis for the European Court of Human Rights.\n", "\t0.531\tEngland is a country in Europe. It is a country with over sixty cities in it. It is in a union with Scotland, Wales and Northern Ireland. All four countries are in the British Isles and are part of the United Kingdom (UK).\n", "\t0.507\tEurope is a Swedish rock band. The band was started by <PERSON> and <PERSON> in 1979. Their song \"The Final Countdown\" was a big hit in 1986.\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t5.199\tThe European Union (abbreviation: EU) is a confederation of 27 member countries in Europe established by the Maastricht Treaty in 1992-1993. The EU grew out of the European Economic Community (EEC) which was established by the Treaties of Rome in 1957. It has created a common economic area with Europe-wide laws allowing the citizens of EU countries to move and trade in other EU countries almost the same as they do in their own. Nineteen of these countries also share the same type of money: the euro.\n", "\t3.202\tA European Union member state is any one of the twenty-seven countries that have joined the European Union (EU) since it was found in 1958 as the European Economic Community (EEC). From an original membership of six states, there have been five successive enlargements. The largest happened on 1 May 2004, when ten member states joined.\n", "\t2.798\tThe Schengen Area is an area that includes 26 European countries. All of those countries have signed the Schengen Agreement in Schengen, Luxembourg in 1985.\n"]}], "source": ["search(query = \"Number countries Europe\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YVQyKpIjbSdC", "outputId": "5ab13487-4259-4516-b913-6e3d14c140ce"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: When did the cold war end?\n", "Top-3 lexical search (BM25) hits\n", "\t17.374\tThe Cold War was the tense relationship between the United States (and its allies), and the Soviet Union (the USSR and its allies) between the end of World War II and the fall of the Soviet Union. It is called the \"Cold\" War because the US and the USSR never actually fought each other directly. Instead, they opposed each other in conflicts known as proxy wars, where each country chose a side to support.\n", "\t17.291\tThe Reagan Doctrine was a document by the United States under the Reagan Administration. It was about being against the global influence of the Soviet Union during the final years of the Cold War. The doctrine lasted for less than a decade, it was the most important document of United States foreign policy from the early 1980s until the end of the Cold War in 1991.\n", "\t15.420\tCold Norton is a village and civil parish in Maldon District, Essex, England. In 2001 there were 1103 people living in Cold Norton. Cold Norton is at the south-east end of the Danbury Ridge.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.613\tThe Cold War was the tense relationship between the United States (and its allies), and the Soviet Union (the USSR and its allies) between the end of World War II and the fall of the Soviet Union. It is called the \"Cold\" War because the US and the USSR never actually fought each other directly. Instead, they opposed each other in conflicts known as proxy wars, where each country chose a side to support.\n", "\t0.557\tThe Continuation War was a war between Finland and the Soviet Union. It was fought between June 25, 1941 and September 19, 1944. It continued the Winter War. Nazi Germany helped Finland as part of the Eastern Front (World War II). The ceasefire started on September 4 at 7.00 am in the Finnish side. The Soviet Union's ceasefire started on September 5. The first peace treaty was signed on September 19 and the final one on February 10, 1947 in Paris.\n", "\t0.548\tThe Winter War (30 November 1939 - 13 March 1940) was a conflict fought between the Soviet Union and Finland. It began when the Soviet Union tried to invade Finland soon after the Invasion of Poland. The Soviet military forces expected a victory over Finland in a few weeks, because the Soviet army had many more tanks and planes than the Finnish army.\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t5.203\tThe Cold War was the tense relationship between the United States (and its allies), and the Soviet Union (the USSR and its allies) between the end of World War II and the fall of the Soviet Union. It is called the \"Cold\" War because the US and the USSR never actually fought each other directly. Instead, they opposed each other in conflicts known as proxy wars, where each country chose a side to support.\n", "\t2.531\tA speech was made by American President <PERSON> to the U.S. Congress on 12 March 1947. In this speech he said he thought that The United States should help Greece and Turkey to stop them being 'Totalitarianists' although he meant Soviet Communism. This became known as the Truman Doctrine. Some Historians believe that this was the start of the Cold War.\n", "\t2.079\tThe Sino-Soviet split (1960–1989) was a time when the relations between the People's Republic of China and the Soviet Union weakened during the Cold War. Eventually, China's leader, <PERSON>, decided to break the alliance with the Soviet Union.\n"]}], "source": ["search(query = \"When did the cold war end?\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iVomHSSsbcut", "outputId": "83c8787f-cf25-4b63-caf1-2b5fea8efc4b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: How long do cats live?\n", "Top-3 lexical search (BM25) hits\n", "\t22.997\tReliable information on the lifespans of house cats is hard to find. However, research has been done to get an estimate (an educated guess) on how long cats usually live. Cats usually live for 13 to 20 years. Sometimes cats can live for 22 to 30 years but there are claims of cats dying at ages of more than 30 years old.\n", "\t16.974\tThe sabertoothed cats or sabretooth cats are some of the best known and most popular extinct animals. They are among the most impressive carnivores that ever have lived. These cats had long canines and jaws which opened wider than modern cats. This suggests a different style of killing from modern felines.\n", "\t16.490\tThe Cyprus cat is a breed of cat. These cats are thought to have first come from ancient Egypt or Palestine. They were brought to the island of Cyprus by <PERSON><PERSON>. These are now common domestic cats that live in homes or outside. Many of these cats still live all over Cyprus. But, a large number are now feral. This means they are not tame and they run wild.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.765\tReliable information on the lifespans of house cats is hard to find. However, research has been done to get an estimate (an educated guess) on how long cats usually live. Cats usually live for 13 to 20 years. Sometimes cats can live for 22 to 30 years but there are claims of cats dying at ages of more than 30 years old.\n", "\t0.531\t<PERSON><PERSON><PERSON> (August 3, 1967 - August 10, 2005) was a female cat who died in 2005 at the age of 38. She was the oldest cat ever recorded, according to the 2010 edition of \"Guinness World Records\".\n", "\t0.508\tThe cat righting reflex is a cat's natural ability to turn itself around as it falls so it will land on its feet. This righting reflex starts to happen at 3–4 weeks of age. The cat has entirely learned how to do this by 6–7 weeks. Cats are able to do this because they have a flexible backbone and a clavicle that does not move. The minimum height needed for this to happen safely in most cats is about 12 inches.\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t10.431\tReliable information on the lifespans of house cats is hard to find. However, research has been done to get an estimate (an educated guess) on how long cats usually live. Cats usually live for 13 to 20 years. Sometimes cats can live for 22 to 30 years but there are claims of cats dying at ages of more than 30 years old.\n", "\t2.998\tThe sand cat (\"<PERSON>lis margarita\") is a small wild cat in the Felinae subfamily. It is distributed over African and Asian deserts. Sometimes people call it \"desert cat,\" but that is really the name of a different animal. The sand cat does live in deserts, even the Sahara and Arabian Desert. It is also found in Iran and Pakistan. In zoos, this cat can live for up to 13 years.\n", "\t2.348\tBobcat (\"Lynx rufus\") are fierce cats that live in forests, swamps, mountains, prairie, and deserts in much of North America. Bobcats are generally nocturnal (most active at night), but are most active at dawn and dusk. They spend the day in their den (a cave, hollow log or rock crevice). They are very good climbers and swimmers. Bobcats are eaten by cougars, coyotes, wolves, and bears. Bobcats usually live from 10 to 14 years. Bobcats and lynxes are closely related.\n"]}], "source": ["search(query = \"How long do cats live?\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QIkvohZ8bxMu", "outputId": "5722f299-395b-45e2-9476-0bb28f437318"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: How many people live in Toronto?\n", "Top-3 lexical search (BM25) hits\n", "\t15.978\tMarkham, Ontario is a city in Regional Municipality of York, in the Greater Toronto Area of Southern Ontario, Canada. There are twice as many people there as in 1990. 261,573 people live in Markham. It is the 4th largest town in the Greater Toronto Area, after Toronto, Mississauga, and Brampton.\n", "\t11.299\tThe Toronto Zoo is a zoo in Toronto, Ontario, Canada. With , the Toronto Zoo is the largest zoo in Canada.\n", "\t10.679\t<PERSON><PERSON><PERSON> (; born ) is a Canadian politician. He is a Toronto city councillor. He is the person that represents Ward 16, an area of Toronto. He is a chairperson of the Employee and Labour Relations Committee in Toronto's municipal government and is also the deputy mayor of Toronto. He is also part of the board of the Toronto Transit Commission and the Toronto Hydro.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.604\tVaughan is a city in Ontario, Canada, 335,000 people live there .\n", "\t0.595\tToronto is a city in Ohio in the United States.\n", "\t0.594\tToronto is a city in Kansas, United States.\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t4.448\tIt has about 110,000 people living there.\n", "\t3.632\tThe 2010 census counted 23,302 people living in the city. (2005 showed 24,709). The city was founded on January 1, 1955. Much of the city was heavily damaged by the 2011 Tōhoku earthquake and tsunami.\n", "\t3.372\tAs of January 1, 2010, about 164,294 people lived there. That was 295.83 people per km². The total area is 555.35 km².\n"]}], "source": ["search(query = \"How many people live in Toronto?\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MpWjJtL_iFBG", "outputId": "08d85c4c-5278-47db-c122-110df3e1cc48"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: Oldest US president\n", "Top-3 lexical search (BM25) hits\n", "\t11.010\t<PERSON><PERSON><PERSON><PERSON> (; 24 April 1919 – 15 November 2013) was a Greek-Cypriot politician. He was the fourth President of Cyprus. He was the oldest living former President of the Republic of Cyprus.\n", "\t9.237\t<PERSON> (Tatuí, November 1, 1945), is a Brazilian jurist. He is the oldest member of the Supreme Federal Court of Brazil. He was nominated by President <PERSON> in 1989.\n", "\t8.872\tUSS \"Constitution\" is a wooden, three-masted heavy frigate of the United States Navy. Named by President <PERSON> after the Constitution of the United States of America, she is the world's oldest commissioned naval vessel afloat.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.645\t<PERSON> (February 9, 1773 – April 4, 1841) was the 9th President of the United States. His nickname was \"Old Tippecanoe \" and he was a well-respected war veteran. <PERSON> served the shortest term of any United States President. His term lasted for exactly one month.\n", "\t0.624\t<PERSON> (May 11, 1906 – December 27, 2018) was an American supercentenarian. He was the oldest verified surviving American World War II veteran, as well as the oldest American man. In 2013 he was honored by President <PERSON>. On that same Memorial Day, <PERSON><PERSON> met with Texas Governor <PERSON>. He was born in Bastrop County, Texas.\n", "\t0.620\t<PERSON><PERSON> (November 24, 1877 – April 30, 1956) was a Democratic member of the U.S. House of Representatives and the United States Senate from Paducah, Kentucky, majority leader of the Senate, and the thirty-fifth Vice President of the United States. He was the oldest Vice President of the United States at the age of . He ran for president in 1952, however the Democratic Party primaries had nominated <PERSON><PERSON> instead and labor union leaders rejected him to run because of old age (74).\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t6.089\t<PERSON><PERSON> (November 24, 1877 – April 30, 1956) was a Democratic member of the U.S. House of Representatives and the United States Senate from Paducah, Kentucky, majority leader of the Senate, and the thirty-fifth Vice President of the United States. He was the oldest Vice President of the United States at the age of . He ran for president in 1952, however the Democratic Party primaries had nominated <PERSON><PERSON> instead and labor union leaders rejected him to run because of old age (74).\n", "\t5.538\t<PERSON> (May 11, 1906 – December 27, 2018) was an American supercentenarian. He was the oldest verified surviving American World War II veteran, as well as the oldest American man. In 2013 he was honored by President <PERSON>. On that same Memorial Day, <PERSON><PERSON> met with Texas Governor <PERSON>. He was born in Bastrop County, Texas.\n", "\t4.702\t<PERSON> nicknamed \"C<PERSON><PERSON> Jack\" (November 22, 1868 – November 7, 1967) was the forty-fourth Speaker of the United States House of Representatives (1931-33) and the thirty-second Vice President of the United States (1933-41). <PERSON> once described the Vice-Presidency as being \"not worth a bucket of warm spit.\" Also, he lived to be 98 years old. That made him the oldest former Vice President of the United States.\n"]}], "source": ["search(query = \"Oldest US president\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zo3NOayXiQME", "outputId": "f385e4b8-b99a-401a-ae5e-8e6c11d96523"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: Coldest place earth\n", "Top-3 lexical search (BM25) hits\n", "\t24.891\tEast Antarctica, also called Greater Antarctica, is the largest part (two-thirds) of the Antarctic continent. It is on the Indian Ocean side of the Transantarctic Mountains. It is the coldest, windiest, and driest part of Earth. East Antarctica holds the record as the coldest place on earth.\n", "\t12.650\tEarth Day is a day that is supposed to inspire more awareness and appreciation for the Earth's natural environment. It takes place each year on April 22. It now takes place in more than 193 countries around the world. During Earth Day, the world encourages everyone to turn off all unwanted lights.\n", "\t12.172\tHeinrich events occurred during the coldest point of \"Bond Cycles\" in which many icebergs were discharged into the North Atlantic and melted.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.633\tEast Antarctica, also called Greater Antarctica, is the largest part (two-thirds) of the Antarctic continent. It is on the Indian Ocean side of the Transantarctic Mountains. It is the coldest, windiest, and driest part of Earth. East Antarctica holds the record as the coldest place on earth.\n", "\t0.556\tThe North Pole is the point that is farthest north on Earth. It is the point on which axis of Earth turns. It is in the Arctic Ocean and it is cold there because the sun does not shine there for about half a year and never rises very high. The ocean around the pole is always very cold and it is covered by a thick sheet of ice.\n", "\t0.516\tPlaces with a subarctic climate (also called boreal climate) have long, usually very cold winters, and short, warm summers. It is found on large landmasses, away from oceans, usually at latitudes from 50° to 70°N. Because there are no large landmasses at such latitudes in the Southern Hemisphere, it is only found at high \"altitudes \"(heights) in the Andes and the mountains of Australia and New Zealand's South Island. These climates are in groups \"Dfc\", \"Dwc\", \"Dfd\" and \"Dwd\" in the Köppen climate classification\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t6.129\tEast Antarctica, also called Greater Antarctica, is the largest part (two-thirds) of the Antarctic continent. It is on the Indian Ocean side of the Transantarctic Mountains. It is the coldest, windiest, and driest part of Earth. East Antarctica holds the record as the coldest place on earth.\n", "\t0.594\tThe Arctic is the area around the Earth's North Pole. The Arctic includes parts of Russia, Alaska, Canada, Greenland, Lapland and Svalbard as well as the Arctic Ocean. It is an ocean, mostly covered with ice. Most scientists call the area north of the treeline Arctic. Trees will not grow when the temperatures get too cold. The forests of the continents stop when they get too far north or too high up a mountain. (Higher places are colder, too.) The place where in the trees stop is called the tree line.\n", "\t0.215\tThe North Pole is the point that is farthest north on Earth. It is the point on which axis of Earth turns. It is in the Arctic Ocean and it is cold there because the sun does not shine there for about half a year and never rises very high. The ocean around the pole is always very cold and it is covered by a thick sheet of ice.\n"]}], "source": ["search(query = \"Coldest place earth\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EJ3OqA32ie_s", "outputId": "e28a27b7-e71b-4bbe-a6d6-583bc7e82e4f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: <PERSON><PERSON> year birth\n", "Top-3 lexical search (BM25) hits\n", "\t23.364\tTesla, Inc. is a company based in Palo Alto, California which makes electric cars. It was started in 2003 by <PERSON>, <PERSON>, and <PERSON><PERSON> (who also co-founded PayPal and SpaceX and is the CEO of SpaceX). <PERSON><PERSON><PERSON> no longer works there. Today, <PERSON><PERSON> is the Chief Executive Officer (CEO). It started selling its first car, the Roadster in 2008.\n", "\t19.943\tThe Boring Company is a tunnel boring company founded by <PERSON><PERSON>, who earlier started SpaceX. It aims to reduce traffic congestion in urban areas. It is involved in the building of the Hyperloop in Los Angeles.\n", "\t18.392\t<PERSON><PERSON> (born June 28, 1971) is a businessman and philanthropist. He was born in South Africa. He moved to Canada and later became an American citizen. <PERSON><PERSON> is the current CEO & Chief Product Architect of Tesla Motors, a company that makes electric vehicles. He is also the CEO of Solar City, a company that makes solar panels, and the CEO & CTO of SpaceX, an aerospace company. In August 2020, Bloomberg ranked <PERSON><PERSON> third among the richest people on the planet with net worth to be $115.4 billion.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.574\t<PERSON><PERSON> (born June 28, 1971) is a businessman and philanthropist. He was born in South Africa. He moved to Canada and later became an American citizen. <PERSON><PERSON> is the current CEO & Chief Product Architect of Tesla Motors, a company that makes electric vehicles. He is also the CEO of Solar City, a company that makes solar panels, and the CEO & CTO of SpaceX, an aerospace company. In August 2020, Bloomberg ranked <PERSON><PERSON> third among the richest people on the planet with net worth to be $115.4 billion.\n", "\t0.471\t<PERSON><PERSON><PERSON> was born on June 21, 1893, in Kofu, Yamanashi Prefecture. Her birth name was <PERSON><PERSON>. Her parents were Methodists. She was raised as a Christian. She studied at the Tokyo Eiwa Jogakuin. She began writing children's stories when she was encouraged by translator <PERSON><PERSON><PERSON>. She graduated from school in 1913.\n", "\t0.467\t<PERSON> (born July 29, 1969) is a former Brazilian football player.\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t7.449\t<PERSON><PERSON> (born June 28, 1971) is a businessman and philanthropist. He was born in South Africa. He moved to Canada and later became an American citizen. <PERSON><PERSON> is the current CEO & Chief Product Architect of Tesla Motors, a company that makes electric vehicles. He is also the CEO of Solar City, a company that makes solar panels, and the CEO & CTO of SpaceX, an aerospace company. In August 2020, Bloomberg ranked <PERSON><PERSON> third among the richest people on the planet with net worth to be $115.4 billion.\n", "\t-2.518\t<PERSON><PERSON> (; 28 April 1946 – 11 August 2015) was a Egyptian actor. His birth name is <PERSON><PERSON><PERSON> (Arabic: محمد جابر محمد عبد الله). <PERSON><PERSON><PERSON><PERSON> was born in the working-class neighbourhood of Sayeda Zainab in Cairo (). He was known for his conspiracy theories about the Holocaust and the September 11 attacks.\n", "\t-4.171\t<PERSON><PERSON> (born 14 May 1984) is an English singer. He comes from Witham, Essex. He became famous after he finished second in The X Factor in 2009.\n"]}], "source": ["search(query = \"<PERSON><PERSON> year birth\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "13h8bMHKk4UX", "outputId": "b464adbc-6f01-41b1-f482-3d5a8330b20d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: Paris eiffel tower\n", "Top-3 lexical search (BM25) hits\n", "\t27.300\tThe Eiffel Tower (French: La Tour Eiffel, ], IPA pronunciation: \"EYE-full\" English; \"eh-FEHL\" French) is a landmark in Paris. It was built between 1887 and 1889 for the Exposition Universelle (World Fair). The Tower was the Exposition's main attraction.\n", "\t25.263\tParis is a city in the U.S. state of Texas. It is in Lamar County, Texas. It had a population of 25,171 in 2010. It has been called the \"Second Largest Paris in the World\". It has a replica of the Eiffel Tower.\n", "\t24.059\tParis is a city in the U.S. state of Tennessee. It had a population of 25,171 in 2010. It has been called the \"World's Biggest Fish Fry\". It has a 70-foot replica of the Eiffel Tower.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.812\tThe Eiffel Tower (French: La Tour Eiffel, ], IPA pronunciation: \"EYE-full\" English; \"eh-FEHL\" French) is a landmark in Paris. It was built between 1887 and 1889 for the Exposition Universelle (World Fair). The Tower was the Exposition's main attraction.\n", "\t0.626\t<PERSON> (December 15, 1832 – December 27, 1923; , ) was a French structural engineer and architect. He is known for designing the Eiffel Tower. He also designed the armature (supporting framework) for the Statue of Liberty, New York Harbor, United States.\n", "\t0.538\tThe Élysée Palace (, ) is the official residence of the French president. It is in Paris, in the 8th \"arrondissement\", near the Champs-Élysées. The building is under protection of a unit of the famous Republican Guard. It was built between 1718 and 1722.\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t10.574\tThe Eiffel Tower (French: La Tour Eiffel, ], IPA pronunciation: \"EYE-full\" English; \"eh-FEHL\" French) is a landmark in Paris. It was built between 1887 and 1889 for the Exposition Universelle (World Fair). The Tower was the Exposition's main attraction.\n", "\t4.362\t<PERSON> (December 15, 1832 – December 27, 1923; , ) was a French structural engineer and architect. He is known for designing the Eiffel Tower. He also designed the armature (supporting framework) for the Statue of Liberty, New York Harbor, United States.\n", "\t3.756\tThe current tower is the second tower on the site. The original tower was built in 1912. The design was based on the Eiffel Tower. The tower had an cable car that connected it to a nearby amusement park called Luna Park. The original was 64 meters tall. It was the second tallest building in Asia at that time. It quickly became one of the most popular locations in the city. Visitors came from all over.\n"]}], "source": ["search(query = \"Paris eiffel tower\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hwTRd5Jqw-09", "outputId": "d3c0286c-b703-4f06-e601-3446bdd01aab"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: Which US president was killed?\n", "Top-3 lexical search (BM25) hits\n", "\t10.179\t<PERSON> (August 27, 1908 – January 22, 1973) was a member of the Democratic Party and the 36th president of the United States serving from 1963 to 1969. <PERSON> took over as president when President <PERSON> was killed in November 1963. He was then re-elected in the 1964 election.\n", "\t10.091\t<PERSON><PERSON>, the fourth President of the Republic of Poland, died on 10 April 2010. He died in a plane crash outside of Smolensk, Russia. The plane was a Tu-154 belonging to the Polish Air Force. The crash killed all 96 on board. His wife, <PERSON>, was also among those killed.\n", "\t9.791\t<PERSON><PERSON> (October 9, 1934 – March 2, 1996) was a Dominican politician. He was Vice President of the Dominican Republic during the <PERSON> presidency between 1978 to 1982. He became President of the Dominican Republic after <PERSON><PERSON><PERSON> killed himself in 1982. He was president for a month between July to August 1982.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.686\t<PERSON> was the 35th President of the United States. He was assassinated (murdered) in Dealey Plaza, Dallas, Texas, on Friday, November 22, 1963. This happened while he was traveling in a Presidential motorcade with his wife <PERSON>, the Governor of Texas <PERSON>, and the governor's wife <PERSON><PERSON>.\n", "\t0.656\t<PERSON>, the 25th President of the United States, was assassinated on September 6, 1901, inside the Temple of Music on the grounds of the Pan-American Exposition in Buffalo, New York.\n", "\t0.655\t<PERSON> (November 19, 1831 - September 19, 1881) was the 20th (1881) President of the United States and the 2nd President to be assassinated (killed while in office). President <PERSON> was in office from March to September of 1881. He was in office for a total of six months and fifteen days. For almost half that time he was bedridden as a result of an attempt to kill him. He was shot on July 2 and finally died in September the same year he got into office.\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t9.278\t<PERSON>, the 25th President of the United States, was assassinated on September 6, 1901, inside the Temple of Music on the grounds of the Pan-American Exposition in Buffalo, New York.\n", "\t8.403\t<PERSON> was the 35th President of the United States. He was assassinated (murdered) in Dealey Plaza, Dallas, Texas, on Friday, November 22, 1963. This happened while he was traveling in a Presidential motorcade with his wife <PERSON>, the Governor of Texas <PERSON>, and the governor's wife <PERSON><PERSON>.\n", "\t7.914\tOn December 26, 2006, <PERSON>, the 38th President of the United States, died at his home in Rancho Mirage, California at 6:45 p.m. local time (02:45, December 27, UTC).\n"]}], "source": ["search(query = \"Which US president was killed?\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TFkPfYGXIz0Y", "outputId": "dead42f8-53b5-454a-8b95-bd9a6c838006"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input question: When is Chinese New Year\n", "Top-3 lexical search (BM25) hits\n", "\t18.743\tChinese New Year, known in China as the SpringFestival and in Singapore as the LunarNewYear, is a holiday on and around the new moon on the first day of the year in the traditional Chinese calendar. This calendar is based on the changes in the moon and is only sometimes changed to fit the seasons of the year based on how the Earth moves around the sun. Because of this, Chinese New Year is never on January1. It moves around between January21 and February20.\n", "\t18.527\tNew Year in Japan is one of the most important festivals. Unlike the Chinese New Year, it is held on January 1.\n", "\t15.789\tThe CCTV New Year's Gala (Simplified Chinese: 中国中央电视台春节联欢晚会; Traditional Chinese: 中國中央電視台春節聯歡晚會; Pinyin: \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zhōngyāng diànshìtái chūnjié liánhuān wǎnhuì\") is a Chinese New Year special produced by China Central Television. It was presented by <PERSON>.\n", "\n", "-------------------------\n", "\n", "Top-3 Bi-Encoder Retrieval hits\n", "\t0.782\tChinese New Year, known in China as the SpringFestival and in Singapore as the LunarNewYear, is a holiday on and around the new moon on the first day of the year in the traditional Chinese calendar. This calendar is based on the changes in the moon and is only sometimes changed to fit the seasons of the year based on how the Earth moves around the sun. Because of this, Chinese New Year is never on January1. It moves around between January21 and February20.\n", "\t0.648\tChinese National Day is the national day of China. It may mean:\n", "\t0.642\tNational Day is a yearly holiday in the People's Republic of China. It celebrates the beginning of its new government on October1, 1949. It is one of two Golden Weeks in the country, along with the Chinese New Year.\n", "\n", "-------------------------\n", "\n", "Top-3 Cross-Encoder Re-ranker hits\n", "\t10.896\tChinese New Year, known in China as the SpringFestival and in Singapore as the LunarNewYear, is a holiday on and around the new moon on the first day of the year in the traditional Chinese calendar. This calendar is based on the changes in the moon and is only sometimes changed to fit the seasons of the year based on how the Earth moves around the sun. Because of this, Chinese New Year is never on January1. It moves around between January21 and February20.\n", "\t6.147\tNew Year in Japan is one of the most important festivals. Unlike the Chinese New Year, it is held on January 1.\n", "\t5.081\tNational Day is a yearly holiday in the People's Republic of China. It celebrates the beginning of its new government on October1, 1949. It is one of two Golden Weeks in the country, along with the Chinese New Year.\n"]}], "source": ["search(query=\"When is Chinese New Year\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "soZ1nH4_I4Zi"}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"collapsed_sections": [], "name": "retrieve_rerank_simple_wikipedia.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"0bee2cf221bf4497b527369b3905553e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "10c12f39a3b340a98f6db972e9d37921": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "11c91ed9d2f049c9aec2124c6c926f88": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a39d7b521ea540cd8af250584340e427", "IPY_MODEL_7ea6f545038f4422850e4a2d75345e65"], "layout": "IPY_MODEL_0bee2cf221bf4497b527369b3905553e"}}, "1390bf4686774d6581348f87f908a400": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "100%", "description_tooltip": null, "layout": "IPY_MODEL_64eb0085f9a84129937350004868ad54", "max": 50223724, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e6f53f85bbbb4e6caf992e913d9cce88", "value": 50223724}}, "1655064d0c164b778220e9a4da96d2ea": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "16d9436e5caf448a883bbdac3dc930dd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1c198b0a475f42fe836c19813f838100": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4fd3399b50224953806f5a646d1ffd89", "IPY_MODEL_fe98ad30ffaa4bcfa09c5c01accdd9b8"], "layout": "IPY_MODEL_1655064d0c164b778220e9a4da96d2ea"}}, "258161ce772d470fbc004f7736cea24e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "27d20db7a7f346ea81e3abe88868cfcd": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "297a74178198439ab2847d90f2ee56e0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "33b91b75207249a7b885c317c95503dd": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_362ab62385884739ba1de446dddf1b87", "placeholder": "​", "style": "IPY_MODEL_614511ff768642299c1b9c19abfb56a6", "value": " 612/612 [00:00&lt;00:00, 641B/s]"}}, "33bd233b64664e639dea85c60f0372dd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "362ab62385884739ba1de446dddf1b87": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "399bf19c69ed4a1490c6609284bacf27": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "100%", "description_tooltip": null, "layout": "IPY_MODEL_e5595b23a62643aab844c875127a0a0a", "max": 509663, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_79e0e838f33f4c51b24bdfd12c7d0a00", "value": 509663}}, "3b01947b6c50476db6c9bcb21b03491b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3fd97a1c9ef74e80b999aba18dd20075": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "442d3ee2958b4db999c5e1624986d201": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "100%", "description_tooltip": null, "layout": "IPY_MODEL_c119469e196f4ceca7b1368f2a38d18b", "max": 782843128, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ec1353a6b4e8450f84b4302a50e2fba6", "value": 782843128}}, "44d63b5526824e0f81d62dfba320bdc1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4c308b4ed1644ae790c878e190642c68": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4d2cd27435f44a4f8bdbdd3e17c8c86a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4fd3399b50224953806f5a646d1ffd89": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "Downloading: 100%", "description_tooltip": null, "layout": "IPY_MODEL_88a71f6953af4259accf096fda7356b1", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cf475534803f42718e6a87e645585357", "value": 231508}}, "55fd02cec9a74a92a7c0bf4cf713bb28": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "566d714778c040908ce443cc51c926bb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "581eef89f62245a0aaae36179e688843": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d1b0a2aa5da48f3ad8e428d7b4ead27": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "614511ff768642299c1b9c19abfb56a6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "64eb0085f9a84129937350004868ad54": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "67346148e616426c83f10f203be5da8d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6bceb6fffeb84566a07aeb340d7c920f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "703fe20c0249435eb847fe29a5a23c52": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1390bf4686774d6581348f87f908a400", "IPY_MODEL_f3ba3908cf1d4000b0da73107cee1526"], "layout": "IPY_MODEL_6bceb6fffeb84566a07aeb340d7c920f"}}, "79e0e838f33f4c51b24bdfd12c7d0a00": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "7d0105cc6432438581e71fad681ae2d8": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "7d4f6eb85c0f4b06bf332c278da7f225": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7e15806e1ab74d6893f9c5f4ede9b5af": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "100%", "description_tooltip": null, "layout": "IPY_MODEL_4d2cd27435f44a4f8bdbdd3e17c8c86a", "max": 244719814, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_89d89e63246f4e1d80b8a2a05db28a19", "value": 244719814}}, "7ea6f545038f4422850e4a2d75345e65": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_581eef89f62245a0aaae36179e688843", "placeholder": "​", "style": "IPY_MODEL_3fd97a1c9ef74e80b999aba18dd20075", "value": " 112/112 [00:00&lt;00:00, 747B/s]"}}, "85a56dd0bd8549e790759f3f4ad0df7f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "877f416c38944e3db52917a6497b3525": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "Downloading: 100%", "description_tooltip": null, "layout": "IPY_MODEL_33bd233b64664e639dea85c60f0372dd", "max": 541, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e120277c4a7141fca20e0115486a0be1", "value": 541}}, "88a71f6953af4259accf096fda7356b1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "89533b9769bd4fe5be0068b381a7b554": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7e15806e1ab74d6893f9c5f4ede9b5af", "IPY_MODEL_f9a01234d31e48cbb2b02f1b34acbbd1"], "layout": "IPY_MODEL_95bcab1f8336422aafa453f8412d9eed"}}, "89d89e63246f4e1d80b8a2a05db28a19": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "938510ed19db4fcbadbe730584ca5e58": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "Downloading: 100%", "description_tooltip": null, "layout": "IPY_MODEL_16d9436e5caf448a883bbdac3dc930dd", "max": 612, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7d0105cc6432438581e71fad681ae2d8", "value": 612}}, "95bcab1f8336422aafa453f8412d9eed": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9c35672b3e224a34b7070099c5067426": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_55fd02cec9a74a92a7c0bf4cf713bb28", "placeholder": "​", "style": "IPY_MODEL_4c308b4ed1644ae790c878e190642c68", "value": " 541/541 [00:00&lt;00:00, 8.37kB/s]"}}, "a39d7b521ea540cd8af250584340e427": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "Downloading: 100%", "description_tooltip": null, "layout": "IPY_MODEL_297a74178198439ab2847d90f2ee56e0", "max": 112, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5d1b0a2aa5da48f3ad8e428d7b4ead27", "value": 112}}, "a6c3c35f17394bc4a66d460f91cd6833": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ab19ef7e9763402589b15b4ccd1b5d6b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_399bf19c69ed4a1490c6609284bacf27", "IPY_MODEL_c25ffe4acbac42ca9755248338ed864b"], "layout": "IPY_MODEL_e5672d2910174d87be7434aa7bbe78c0"}}, "aec7bd589fb04ecb999ef9a60dd60afa": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_10c12f39a3b340a98f6db972e9d37921", "placeholder": "​", "style": "IPY_MODEL_f10d1c0166f941bcba75f9470db86651", "value": " 268M/268M [00:07&lt;00:00, 36.9MB/s]"}}, "bbfae35891b7490f8da4a60627fa5a16": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "be0fac5d7a974f47ac5b8a5e6ed9e4d6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf2f3698d10f47fdb65dc82e54a9e566": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf43844c257c4ee99d5f2cf181179c81": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "Downloading: 100%", "description_tooltip": null, "layout": "IPY_MODEL_a6c3c35f17394bc4a66d460f91cd6833", "max": 267871721, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fc984d27599d41c2be86c13dcc66a171", "value": 267871721}}, "c119469e196f4ceca7b1368f2a38d18b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c25ffe4acbac42ca9755248338ed864b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7d4f6eb85c0f4b06bf332c278da7f225", "placeholder": "​", "style": "IPY_MODEL_dee30a855454476aaa572af57e6b7405", "value": " 509663/509663 [00:21&lt;00:00, 23222.74it/s]"}}, "cc09077698c2445c8c7a7be99a5eb931": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_566d714778c040908ce443cc51c926bb", "placeholder": "​", "style": "IPY_MODEL_67346148e616426c83f10f203be5da8d", "value": " 783M/783M [00:44&lt;00:00, 17.6MB/s]"}}, "cf475534803f42718e6a87e645585357": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "dd4f3ca182c54678887ff51f75877997": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bf43844c257c4ee99d5f2cf181179c81", "IPY_MODEL_aec7bd589fb04ecb999ef9a60dd60afa"], "layout": "IPY_MODEL_bbfae35891b7490f8da4a60627fa5a16"}}, "dee30a855454476aaa572af57e6b7405": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e00bfbac209b4caa92ac3c679381a5a5": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_442d3ee2958b4db999c5e1624986d201", "IPY_MODEL_cc09077698c2445c8c7a7be99a5eb931"], "layout": "IPY_MODEL_44d63b5526824e0f81d62dfba320bdc1"}}, "e120277c4a7141fca20e0115486a0be1": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "e45327599f404ae09e5452978695d4e8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_877f416c38944e3db52917a6497b3525", "IPY_MODEL_9c35672b3e224a34b7070099c5067426"], "layout": "IPY_MODEL_258161ce772d470fbc004f7736cea24e"}}, "e47e734b2bbc42d1b9a32e1b1dca6451": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e5595b23a62643aab844c875127a0a0a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e5672d2910174d87be7434aa7bbe78c0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e6f53f85bbbb4e6caf992e913d9cce88": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "ec1353a6b4e8450f84b4302a50e2fba6": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "f0dca993eee34ed8b9c0a9ead6cbbf68": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f10d1c0166f941bcba75f9470db86651": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f3ba3908cf1d4000b0da73107cee1526": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bf2f3698d10f47fdb65dc82e54a9e566", "placeholder": "​", "style": "IPY_MODEL_3b01947b6c50476db6c9bcb21b03491b", "value": " 50.2M/50.2M [00:36&lt;00:00, 1.38MB/s]"}}, "f9a01234d31e48cbb2b02f1b34acbbd1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e47e734b2bbc42d1b9a32e1b1dca6451", "placeholder": "​", "style": "IPY_MODEL_27d20db7a7f346ea81e3abe88868cfcd", "value": " 245M/245M [00:13&lt;00:00, 18.7MB/s]"}}, "fae4387adeac4391ba73c21643e6034f": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_938510ed19db4fcbadbe730584ca5e58", "IPY_MODEL_33b91b75207249a7b885c317c95503dd"], "layout": "IPY_MODEL_be0fac5d7a974f47ac5b8a5e6ed9e4d6"}}, "fc984d27599d41c2be86c13dcc66a171": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": "initial"}}, "fe98ad30ffaa4bcfa09c5c01accdd9b8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_85a56dd0bd8549e790759f3f4ad0df7f", "placeholder": "​", "style": "IPY_MODEL_f0dca993eee34ed8b9c0a9ead6cbbf68", "value": " 232k/232k [00:00&lt;00:00, 845kB/s]"}}}}}, "nbformat": 4, "nbformat_minor": 1}