import pandas as pd
import numpy as np
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from sklearn.feature_extraction.text import TfidfVectorizer
import re
import json

def load_voyager_bom_data():
    """Load Voyager BOM data"""
    try:
        df = pd.read_excel("output/voyager_5408509_bom_pcm_aggregated.xlsx")
        print(f"Loaded enhanced BOM data: {len(df)} rows")
        
        column_mapping = {
            'F_E_Line': 'F-E-Line',
            'Item_Number': 'Item Number',
            'Item_Description': 'Item Description'
        }
        df = df.rename(columns=column_mapping)
        return df
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def remove_accessories_and_non_physical_items(df):
    """Remove non-physical items"""
    exclusion_patterns = [
        r'manual|documentation|doc|guide|instruction',
        r'software|firmware|program|application|app',
        r'paper|label|sticker|decal|marking',
        r'accessory|accessories|optional|spare',
        r'packaging|box|container|bag|wrap',
        r'cable tie|zip tie|fastener|screw|bolt|nut',
        r'warranty|service|maintenance|support',
        r'training|education|course|tutorial',
        r'license|subscription|agreement|contract'
    ]
    
    combined_pattern = '|'.join(exclusion_patterns)
    initial_count = len(df)
    
    mask = ~df['Item Description'].str.contains(combined_pattern, case=False, na=False)
    df_filtered = df[mask].copy()
    
    removed_count = initial_count - len(df_filtered)
    removed_items = df[~df.index.isin(df_filtered.index)]
    
    print(f"Removed {removed_count} non-physical items")
    print(f"Remaining items for clustering: {len(df_filtered)}")
    
    return df_filtered, removed_items

def quick_clustering_analysis(df_clean):
    """Perform quick clustering analysis with reduced complexity"""
    
    results = {}
    
    # Sample data for faster processing
    if len(df_clean) > 5000:
        df_sample = df_clean.sample(n=5000, random_state=42)
        print(f"Sampling {len(df_sample)} items for quick analysis")
    else:
        df_sample = df_clean
    
    # Function-based clustering
    print("FUNCTION CLUSTERING")
    functional_keywords = []
    for desc in df_sample['Item Description']:
        words = re.findall(r'\b(?:pump|valve|sensor|motor|drive|control|filter|heat|cool|measure|detect|connect|support|mount|seal|protect)\b', 
                          desc.lower())
        functional_keywords.append(' '.join(words) if words else desc.lower())
    
    vectorizer_func = TfidfVectorizer(max_features=500, stop_words='english', ngram_range=(1, 2))
    func_embeddings = vectorizer_func.fit_transform(functional_keywords).toarray()
    
    func_results = {}
    for n_clusters in [5, 8, 10]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(func_embeddings)
        
        silhouette = silhouette_score(func_embeddings, labels)
        calinski = calinski_harabasz_score(func_embeddings, labels)
        davies_bouldin = davies_bouldin_score(func_embeddings, labels)
        
        func_results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    results['function'] = func_results
    
    # Structure-based clustering
    print("STRUCTURE CLUSTERING")
    structural_features = []
    for idx, row in df_sample.iterrows():
        features = []
        features.append(f"level_{row['Level']}")
        
        if 'F-E-Line' in row and pd.notna(row['F-E-Line']):
            fe_parts = str(row['F-E-Line']).split('-')
            for i, part in enumerate(fe_parts):
                features.append(f"fe_{i}_{part}")
        
        desc = str(row['Item Description']).lower()
        structural_terms = re.findall(r'\b(?:assembly|sub|component|part|module|unit|system|housing|frame|bracket|plate|panel)\b', desc)
        features.extend(structural_terms)
        
        structural_features.append(' '.join(features))
    
    vectorizer_struct = TfidfVectorizer(max_features=500, stop_words='english')
    struct_embeddings = vectorizer_struct.fit_transform(structural_features).toarray()
    
    struct_results = {}
    for n_clusters in [5, 8, 10]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(struct_embeddings)
        
        silhouette = silhouette_score(struct_embeddings, labels)
        calinski = calinski_harabasz_score(struct_embeddings, labels)
        davies_bouldin = davies_bouldin_score(struct_embeddings, labels)
        
        struct_results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    results['structure'] = struct_results
    
    # Material-based clustering
    print("MATERIAL CLUSTERING")
    material_features = []
    for desc in df_sample['Item Description']:
        desc_lower = str(desc).lower()
        materials = re.findall(r'\b(?:steel|aluminum|plastic|rubber|ceramic|glass|carbon|titanium|brass|copper|bronze|iron)\b', desc_lower)
        properties = re.findall(r'\b(?:stainless|galvanized|anodized|coated|treated|hardened|tempered|reinforced)\b', desc_lower)
        
        features = materials + properties
        material_features.append(' '.join(features) if features else 'standard_material')
    
    vectorizer_mat = TfidfVectorizer(max_features=300, stop_words='english')
    mat_embeddings = vectorizer_mat.fit_transform(material_features).toarray()
    
    mat_results = {}
    for n_clusters in [4, 6, 8]:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(mat_embeddings)
        
        silhouette = silhouette_score(mat_embeddings, labels)
        calinski = calinski_harabasz_score(mat_embeddings, labels)
        davies_bouldin = davies_bouldin_score(mat_embeddings, labels)
        
        mat_results[f'kmeans_{n_clusters}'] = {
            'labels': labels,
            'silhouette_score': silhouette,
            'calinski_harabasz_score': calinski,
            'davies_bouldin_score': davies_bouldin,
            'n_clusters': n_clusters
        }
    
    results['material'] = mat_results
    
    return results, df_sample

def find_best_models(results):
    """Find best performing model for each approach"""
    best_models = {}
    
    for approach, approach_results in results.items():
        best_score = -1
        best_model = None
        best_metrics = None
        
        for model_name, metrics in approach_results.items():
            if metrics['silhouette_score'] > best_score:
                best_score = metrics['silhouette_score']
                best_model = model_name
                best_metrics = metrics
        
        best_models[approach] = {
            'model': best_model,
            'metrics': best_metrics
        }
    
    return best_models

def save_quick_results(df_sample, removed_items, results, best_models):
    """Save quick clustering results"""
    
    with pd.ExcelWriter('output/voyager_bom_quick_clustering_results.xlsx', engine='openpyxl') as writer:
        
        # Save sample data used
        df_sample.to_excel(writer, sheet_name='Sample_Data', index=False)
        
        # Save removed items summary
        if len(removed_items) > 0:
            removed_summary = pd.DataFrame({
                'Category': ['Total Removed', 'Documentation', 'Software', 'Labels/Papers', 'Accessories', 'Packaging', 'Fasteners'],
                'Count': [
                    len(removed_items),
                    removed_items['Item Description'].str.contains('manual|documentation|doc|guide', case=False, na=False).sum(),
                    removed_items['Item Description'].str.contains('software|firmware|program', case=False, na=False).sum(),
                    removed_items['Item Description'].str.contains('paper|label|sticker', case=False, na=False).sum(),
                    removed_items['Item Description'].str.contains('accessory|accessories|optional', case=False, na=False).sum(),
                    removed_items['Item Description'].str.contains('packaging|box|container', case=False, na=False).sum(),
                    removed_items['Item Description'].str.contains('cable tie|zip tie|fastener|screw|bolt|nut', case=False, na=False).sum()
                ]
            })
            removed_summary.to_excel(writer, sheet_name='Removed_Items_Summary', index=False)
        
        # Save clustering results summary
        summary_data = []
        for approach, approach_results in results.items():
            for model_name, metrics in approach_results.items():
                summary_data.append({
                    'Approach': approach,
                    'Model': model_name,
                    'N_Clusters': metrics['n_clusters'],
                    'Silhouette_Score': metrics['silhouette_score'],
                    'Calinski_Harabasz_Score': metrics['calinski_harabasz_score'],
                    'Davies_Bouldin_Score': metrics['davies_bouldin_score']
                })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values(['Approach', 'Silhouette_Score'], ascending=[True, False])
        summary_df.to_excel(writer, sheet_name='Clustering_Results', index=False)
        
        # Save best models
        best_summary = []
        for approach, model_info in best_models.items():
            if model_info['metrics']:
                best_summary.append({
                    'Approach': approach,
                    'Best_Model': model_info['model'],
                    'N_Clusters': model_info['metrics']['n_clusters'],
                    'Silhouette_Score': model_info['metrics']['silhouette_score'],
                    'Calinski_Harabasz_Score': model_info['metrics']['calinski_harabasz_score'],
                    'Davies_Bouldin_Score': model_info['metrics']['davies_bouldin_score']
                })
        
        best_df = pd.DataFrame(best_summary)
        best_df = best_df.sort_values('Silhouette_Score', ascending=False)
        best_df.to_excel(writer, sheet_name='Best_Models', index=False)
        
        # Save cluster assignments for best models
        for approach, model_info in best_models.items():
            if model_info['metrics']:
                best_model_name = model_info['model']
                best_labels = results[approach][best_model_name]['labels']
                
                cluster_df = df_sample.copy()
                cluster_df[f'{approach}_cluster'] = best_labels
                cluster_df.to_excel(writer, sheet_name=f'{approach.title()}_Clusters', index=False)

def create_quick_report(df_clean, removed_items, results, best_models):
    """Create quick analysis report"""
    
    report_lines = []
    
    report_lines.append("VOYAGER BOM QUICK CLUSTERING ANALYSIS REPORT")
    report_lines.append("=" * 60)
    report_lines.append("")
    
    report_lines.append("DATA SUMMARY")
    report_lines.append("-" * 20)
    report_lines.append(f"Original BOM items: {len(df_clean) + len(removed_items)}")
    report_lines.append(f"Physical items for analysis: {len(df_clean)}")
    report_lines.append(f"Non-physical items removed: {len(removed_items)}")
    report_lines.append("")
    
    report_lines.append("BEST PERFORMING MODELS")
    report_lines.append("-" * 30)
    
    for approach, model_info in best_models.items():
        if model_info['metrics']:
            metrics = model_info['metrics']
            report_lines.append(f"{approach.upper()}:")
            report_lines.append(f"  Model: {model_info['model']}")
            report_lines.append(f"  Clusters: {metrics['n_clusters']}")
            report_lines.append(f"  Silhouette Score: {metrics['silhouette_score']:.4f}")
            report_lines.append(f"  Calinski-Harabasz Score: {metrics['calinski_harabasz_score']:.4f}")
            report_lines.append(f"  Davies-Bouldin Score: {metrics['davies_bouldin_score']:.4f}")
            report_lines.append("")
    
    report_lines.append("RECOMMENDATIONS")
    report_lines.append("-" * 20)
    
    # Find overall best approach
    best_overall = max(best_models.items(), 
                      key=lambda x: x[1]['metrics']['silhouette_score'] if x[1]['metrics'] else -1)
    
    report_lines.append(f"Best overall clustering approach: {best_overall[0].upper()}")
    report_lines.append(f"Recommended number of clusters: {best_overall[1]['metrics']['n_clusters']}")
    report_lines.append("")
    
    report_lines.append("NEXT STEPS")
    report_lines.append("-" * 15)
    report_lines.append("1. Review cluster assignments in Excel output")
    report_lines.append("2. Validate business relevance of clusters")
    report_lines.append("3. Consider running full analysis on complete dataset")
    report_lines.append("4. Integrate with supply chain and pricing data")
    
    with open('output/voyager_bom_quick_clustering_report.txt', 'w') as f:
        f.write('\n'.join(report_lines))

def main():
    """Main execution function"""
    
    print("LOADING VOYAGER BOM DATA")
    df = load_voyager_bom_data()
    
    if df is None:
        print("Failed to load data")
        return
    
    print("REMOVING NON-PHYSICAL ITEMS")
    df_clean, removed_items = remove_accessories_and_non_physical_items(df)
    
    print("PERFORMING QUICK CLUSTERING ANALYSIS")
    results, df_sample = quick_clustering_analysis(df_clean)
    
    print("FINDING BEST MODELS")
    best_models = find_best_models(results)
    
    print("SAVING RESULTS")
    save_quick_results(df_sample, removed_items, results, best_models)
    
    print("CREATING REPORT")
    create_quick_report(df_clean, removed_items, results, best_models)
    
    print("QUICK ANALYSIS COMPLETED")
    print("Files generated:")
    print("- output/voyager_bom_quick_clustering_results.xlsx")
    print("- output/voyager_bom_quick_clustering_report.txt")
    
    return df_clean, removed_items, results, best_models

if __name__ == "__main__":
    df_clean, removed_items, results, best_models = main()
