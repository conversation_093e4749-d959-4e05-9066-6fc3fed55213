#!/usr/bin/env python3
"""
Data Quality Check for BOM Analysis
Verifies the accuracy and quality of the similarity analysis results.
"""

import os
import pandas as pd
import pickle
from collections import Counter

def load_and_inspect_data():
    """Load and inspect the cached BOM data and similarity results"""
    print("🔍 DATA QUALITY INSPECTION")
    print("=" * 50)
    
    # Load cached BOM data
    cache_folder = 'cache'
    cache_data_file = os.path.join(cache_folder, 'bom_data.pkl')
    
    if not os.path.exists(cache_data_file):
        print("❌ No cached BOM data found")
        return None, None
    
    try:
        with open(cache_data_file, 'rb') as f:
            bom_data = pickle.load(f)
        print(f"✅ Loaded cached BOM data: {len(bom_data)} files")
        
        # Load similarity results
        output_folder = 'output'
        similarity_file = os.path.join(output_folder, 'fast_bom_similarity_analysis.xlsx')
        
        if os.path.exists(similarity_file):
            try:
                similarity_df = pd.read_excel(similarity_file, sheet_name='Similarities', engine='openpyxl')
                print(f"✅ Loaded similarity results: {len(similarity_df)} pairs")
                return bom_data, similarity_df
            except Exception as e:
                print(f"⚠️  Error reading Excel file: {e}")
                print("Trying to read with different engine...")
                try:
                    similarity_df = pd.read_excel(similarity_file, engine='openpyxl')
                    print(f"✅ Loaded similarity results: {len(similarity_df)} pairs")
                    return bom_data, similarity_df
                except Exception as e2:
                    print(f"❌ Could not read Excel file: {e2}")
                    return bom_data, None
        else:
            print("❌ No similarity results found")
            return bom_data, None
            
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None, None

def inspect_bom_data(bom_data):
    """Inspect the structure and quality of BOM data"""
    print("\n📊 BOM DATA INSPECTION")
    print("=" * 30)
    
    total_items = 0
    
    for i, (file_name, df) in enumerate(bom_data.items(), 1):
        print(f"\n[{i}] File: {file_name[:50]}...")
        print(f"    Rows: {len(df)}")
        print(f"    Columns: {list(df.columns)}")
        
        # Check Item ID patterns
        item_ids = df['Item ID'].astype(str)
        unique_item_ids = item_ids.nunique()
        print(f"    Unique Item IDs: {unique_item_ids}")
        
        # Show sample Item IDs
        sample_ids = item_ids.head(10).tolist()
        print(f"    Sample Item IDs: {sample_ids}")
        
        # Check for suspicious patterns
        numeric_only_ids = item_ids[item_ids.str.match(r'^\d+$')].nunique()
        if numeric_only_ids > 0:
            print(f"    ⚠️  Numeric-only Item IDs: {numeric_only_ids}")
        
        # Check levels
        levels = df['Level'].value_counts().head(5)
        print(f"    Level distribution: {dict(levels)}")
        
        # Check descriptions
        descriptions = df['Item Description'].astype(str)
        avg_desc_length = descriptions.str.len().mean()
        print(f"    Avg description length: {avg_desc_length:.1f} chars")
        
        total_items += len(df)
        
        if i >= 3:  # Show first 3 files in detail
            print(f"    ... (showing first 3 files in detail)")
            break
    
    print(f"\n📊 SUMMARY:")
    print(f"    Total files: {len(bom_data)}")
    print(f"    Total items: {total_items:,}")

def inspect_similarity_results(similarity_df):
    """Inspect the similarity analysis results"""
    print("\n🔍 SIMILARITY RESULTS INSPECTION")
    print("=" * 40)
    
    if similarity_df is None:
        print("❌ No similarity data to inspect")
        return
    
    print(f"Total similarity pairs: {len(similarity_df):,}")
    
    # Check Item ID patterns in results
    print("\n📊 Item ID Analysis:")
    all_item_ids = list(similarity_df['Item_ID_1']) + list(similarity_df['Item_ID_2'])
    item_id_counter = Counter(all_item_ids)
    
    print(f"Unique Item IDs in results: {len(set(all_item_ids))}")
    print(f"Most frequent Item IDs:")
    for item_id, count in item_id_counter.most_common(10):
        print(f"  '{item_id}': appears {count:,} times")
    
    # Check for suspicious patterns
    numeric_only = [item_id for item_id in set(all_item_ids) if str(item_id).isdigit()]
    if numeric_only:
        print(f"\n⚠️  Suspicious numeric-only Item IDs found: {len(numeric_only)}")
        print(f"    Examples: {numeric_only[:10]}")
    
    # Check similarity score distribution
    print(f"\n📊 Similarity Score Distribution:")
    score_ranges = [
        (0.95, 1.0, "Near-perfect (95-100%)"),
        (0.9, 0.95, "Very high (90-95%)"),
        (0.8, 0.9, "High (80-90%)"),
        (0.7, 0.8, "Medium (70-80%)"),
        (0.6, 0.7, "Low (60-70%)")
    ]
    
    for min_score, max_score, label in score_ranges:
        count = len(similarity_df[(similarity_df['Similarity_Score'] >= min_score) & 
                                 (similarity_df['Similarity_Score'] < max_score)])
        percentage = (count / len(similarity_df)) * 100
        print(f"  {label}: {count:,} pairs ({percentage:.1f}%)")
    
    # Check for exact duplicates
    exact_matches = similarity_df[similarity_df['Similarity_Score'] >= 0.999]
    print(f"\n🎯 Exact matches (≥99.9%): {len(exact_matches):,}")
    
    if len(exact_matches) > 0:
        print("Sample exact matches:")
        for i, (_, row) in enumerate(exact_matches.head(5).iterrows()):
            print(f"  {i+1}. '{row['Item_ID_1']}' ↔ '{row['Item_ID_2']}' ({row['Similarity_Score']:.3f})")
            print(f"     Desc1: '{row['Description_1'][:50]}...'")
            print(f"     Desc2: '{row['Description_2'][:50]}...'")
    
    # Check cross-file vs same-file
    cross_file_count = similarity_df['Cross_File_Match'].sum()
    same_file_count = len(similarity_df) - cross_file_count
    print(f"\n📁 File Distribution:")
    print(f"  Cross-file matches: {cross_file_count:,} ({(cross_file_count/len(similarity_df)*100):.1f}%)")
    print(f"  Same-file matches: {same_file_count:,} ({(same_file_count/len(similarity_df)*100):.1f}%)")

def check_data_consistency(bom_data, similarity_df):
    """Check for consistency issues between BOM data and similarity results"""
    print("\n🔧 DATA CONSISTENCY CHECK")
    print("=" * 30)
    
    if similarity_df is None:
        print("❌ No similarity data for consistency check")
        return
    
    # Get all Item IDs from BOM data
    all_bom_item_ids = set()
    for file_name, df in bom_data.items():
        file_item_ids = set(df['Item ID'].astype(str))
        all_bom_item_ids.update(file_item_ids)
    
    print(f"Total unique Item IDs in BOM data: {len(all_bom_item_ids):,}")
    
    # Get all Item IDs from similarity results
    similarity_item_ids = set(similarity_df['Item_ID_1']) | set(similarity_df['Item_ID_2'])
    print(f"Total unique Item IDs in similarity results: {len(similarity_item_ids):,}")
    
    # Check overlap
    overlap = all_bom_item_ids & similarity_item_ids
    print(f"Overlapping Item IDs: {len(overlap):,}")
    
    # Check for Item IDs in similarity that aren't in BOM data
    missing_in_bom = similarity_item_ids - all_bom_item_ids
    if missing_in_bom:
        print(f"⚠️  Item IDs in similarity results but not in BOM data: {len(missing_in_bom)}")
        print(f"    Examples: {list(missing_in_bom)[:10]}")
    
    # Sample some BOM Item IDs to see what they look like
    print(f"\nSample BOM Item IDs:")
    sample_bom_ids = list(all_bom_item_ids)[:20]
    for i, item_id in enumerate(sample_bom_ids):
        print(f"  {i+1}. '{item_id}'")

def main():
    print("🚀 Starting Data Quality Check")
    print("=" * 50)
    
    # Load data
    bom_data, similarity_df = load_and_inspect_data()
    
    if bom_data is None:
        return
    
    # Inspect BOM data structure
    inspect_bom_data(bom_data)
    
    # Inspect similarity results
    inspect_similarity_results(similarity_df)
    
    # Check consistency
    check_data_consistency(bom_data, similarity_df)
    
    print(f"\n🎉 Data Quality Check Complete!")
    print("=" * 50)
    
    # Provide recommendations
    print("\n💡 RECOMMENDATIONS:")
    if similarity_df is not None:
        total_pairs = len(similarity_df)
        if total_pairs > 1000000:
            print("  ⚠️  Very high number of similarity pairs detected")
            print("     Consider increasing similarity threshold or checking for data issues")
        
        # Check if most Item IDs are single digits
        all_item_ids = list(similarity_df['Item_ID_1']) + list(similarity_df['Item_ID_2'])
        single_digit_count = sum(1 for item_id in all_item_ids if str(item_id).isdigit() and len(str(item_id)) == 1)
        if single_digit_count > len(all_item_ids) * 0.5:
            print("  ⚠️  Many single-digit Item IDs detected")
            print("     This suggests possible data parsing issues")
            print("     Check if Level column is being confused with Item ID column")

if __name__ == "__main__":
    main()
