from bs4 import BeautifulSoup
from ppt import Presentation

html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GE Healthcare BOM Clustering Solution</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f4c75 0%, #1e3a8a 100%);
            color: #ffffff;
            overflow: hidden;
        }

        .presentation-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .slide {
            display: none;
            height: 100vh;
            padding: 60px;
            position: relative;
            background: linear-gradient(135deg, #0f4c75 0%, #1e3a8a 100%);
        }

        .slide.active {
            display: flex;
            flex-direction: column;
        }

        .slide-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #00d4ff;
        }

        .ge-logo {
            font-size: 32px;
            font-weight: bold;
            color: #00d4ff;
            letter-spacing: 2px;
        }

        .slide-number {
            color: #94a3b8;
            font-size: 16px;
        }

        .slide-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        h1 {
            font-size: 48px;
            font-weight: 300;
            margin-bottom: 20px;
            color: #ffffff;
            line-height: 1.2;
        }

        h2 {
            font-size: 36px;
            font-weight: 300;
            margin-bottom: 30px;
            color: #00d4ff;
            line-height: 1.2;
        }

        h3 {
            font-size: 28px;
            font-weight: 400;
            margin-bottom: 20px;
            color: #ffffff;
        }

        .subtitle {
            font-size: 24px;
            color: #94a3b8;
            margin-bottom: 40px;
            font-weight: 300;
        }

        .key-stat {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid #00d4ff;
            border-radius: 8px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }

        .key-stat h3 {
            font-size: 48px;
            color: #00d4ff;
            margin-bottom: 10px;
        }

        .key-stat p {
            font-size: 18px;
            color: #e2e8f0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 40px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-card h4 {
            font-size: 36px;
            color: #00d4ff;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .stat-card p {
            font-size: 16px;
            color: #e2e8f0;
            margin-bottom: 5px;
        }

        .benchmark {
            font-size: 14px;
            color: #94a3b8;
        }

        .algorithm-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin: 40px 0;
        }

        .algorithm-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .algorithm-card h4 {
            color: #00d4ff;
            font-size: 24px;
            margin-bottom: 15px;
        }

        .algorithm-card .method {
            color: #94a3b8;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
            font-weight: 600;
        }

        .bullet-points {
            list-style: none;
            margin: 30px 0;
        }

        .bullet-points li {
            margin: 15px 0;
            padding-left: 30px;
            position: relative;
            font-size: 18px;
            line-height: 1.6;
        }

        .bullet-points li::before {
            content: "→";
            position: absolute;
            left: 0;
            color: #00d4ff;
            font-weight: bold;
        }

        .impact-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
            margin: 40px 0;
        }

        .impact-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .impact-level {
            color: #00d4ff;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 10px;
        }

        .savings {
            font-size: 32px;
            color: #10b981;
            font-weight: 300;
            margin: 10px 0;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid #00d4ff;
            color: #ffffff;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-btn:hover {
            background: rgba(0, 212, 255, 0.4);
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            overflow: hidden;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
        }

        .table th {
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            font-weight: 600;
        }

        .performance-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .high { background: rgba(16, 185, 129, 0.2); color: #10b981; }
        .medium { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }

        .timeline {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            position: relative;
        }

        .timeline::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(to right, #00d4ff, #10b981);
        }

        .timeline-item {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid #00d4ff;
            border-radius: 12px;
            padding: 20px;
            width: 280px;
            text-align: center;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .timeline-item h4 {
            color: #00d4ff;
            margin-bottom: 10px;
        }

        .roi-highlight {
            color: #10b981;
            font-size: 18px;
            font-weight: 600;
        }

        .slide-footer {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(0, 212, 255, 0.3);
            text-align: center;
            color: #94a3b8;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- Slide 1: Title -->
        <div class="slide active">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">1 / 12</div>
            </div>
            <div class="slide-content">
                <h1>Advanced BOM Clustering Solution</h1>
                <div class="subtitle">Transforming Product Development Through Intelligent Part Family Detection</div>
                
                <div class="key-stat">
                    <h3>$2-5M</h3>
                    <p>Annual savings potential identified from first analysis</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h4>97,690</h4>
                        <p>Components Analyzed</p>
                    </div>
                    <div class="stat-card">
                        <h4>44.2%</h4>
                        <p>Cross-Product Standardization</p>
                        <div class="benchmark">vs 30% industry standard</div>
                    </div>
                    <div class="stat-card">
                        <h4>140</h4>
                        <p>Product Families Identified</p>
                    </div>
                </div>
            </div>
            <div class="slide-footer">
                Confidential - GE HealthCare Internal Use Only
            </div>
        </div>

        <!-- Slide 2: Problem Statement -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">2 / 12</div>
            </div>
            <div class="slide-content">
                <h2>The Hidden Cost of Fragmented BOMs</h2>
                
                <ul class="bullet-points">
                    <li><strong>Missed Standardization Opportunities:</strong> Similar components across product lines treated as unique parts, eliminating volume advantages</li>
                    <li><strong>Inefficient Procurement:</strong> Multiple suppliers for functionally equivalent parts, reducing negotiating power</li>
                    <li><strong>Inventory Complexity:</strong> Excessive SKUs increase carrying costs and supply chain risk</li>
                    <li><strong>Engineering Duplication:</strong> Teams designing new parts when existing solutions could be leveraged</li>
                </ul>

                <div class="key-stat">
                    <h3>15-25%</h3>
                    <p>Typical cost reduction potential through intelligent BOM standardization</p>
                </div>
            </div>
            <div class="slide-footer">
                Industry studies show medical device manufacturers lose millions annually to BOM fragmentation
            </div>
        </div>

        <!-- Slide 3: Solution Overview -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">3 / 12</div>
            </div>
            <div class="slide-content">
                <h2>Multi-Algorithm Intelligence Platform</h2>
                
                <div class="algorithm-grid">
                    <div class="algorithm-card">
                        <div class="method">Structural Analysis</div>
                        <h4>F-E-Line Clustering</h4>
                        <p>Leverages hierarchical BOM structure to identify natural assembly groupings</p>
                        <div class="performance-indicator high">89% Precision</div>
                    </div>
                    
                    <div class="algorithm-card">
                        <div class="method">Graph Theory</div>
                        <h4>Similarity Networks</h4>
                        <p>Advanced graph analysis of 176K pairwise component comparisons</p>
                        <div class="performance-indicator high">94% Precision</div>
                    </div>
                    
                    <div class="algorithm-card">
                        <div class="method">Machine Learning</div>
                        <h4>Semantic Clustering</h4>
                        <p>NLP-powered analysis of component descriptions using TF-IDF</p>
                        <div class="performance-indicator medium">72% Precision</div>
                    </div>
                </div>

                <div class="key-stat">
                    <h3>3-Layer Validation</h3>
                    <p>Proprietary consolidation engine prioritizes and validates findings across all algorithms</p>
                </div>
            </div>
            <div class="slide-footer">
                Each algorithm targets different relationship types - combined power eliminates false positives
            </div>
        </div>

        <!-- Slide 4: Performance Metrics -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">4 / 12</div>
            </div>
            <div class="slide-content">
                <h2>Performance vs Industry Benchmarks</h2>
                
                <table class="table">
                    <thead>
                        <tr>
                            <th>Metric</th>
                            <th>Our Results</th>
                            <th>Industry Standard</th>
                            <th>Performance Gap</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Cross-Product Standardization</td>
                            <td><strong>44.2%</strong></td>
                            <td>30%</td>
                            <td><span class="performance-indicator high">+47% above</span></td>
                        </tr>
                        <tr>
                            <td>High-Potential Families</td>
                            <td><strong>15.7%</strong></td>
                            <td>10%</td>
                            <td><span class="performance-indicator high">+57% above</span></td>
                        </tr>
                        <tr>
                            <td>Cost Reduction Potential</td>
                            <td><strong>43.8%</strong></td>
                            <td>25%</td>
                            <td><span class="performance-indicator high">+75% above</span></td>
                        </tr>
                        <tr>
                            <td>Cluster Quality</td>
                            <td><strong>75.3%</strong></td>
                            <td>60%</td>
                            <td><span class="performance-indicator high">+26% above</span></td>
                        </tr>
                    </tbody>
                </table>

                <div class="key-stat">
                    <h3>Superior Performance</h3>
                    <p>Exceeds industry standards across all key metrics</p>
                </div>
            </div>
            <div class="slide-footer">
                Benchmarks based on McKinsey, BCG, and industry consortium studies
            </div>
        </div>

        <!-- Slide 5: Real Data Results -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">5 / 12</div>
            </div>
            <div class="slide-content">
                <h2>Real-World Validation: GE HealthCare BOMs</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h4>5</h4>
                        <p>Complete Product BOMs</p>
                        <div class="benchmark">Full production systems</div>
                    </div>
                    <div class="stat-card">
                        <h4>81%</h4>
                        <p>Data Quality Retention</p>
                        <div class="benchmark">After intelligent filtering</div>
                    </div>
                    <div class="stat-card">
                        <h4>176K</h4>
                        <p>Similarity Comparisons</p>
                        <div class="benchmark">Pairwise analysis</div>
                    </div>
                </div>

                <h3>Largest Opportunity Identified:</h3>
                <div class="impact-card">
                    <div class="impact-level">Critical Impact Family</div>
                    <h4>F-E-Line "1-1-2" Assembly Pattern</h4>
                    <div class="savings">37,643 components</div>
                    <p>Spans 3 different product lines</p>
                    <p><strong>Estimated Annual Savings: $2-5M</strong></p>
                    <p>High standardization potential with immediate procurement volume advantages</p>
                </div>
            </div>
            <div class="slide-footer">
                Analysis performed on actual GE HealthCare production BOM data
            </div>
        </div>

        <!-- Slide 6: Business Impact Tiers -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">6 / 12</div>
            </div>
            <div class="slide-content">
                <h2>Tiered Opportunity Portfolio</h2>
                
                <div class="impact-grid">
                    <div class="impact-card">
                        <div class="impact-level">Critical Impact</div>
                        <h4>2 Families Identified</h4>
                        <div class="savings">$2-5M annually</div>
                        <p>Massive cross-product standardization</p>
                        <p>Immediate procurement leverage</p>
                    </div>
                    
                    <div class="impact-card">
                        <div class="impact-level">High Impact</div>
                        <h4>12 Families Identified</h4>
                        <div class="savings">$500K-1M annually</div>
                        <p>Quick-win opportunities</p>
                        <p>Ready for immediate action</p>
                    </div>
                </div>

                <div class="impact-grid">
                    <div class="impact-card">
                        <div class="impact-level">Medium Impact</div>
                        <h4>58 Families Identified</h4>
                        <div class="savings">$50K-200K annually</div>
                        <p>Structured optimization</p>
                        <p>Strategic standardization</p>
                    </div>
                    
                    <div class="impact-card">
                        <div class="impact-level">Portfolio Total</div>
                        <h4>140 Families</h4>
                        <div class="savings">$8-15M potential</div>
                        <p>Comprehensive optimization</p>
                        <p>Multi-year value realization</p>
                    </div>
                </div>
            </div>
            <div class="slide-footer">
                Conservative estimates based on industry standardization studies
            </div>
        </div>

        <!-- Slide 7: Technical Validation -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">7 / 12</div>
            </div>
            <div class="slide-content">
                <h2>Enterprise-Grade Technical Validation</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h4>75.3%</h4>
                        <p>Intra-Cluster Cohesion</p>
                        <div class="benchmark">Components truly belong together</div>
                    </div>
                    <div class="stat-card">
                        <h4>98.7%</h4>
                        <p>Inter-Cluster Separation</p>
                        <div class="benchmark">Distinct family boundaries</div>
                    </div>
                    <div class="stat-card">
                        <h4>82.3%</h4>
                        <p>Implementation Feasibility</p>
                        <div class="benchmark">Families ready for action</div>
                    </div>
                </div>

                <ul class="bullet-points">
                    <li><strong>Mathematical Foundation:</strong> Cosine similarity, graph theory, probabilistic matching</li>
                    <li><strong>Scalability Proven:</strong> Linear time complexity handles enterprise BOMs</li>
                    <li><strong>Multi-Source Validation:</strong> Three independent algorithms prevent false positives</li>
                    <li><strong>Quality Assurance:</strong> Automated detection of incomplete/invalid data</li>
                </ul>
            </div>
            <div class="slide-footer">
                Validation metrics exceed requirements for production deployment
            </div>
        </div>

        <!-- Slide 8: Implementation Roadmap -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">8 / 12</div>
            </div>
            <div class="slide-content">
                <h2>Implementation Roadmap & ROI Timeline</h2>
                
                <div class="timeline">
                    <div class="timeline-item">
                        <h4>Phase 1: Pilot</h4>
                        <p>Months 1-2</p>
                        <p>Deploy on 2-3 high-value product lines</p>
                        <p>Validate top 20 opportunities</p>
                        <div class="roi-highlight">200-300% ROI</div>
                    </div>
                    
                    <div class="timeline-item">
                        <h4>Phase 2: Scale</h4>
                        <p>Months 3-6</p>
                        <p>Full product portfolio</p>
                        <p>PLM/ERP integration</p>
                        <div class="roi-highlight">400-600% ROI</div>
                    </div>
                    
                    <div class="timeline-item">
                        <h4>Phase 3: Optimize</h4>
                        <p>Months 6+</p>
                        <p>Real-time BOM analysis</p>
                        <p>Automated recommendations</p>
                        <div class="roi-highlight">15-25% annual growth</div>
                    </div>
                </div>

                <div class="key-stat">
                    <h3>30-Day Pilot Available</h3>
                    <p>Risk-free evaluation with guaranteed opportunity identification</p>
                </div>
            </div>
            <div class="slide-footer">
                Phased approach minimizes disruption while maximizing early wins
            </div>
        </div>

        <!-- Slide 9: Competitive Advantage -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">9 / 12</div>
            </div>
            <div class="slide-content">
                <h2>Why This Solution Wins</h2>
                
                <div class="algorithm-grid">
                    <div class="algorithm-card">
                        <h4>✅ Proven at Scale</h4>
                        <p>Real-world validation on complex, multi-product BOMs</p>
                        <p>Performance exceeds industry benchmarks</p>
                        <p>Handles enterprise data volumes with high accuracy</p>
                    </div>
                    
                    <div class="algorithm-card">
                        <h4>✅ Immediate Value</h4>
                        <p>Identifies millions in opportunities from day one</p>
                        <p>Actionable, prioritized recommendations</p>
                        <p>Clear ROI path with measurable outcomes</p>
                    </div>
                    
                    <div class="algorithm-card">
                        <h4>✅ Technical Excellence</h4>
                        <p>Multi-algorithm approach eliminates risks</p>
                        <p>Advanced NLP and graph theory insights</p>
                        <p>Scalable architecture for enterprise deployment</p>
                    </div>
                </div>

                <div class="key-stat">
                    <h3>Implementation Ready</h3>
                    <p>Comprehensive validation, business classification, and integration path defined</p>
                </div>
            </div>
            <div class="slide-footer">
                No other solution combines this level of technical sophistication with proven business results
            </div>
        </div>

        <!-- Slide 10: Risk Mitigation -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">10 / 12</div>
            </div>
            <div class="slide-content">
                <h2>Risk Mitigation & Quality Assurance</h2>
                
                <ul class="bullet-points">
                    <li><strong>False Positive Prevention:</strong> Three-algorithm validation ensures only genuine relationships identified</li>
                    <li><strong>Business Impact Scoring:</strong> Quantified prioritization prevents resource waste on low-value opportunities</li>
                    <li><strong>Phased Implementation:</strong> Pilot approach allows validation before full-scale commitment</li>
                    <li><strong>Quality Metrics:</strong> Comprehensive cohesion and separation analysis validates cluster integrity</li>
                    <li><strong>Domain Expertise Integration:</strong> Engineering review process for final validation</li>
                </ul>

                <div class="impact-grid">
                    <div class="impact-card">
                        <div class="impact-level">Data Quality</div>
                        <h4>81% Retention Rate</h4>
                        <p>Intelligent filtering removes incomplete/invalid entries</p>
                        <p>Clean dataset ensures reliable results</p>
                    </div>
                    
                    <div class="impact-card">
                        <div class="impact-level">Algorithm Robustness</div>
                        <h4>98.7% Separation</h4>
                        <p>Distinct family boundaries prevent overlap</p>
                        <p>High confidence in recommendations</p>
                    </div>
                </div>
            </div>
            <div class="slide-footer">
                Enterprise-grade quality controls throughout the entire process
            </div>
        </div>

        <!-- Slide 11: Next Steps -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">11 / 12</div>
            </div>
            <div class="slide-content">
                <h2>Immediate Next Steps</h2>
                
                <div class="timeline">
                    <div class="timeline-item">
                        <h4>Week 1</h4>
                        <p>Live Demo Session</p>
                        <p>Interactive analysis with your BOM data</p>
                        <p>Q&A with technical team</p>
                    </div>
                    
                    <div class="timeline-item">
                        <h4>Week 2</h4>
                        <p>Pilot Scope Definition</p>
                        <p>Select 2-3 product lines</p>
                        <p>Define success metrics</p>
                    </div>
                    
                    <div class="timeline-item">
                        <h4>Week 3-4</h4>
                        <p>Detailed ROI Modeling</p>
                        <p>Financial impact analysis</p>
                        <p>Implementation planning</p>
                    </div>
                </div>

                <div class="key-stat">
                    <h3>30-Day Pilot Program</h3>
                    <p>Risk-free evaluation with guaranteed standardization opportunities identification or full refund</p>
                </div>

                <ul class="bullet-points">
                    <li><strong>Full Technical Documentation:</strong> Complete methodology and algorithm details available</li>
                    <li><strong>Reference Implementation:</strong> Working code and validation results provided</li>
                    <li><strong>Integration Support:</strong> Technical team available for PLM/ERP integration planning</li>
                </ul>
            </div>
            <div class="slide-footer">
                Ready to unlock millions in hidden standardization value
            </div>
        </div>

        <!-- Slide 12: Call to Action -->
        <div class="slide">
            <div class="slide-header">
                <div class="ge-logo">GE HealthCare</div>
                <div class="slide-number">12 / 12</div>
            </div>
            <div class="slide-content">
                <h1>Transform Your Product Development Efficiency</h1>
                <div class="subtitle">Through Intelligent BOM Analysis</div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h4>$8-15M</h4>
                        <p>Total Savings Potential</p>
                        <div class="benchmark">Conservative estimate</div>
                    </div>
                    <div class="stat-card">
                        <h4>6 Months</h4>
                        <p>Full ROI Realization</p>
                        <div class="benchmark">400-600%"""  # Load your HTML
soup = BeautifulSoup(html_content, 'html.parser')

# Create a new PowerPoint presentation
prs = Presentation()

# Extracting slide titles
for slide in soup.find_all('div', class_='slide'):
    title = slide.find('h2')
    if title:
        slide_layout = prs.slide_layouts[1]  # Title + Content slide
        slide = prs.slides.add_slide(slide_layout)
        title_placeholder = slide.shapes.title
        title_placeholder.text = title.text

prs.save("output.pptx")
