#!/usr/bin/env python3
"""
Integrate PCM Data and Complete Stage 2 Analysis
Run this script after providing PCM files
"""

import pandas as pd
import numpy as np
import os
from full_dataset_similarity_clustering import FullDatasetSimilarityAnalyzer

def find_pcm_files():
    """Automatically find PCM files in the workspace"""
    pcm_files = {}
    
    # Look for Excel files that might be PCM data
    for file in os.listdir('.'):
        if file.endswith('.xlsx') and 'pcm' in file.lower():
            system_name = file.replace('.xlsx', '').replace('_pcm', '').replace('pcm_', '')
            pcm_files[system_name] = file
            print(f"📋 Found PCM file: {file} -> {system_name}")
    
    # Also check common PCM file patterns
    common_patterns = [
        'voyager_pcm.xlsx',
        'system_a_pcm.xlsx', 
        'system_b_pcm.xlsx',
        'system_c_pcm.xlsx',
        'product_compatibility_matrix.xlsx'
    ]
    
    for pattern in common_patterns:
        if os.path.exists(pattern):
            system_name = pattern.replace('.xlsx', '').replace('_pcm', '').replace('pcm_', '')
            pcm_files[system_name] = pattern
            print(f"📋 Found PCM file: {pattern} -> {system_name}")
    
    return pcm_files

def manual_pcm_integration():
    """Manual PCM file specification"""
    print("🔧 MANUAL PCM INTEGRATION")
    print("=" * 30)
    print("Please specify your PCM files:")
    print("Format: {'system_name': 'file_path.xlsx'}")
    print("")
    print("Example:")
    print("pcm_files = {")
    print("    'Voyager_System': 'voyager_pcm.xlsx',")
    print("    'System_A': 'system_a_pcm.xlsx',")
    print("    'System_B': 'system_b_pcm.xlsx'")
    print("}")
    print("")
    
    # You can manually specify PCM files here:
    pcm_files = {
        # Add your PCM files here when ready
        # 'System_Name': 'path/to/pcm_file.xlsx'
    }
    
    return pcm_files

def run_complete_analysis():
    """Run the complete two-stage analysis"""
    print("🚀 RUNNING COMPLETE TWO-STAGE ANALYSIS")
    print("=" * 50)
    
    # Initialize analyzer
    analyzer = FullDatasetSimilarityAnalyzer()
    
    # Stage 1: Comprehensive similarity analysis (already done, but run again for completeness)
    print("🎯 Running Stage 1...")
    stage1_results = analyzer.stage1_comprehensive_similarity_analysis()
    
    # Try to find PCM files automatically
    pcm_files = find_pcm_files()
    
    if not pcm_files:
        print("\n⚠️ No PCM files found automatically.")
        print("Please add your PCM files and update the manual_pcm_integration() function")
        pcm_files = manual_pcm_integration()
    
    if pcm_files:
        print(f"\n📋 Loading {len(pcm_files)} PCM files...")
        
        # Load PCM data
        analyzer.load_pcm_data(pcm_files)
        
        # Stage 2: Compatibility filtering
        print("\n🎯 Running Stage 2...")
        stage2_results = analyzer.stage2_compatibility_filtering()
        
        # Export final results
        print("\n💾 Exporting final results...")
        analyzer.export_results_to_excel('output/final_similarity_clustering_results.xlsx')
        
        # Generate summary report
        generate_final_summary_report(analyzer, stage1_results, stage2_results)
        
        print("\n✅ COMPLETE ANALYSIS FINISHED!")
        print("📊 Check output/final_similarity_clustering_results.xlsx for system-specific compatible clusters")
        
    else:
        print("\n⏳ Waiting for PCM files...")
        print("Stage 1 complete. Provide PCM files to continue with Stage 2.")
        
        # Export Stage 1 results only
        analyzer.export_results_to_excel('output/stage1_only_results.xlsx')
        print("📊 Stage 1 results saved to: output/stage1_only_results.xlsx")
    
    return analyzer

def generate_final_summary_report(analyzer, stage1_results, stage2_results):
    """Generate comprehensive final summary report"""
    
    report_lines = []
    report_lines.append("COMPREHENSIVE TWO-STAGE SIMILARITY ANALYSIS REPORT")
    report_lines.append("=" * 65)
    report_lines.append("")
    
    # Dataset overview
    report_lines.append("📊 DATASET OVERVIEW")
    report_lines.append("-" * 25)
    report_lines.append(f"Total Components Analyzed: {len(analyzer.df)}")
    report_lines.append(f"Pages Covered: {sorted(analyzer.df['page_number'].unique())}")
    report_lines.append(f"Color Distribution:")
    color_dist = analyzer.df['color_detected'].value_counts()
    for color, count in color_dist.items():
        percentage = (count / len(analyzer.df)) * 100
        report_lines.append(f"  {color}: {count} ({percentage:.1f}%)")
    report_lines.append("")
    
    # Stage 1 results
    report_lines.append("🎯 STAGE 1: SIMILARITY ANALYSIS RESULTS")
    report_lines.append("-" * 45)
    if 'exact_matches' in stage1_results:
        report_lines.append(f"Exact Description Matches: {len(stage1_results['exact_matches'])}")
    if 'semantic' in stage1_results and 'matches' in stage1_results['semantic']:
        report_lines.append(f"Semantic Matches (≥0.7): {len(stage1_results['semantic']['matches'])}")
    if 'spatial' in stage1_results:
        total_spatial = sum(len(clusters) for clusters in stage1_results['spatial'].values())
        report_lines.append(f"Spatial Clusters: {total_spatial}")
    if 'color' in stage1_results and 'chains' in stage1_results['color']:
        report_lines.append(f"Color Dependency Chains: {len(stage1_results['color']['chains'])}")
    if 'clustering' in stage1_results:
        report_lines.append(f"Clustering Methods Applied: {len(stage1_results['clustering'])}")
    report_lines.append("")
    
    # Stage 2 results
    if stage2_results:
        report_lines.append("🎯 STAGE 2: COMPATIBILITY FILTERING RESULTS")
        report_lines.append("-" * 50)
        report_lines.append(f"Systems Analyzed: {len(stage2_results)}")
        
        for system_name, system_clusters in stage2_results.items():
            report_lines.append(f"\n{system_name}:")
            total_compatible = 0
            for cluster_method, clusters in system_clusters.items():
                method_total = sum(cluster['count'] for cluster in clusters.values())
                total_compatible += method_total
                report_lines.append(f"  {cluster_method}: {len(clusters)} clusters, {method_total} components")
            report_lines.append(f"  Total Compatible Components: {total_compatible}")
        report_lines.append("")
    
    # Clustering method performance
    if 'clustering' in stage1_results:
        report_lines.append("📈 CLUSTERING METHOD PERFORMANCE")
        report_lines.append("-" * 40)
        for method_name, method_result in stage1_results['clustering'].items():
            n_clusters = method_result.get('n_clusters', 'Unknown')
            method_type = method_result.get('method', 'Unknown')
            report_lines.append(f"{method_name}: {n_clusters} clusters ({method_type})")
        report_lines.append("")
    
    # Recommendations
    report_lines.append("🚀 RECOMMENDATIONS")
    report_lines.append("-" * 20)
    report_lines.append("1. Review system-specific compatible clusters in Excel output")
    report_lines.append("2. Focus on high-compatibility clusters for product optimization")
    report_lines.append("3. Investigate cross-system component compatibility opportunities")
    report_lines.append("4. Use clustering results for product family definition")
    report_lines.append("5. Apply findings to supply chain and BOM optimization")
    report_lines.append("")
    
    # Files generated
    report_lines.append("📁 FILES GENERATED")
    report_lines.append("-" * 20)
    report_lines.append("- final_similarity_clustering_results.xlsx (Main results)")
    report_lines.append("- stage1_overview_visualization.png (Overview charts)")
    report_lines.append("- stage1_spatial_distribution.png (Spatial analysis)")
    report_lines.append("- stage1_basic_results.xlsx (Stage 1 detailed results)")
    report_lines.append("- final_analysis_summary.txt (This report)")
    
    # Save report
    with open('output/final_analysis_summary.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print("✅ Final summary report saved: output/final_analysis_summary.txt")

def main():
    """Main execution function"""
    print("🔧 PCM INTEGRATION AND FINALIZATION SCRIPT")
    print("=" * 50)
    print("")
    print("This script will:")
    print("1. Look for PCM files automatically")
    print("2. Run complete two-stage analysis")
    print("3. Generate system-specific compatible clusters")
    print("4. Export results to Excel with separate sheets")
    print("")
    
    # Run the complete analysis
    analyzer = run_complete_analysis()
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
