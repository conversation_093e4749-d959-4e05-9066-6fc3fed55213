@echo off
echo 🚀 BOM Analysis Complete Setup - Python 3.11.9
echo =============================================
echo.

REM Try different ways to access Python
set PYTHON_FOUND=0

echo 🔍 Searching for Python 3.11.9...

REM Method 1: Try python command
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Found python command
    set PYTHON_CMD=python
    set PYTHON_FOUND=1
    goto :setup
)

REM Method 2: Try py launcher
py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Found py launcher
    set PYTHON_CMD=py
    set PYTHON_FOUND=1
    goto :setup
)

REM Method 3: Try common installation paths
set "PYTHON_PATH=C:\Program Files\Python311\python.exe"
if exist "%PYTHON_PATH%" (
    echo ✅ Found Python at %PYTHON_PATH%
    set PYTHON_CMD="%PYTHON_PATH%"
    set PYTHON_FOUND=1
    goto :setup
)

set "PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe"
if exist "%PYTHON_PATH%" (
    echo ✅ Found Python at %PYTHON_PATH%
    set PYTHON_CMD="%PYTHON_PATH%"
    set PYTHON_FOUND=1
    goto :setup
)

REM Method 4: Try Microsoft Store Python
set "PYTHON_PATH=C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe"
if exist "%PYTHON_PATH%" (
    echo ✅ Found Microsoft Store Python
    set PYTHON_CMD="%PYTHON_PATH%"
    set PYTHON_FOUND=1
    goto :setup
)

if %PYTHON_FOUND% equ 0 (
    echo ❌ Python 3.11.9 not found!
    echo Please restart Command Prompt after Python installation
    echo Or check if Python was added to PATH
    pause
    exit /b 1
)

:setup
echo.
echo 🐍 Using Python: %PYTHON_CMD%
%PYTHON_CMD% --version
echo.

echo 🔧 Creating virtual environment...
%PYTHON_CMD% -m venv bom_analysis_env
if %errorlevel% neq 0 (
    echo ❌ Failed to create virtual environment
    pause
    exit /b 1
)

echo ✅ Virtual environment created!
echo.

echo 🔄 Activating environment...
call bom_analysis_env\Scripts\activate.bat

echo 🔄 Upgrading pip...
python -m pip install --upgrade pip

echo.
echo 📦 Installing packages (this may take a few minutes)...
echo.

echo Installing core data science packages...
pip install pandas numpy scikit-learn scipy

echo Installing Excel support...
pip install openpyxl xlrd

echo Installing visualization...
pip install matplotlib seaborn plotly

echo Installing network analysis...
pip install networkx

echo Installing NLP and AI packages...
pip install sentence-transformers transformers

echo Installing PyTorch...
pip install torch

echo Installing Jupyter...
pip install jupyter jupyterlab ipywidgets

echo Installing utilities...
pip install tqdm joblib

echo.
echo 🧪 Testing installation...
python -c "import pandas, numpy, sklearn; print('✅ Core packages working!')"

echo.
echo 🎉 Setup completed successfully!
echo.
echo 📋 Your BOM Analysis Environment is ready!
echo.
echo 🚀 To use your environment:
echo 1. Run: bom_analysis_env\Scripts\activate.bat
echo 2. Then: python bom_similarity_analysis.py
echo 3. Or: jupyter lab
echo.
echo 📁 Place your Excel BOM files in the 'input' folder
echo 📊 Results will be saved in the 'output' folder
echo.
pause
