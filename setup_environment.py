#!/usr/bin/env python3
"""
BOM Cluster Analysis Environment Setup Script
This script sets up a complete Python environment for BOM similarity analysis.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully!")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}:")
        print(f"Command: {command}")
        print(f"Error: {e.stderr}")
        return None

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible. Need Python 3.8+")
        return False

def create_virtual_environment():
    """Create and activate virtual environment."""
    venv_name = "bom_analysis_env"
    
    # Check if virtual environment already exists
    if os.path.exists(venv_name):
        print(f"📁 Virtual environment '{venv_name}' already exists")
        return venv_name
    
    # Create virtual environment
    if run_command(f"python -m venv {venv_name}", f"Creating virtual environment '{venv_name}'"):
        return venv_name
    return None

def install_requirements(venv_name):
    """Install requirements in the virtual environment."""
    if sys.platform == "win32":
        pip_path = f"{venv_name}\\Scripts\\pip"
        python_path = f"{venv_name}\\Scripts\\python"
    else:
        pip_path = f"{venv_name}/bin/pip"
        python_path = f"{venv_name}/bin/python"
    
    # Upgrade pip first
    run_command(f"{python_path} -m pip install --upgrade pip", "Upgrading pip")
    
    # Install requirements
    if os.path.exists("requirements.txt"):
        run_command(f"{pip_path} install -r requirements.txt", "Installing requirements from requirements.txt")
    else:
        print("❌ requirements.txt not found!")
        return False
    
    return True

def setup_jupyter_kernel(venv_name):
    """Setup Jupyter kernel for the virtual environment."""
    if sys.platform == "win32":
        python_path = f"{venv_name}\\Scripts\\python"
    else:
        python_path = f"{venv_name}/bin/python"
    
    kernel_name = "bom-analysis"
    run_command(
        f"{python_path} -m ipykernel install --user --name={kernel_name} --display-name='BOM Analysis'",
        f"Setting up Jupyter kernel '{kernel_name}'"
    )

def create_activation_scripts():
    """Create convenient activation scripts."""
    
    # Windows batch file
    with open("activate_env.bat", "w") as f:
        f.write("""@echo off
echo 🚀 Activating BOM Analysis Environment...
call bom_analysis_env\\Scripts\\activate.bat
echo ✅ Environment activated! You can now run:
echo    python bom_similarity_analysis.py
echo    jupyter lab
echo    jupyter notebook
""")
    
    # Unix shell script
    with open("activate_env.sh", "w") as f:
        f.write("""#!/bin/bash
echo "🚀 Activating BOM Analysis Environment..."
source bom_analysis_env/bin/activate
echo "✅ Environment activated! You can now run:"
echo "   python bom_similarity_analysis.py"
echo "   jupyter lab"
echo "   jupyter notebook"
""")
    
    # Make shell script executable on Unix systems
    if sys.platform != "win32":
        os.chmod("activate_env.sh", 0o755)
    
    print("✅ Created activation scripts: activate_env.bat (Windows) and activate_env.sh (Unix)")

def main():
    """Main setup function."""
    print("🔧 BOM Cluster Analysis Environment Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    venv_name = create_virtual_environment()
    if not venv_name:
        print("❌ Failed to create virtual environment")
        sys.exit(1)
    
    # Install requirements
    if not install_requirements(venv_name):
        print("❌ Failed to install requirements")
        sys.exit(1)
    
    # Setup Jupyter kernel
    setup_jupyter_kernel(venv_name)
    
    # Create activation scripts
    create_activation_scripts()
    
    print("\n🎉 Environment setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Activate the environment:")
    if sys.platform == "win32":
        print("   Windows: activate_env.bat")
    else:
        print("   Unix/Mac: source activate_env.sh")
    print("2. Run your analysis: python bom_similarity_analysis.py")
    print("3. Start Jupyter: jupyter lab")
    print("\n📁 Project structure:")
    print("   📂 input/          - Place your Excel BOM files here")
    print("   📂 output/         - Analysis results will be saved here")
    print("   📄 bom_similarity_analysis.py - Main analysis script")

if __name__ == "__main__":
    main()
