#!/usr/bin/env python3
"""
PCM Page-by-Page Analysis: Left to Right, Line by Line
Regrouper les éléments qui paraissent comme un seul truc avec code et description
"""

import pandas as pd
import numpy as np
from PIL import Image
import cv2
import pytesseract
import os
import re
from collections import defaultdict

class PCMPageAnalyzer:
    def __init__(self):
        """Initialize PCM page analyzer"""
        self.pages_data = []
        self.extracted_elements = []
        
    def load_pcm_pages(self):
        """Load all PCM pages from output folder"""
        print("📄 CHARGEMENT DES PAGES PCM")
        print("=" * 40)
        
        pages = []
        page_files = [f for f in os.listdir('output') if f.startswith('page_') and f.endswith('.png')]
        page_files.sort(key=lambda x: int(x.split('_')[1].split('.')[0]))
        
        for page_file in page_files:
            page_number = int(page_file.split('_')[1].split('.')[0])
            page_path = os.path.join('output', page_file)
            
            if os.path.exists(page_path):
                pages.append({
                    'page_number': page_number,
                    'image_path': page_path,
                    'filename': page_file
                })
        
        print(f"✅ Trouvé {len(pages)} pages PCM à analyser")
        self.pages_data = pages
        return pages
    
    def analyze_page_structure(self, page_info):
        """Analyser la structure d'une page de gauche à droite, ligne par ligne"""
        page_num = page_info['page_number']
        image_path = page_info['image_path']
        
        print(f"\n📄 ANALYSE PAGE {page_num}")
        print("=" * 50)
        print(f"Fichier: {page_info['filename']}")
        
        # Charger l'image
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Impossible de charger l'image: {image_path}")
            return []
        
        height, width = image.shape[:2]
        print(f"📐 Dimensions: {width} x {height} pixels")
        
        # Convertir en RGB pour PIL
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image_rgb)
        
        # Détecter les zones colorées (cadrants)
        colored_regions = self.detect_colored_regions(image)
        
        # Détecter le texte avec OCR
        text_elements = self.extract_text_elements(pil_image)
        
        # Regrouper les éléments par position (gauche à droite, ligne par ligne)
        grouped_elements = self.group_elements_by_position(text_elements, colored_regions, width, height)
        
        # Analyser chaque groupe
        page_elements = []
        for group in grouped_elements:
            element = self.analyze_element_group(group, page_num)
            if element:
                page_elements.append(element)
        
        print(f"✅ Page {page_num}: {len(page_elements)} éléments identifiés")
        return page_elements
    
    def detect_colored_regions(self, image):
        """Détecter les cadrants colorés"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Définir les plages de couleurs
        color_ranges = {
            'Blue': {
                'lower': np.array([100, 50, 50]),
                'upper': np.array([130, 255, 255])
            },
            'Purple': {
                'lower': np.array([130, 50, 50]),
                'upper': np.array([160, 255, 255])
            },
            'Red': {
                'lower': np.array([0, 50, 50]),
                'upper': np.array([10, 255, 255])
            },
            'Red2': {
                'lower': np.array([170, 50, 50]),
                'upper': np.array([180, 255, 255])
            }
        }
        
        colored_regions = []
        
        for color_name, color_info in color_ranges.items():
            if color_name == 'Red2':
                color_display = 'Red'
            else:
                color_display = color_name
                
            mask = cv2.inRange(hsv, color_info['lower'], color_info['upper'])
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 1000:  # Seuil minimum
                    x, y, w, h = cv2.boundingRect(contour)
                    colored_regions.append({
                        'color': color_display,
                        'x': x, 'y': y, 'width': w, 'height': h,
                        'area': area
                    })
        
        return colored_regions
    
    def extract_text_elements(self, pil_image):
        """Extraire les éléments texte avec OCR"""
        try:
            # Configuration OCR pour français
            custom_config = r'--oem 3 --psm 6 -l fra'
            
            # Extraire le texte avec les coordonnées
            data = pytesseract.image_to_data(pil_image, config=custom_config, output_type=pytesseract.Output.DICT)
            
            text_elements = []
            n_boxes = len(data['level'])
            
            for i in range(n_boxes):
                if int(data['conf'][i]) > 30:  # Confiance minimum
                    text = data['text'][i].strip()
                    if text:
                        text_elements.append({
                            'text': text,
                            'x': data['left'][i],
                            'y': data['top'][i],
                            'width': data['width'][i],
                            'height': data['height'][i],
                            'confidence': data['conf'][i]
                        })
            
            return text_elements
            
        except Exception as e:
            print(f"⚠️ Erreur OCR: {e}")
            return []
    
    def group_elements_by_position(self, text_elements, colored_regions, page_width, page_height):
        """Regrouper les éléments par position (gauche à droite, ligne par ligne)"""
        
        # Trier les éléments de gauche à droite, puis de haut en bas
        sorted_elements = sorted(text_elements, key=lambda x: (x['y'], x['x']))
        
        # Regrouper les éléments qui sont proches (même ligne/zone)
        groups = []
        current_group = []
        last_y = -1
        line_tolerance = 50  # Tolérance pour considérer que c'est la même ligne
        
        for element in sorted_elements:
            # Si c'est une nouvelle ligne ou trop éloigné
            if last_y == -1 or abs(element['y'] - last_y) > line_tolerance:
                if current_group:
                    groups.append(current_group)
                current_group = [element]
                last_y = element['y']
            else:
                current_group.append(element)
                last_y = element['y']
        
        # Ajouter le dernier groupe
        if current_group:
            groups.append(current_group)
        
        # Associer chaque groupe avec les régions colorées
        for group in groups:
            group_color = self.find_group_color(group, colored_regions)
            for element in group:
                element['group_color'] = group_color
        
        return groups
    
    def find_group_color(self, group, colored_regions):
        """Trouver la couleur du cadrant pour un groupe d'éléments"""
        if not group:
            return None
        
        # Calculer le centre du groupe
        group_x = sum(e['x'] + e['width']/2 for e in group) / len(group)
        group_y = sum(e['y'] + e['height']/2 for e in group) / len(group)
        
        # Chercher dans quelle région colorée se trouve le groupe
        for region in colored_regions:
            if (region['x'] <= group_x <= region['x'] + region['width'] and
                region['y'] <= group_y <= region['y'] + region['height']):
                return region['color']
        
        return None  # Pas dans un cadrant coloré
    
    def analyze_element_group(self, group, page_num):
        """Analyser un groupe d'éléments pour identifier code et description"""
        if not group:
            return None
        
        # Combiner tout le texte du groupe
        full_text = ' '.join([elem['text'] for elem in group])
        
        # Chercher des patterns de code (lettres + chiffres)
        code_patterns = [
            r'[A-Z]{1,4}[-_]?\d{3,6}',  # Ex: CPU-1234, G1234
            r'\d{4,6}',                  # Ex: 1234567
            r'[A-Z]\d{4,6}[A-Z]{0,2}'   # Ex: G1234AB
        ]
        
        found_code = None
        for pattern in code_patterns:
            matches = re.findall(pattern, full_text)
            if matches:
                found_code = matches[0]
                break
        
        # Le reste du texte est considéré comme description
        description = full_text
        if found_code:
            description = full_text.replace(found_code, '').strip()
        
        # Nettoyer la description
        description = re.sub(r'\s+', ' ', description).strip()
        
        # Calculer la position moyenne du groupe
        avg_x = sum(e['x'] for e in group) / len(group)
        avg_y = sum(e['y'] for e in group) / len(group)
        
        # Déterminer la couleur du cadrant
        group_color = group[0].get('group_color', None)
        
        element = {
            'page': page_num,
            'code': found_code,
            'description': description,
            'full_text': full_text,
            'cadrant_color': group_color,
            'position_x': int(avg_x),
            'position_y': int(avg_y),
            'confidence': sum(e['confidence'] for e in group) / len(group)
        }
        
        return element
    
    def print_page_analysis(self, page_elements, page_num):
        """Afficher l'analyse d'une page"""
        print(f"\n📋 ÉLÉMENTS IDENTIFIÉS - PAGE {page_num}")
        print("=" * 60)
        
        # Trier par position (gauche à droite, haut en bas)
        sorted_elements = sorted(page_elements, key=lambda x: (x['position_y'], x['position_x']))
        
        for i, element in enumerate(sorted_elements, 1):
            cadrant = f"[{element['cadrant_color']}]" if element['cadrant_color'] else "[Sans couleur]"
            
            print(f"\n{i:2d}. {cadrant}")
            print(f"    Code: {element['code'] or 'Non identifié'}")
            print(f"    Description: {element['description']}")
            print(f"    Texte complet: {element['full_text']}")
            print(f"    Position: ({element['position_x']}, {element['position_y']})")
            print(f"    Confiance: {element['confidence']:.1f}%")
    
    def analyze_all_pages(self):
        """Analyser toutes les pages du PCM"""
        print("🔍 ANALYSE COMPLÈTE DU PCM VOYAGER")
        print("=" * 60)
        print("Processus: Page par page, gauche à droite, ligne par ligne")
        print("Objectif: Regrouper éléments avec code et description")
        print("")
        
        if not self.pages_data:
            self.load_pcm_pages()
        
        all_elements = []
        
        # Analyser chaque page
        for page_info in self.pages_data[:3]:  # Commencer par les 3 premières pages
            page_elements = self.analyze_page_structure(page_info)
            self.print_page_analysis(page_elements, page_info['page_number'])
            all_elements.extend(page_elements)
        
        self.extracted_elements = all_elements
        
        print(f"\n📊 RÉSUMÉ GLOBAL")
        print("=" * 30)
        print(f"Total éléments extraits: {len(all_elements)}")
        
        # Statistiques par couleur
        color_stats = defaultdict(int)
        for elem in all_elements:
            color = elem['cadrant_color'] or 'Sans couleur'
            color_stats[color] += 1
        
        print(f"\nRépartition par couleur:")
        for color, count in color_stats.items():
            print(f"  {color}: {count} éléments")
        
        return all_elements

def main():
    """Fonction principale"""
    print("🔍 ANALYSE PCM VOYAGER - PAGE PAR PAGE")
    print("=" * 70)
    
    analyzer = PCMPageAnalyzer()
    elements = analyzer.analyze_all_pages()
    
    print(f"\n✅ ANALYSE TERMINÉE")
    print(f"📊 {len(elements)} éléments identifiés")
    print(f"📋 Prêt pour définir les critères de similarité avec la BOM")
    
    return analyzer, elements

if __name__ == "__main__":
    analyzer, elements = main()
