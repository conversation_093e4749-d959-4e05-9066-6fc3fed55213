#!/usr/bin/env python3
"""
Artist Product Analysis
Complete BOM analysis for Artist product using file:
4516256_0d1582e3-6687-4d88-94c5-4c98b07e76cb_1747378299412.xlsx
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import Counter, defaultdict
import re
import json

def analyze_artist_bom():
    """Analyze the Artist product BOM file"""
    print("🎨 Starting Artist Product BOM Analysis")
    print("=" * 60)
    
    # File path
    file_path = r"C:\Users\<USER>\OneDrive - GE HealthCare\Documents\BOM_Cluster\input\4516256_0d1582e3-6687-4d88-94c5-4c98b07e76cb_1747378299412.xlsx"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return None
    
    try:
        # Read the Excel file
        xls = pd.ExcelFile(file_path)
        print(f"📊 Excel file loaded successfully")
        print(f"📋 Available sheets: {xls.sheet_names}")
        
        # Read the second sheet (BOM data)
        if len(xls.sheet_names) < 2:
            print("❌ No second sheet found")
            return None
        
        df = pd.read_excel(file_path, sheet_name=1)
        print(f"✅ BOM data loaded from sheet: {xls.sheet_names[1]}")
        print(f"📊 Raw data shape: {df.shape}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return None

def clean_artist_data(df):
    """Clean and prepare Artist BOM data"""
    print("\n🧹 Cleaning Artist BOM Data")
    print("-" * 40)
    
    # Show first few rows to understand structure
    print("📋 First 5 rows of raw data:")
    print(df.head())
    print(f"\n📊 Column names: {list(df.columns)}")
    
    # Take first 4 columns as standard BOM structure
    if len(df.columns) >= 4:
        df_clean = df.iloc[:, :4].copy()
        df_clean.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
    else:
        print(f"❌ Not enough columns. Found {len(df.columns)}, need at least 4")
        return None
    
    print(f"\n📊 Data before cleaning: {len(df_clean)} rows")
    
    # Remove rows with missing critical data
    initial_count = len(df_clean)
    df_clean = df_clean.dropna(subset=['F_E_Line', 'Item_ID', 'Item_Description'])
    print(f"📊 After removing null values: {len(df_clean)} rows (-{initial_count - len(df_clean)})")
    
    # Remove empty strings
    df_clean = df_clean[df_clean['F_E_Line'].astype(str).str.strip() != '']
    df_clean = df_clean[df_clean['Item_ID'].astype(str).str.strip() != '']
    df_clean = df_clean[df_clean['Item_Description'].astype(str).str.strip() != '']
    print(f"📊 After removing empty strings: {len(df_clean)} rows")
    
    # Remove numeric-only Item IDs (sequence numbers)
    numeric_mask = df_clean['Item_ID'].astype(str).str.match(r'^\d+$')
    df_clean = df_clean[~numeric_mask]
    print(f"📊 After removing numeric Item IDs: {len(df_clean)} rows")
    
    # Add metadata
    df_clean['Row_Index'] = range(len(df_clean))
    df_clean['Product'] = 'Artist'
    
    print(f"✅ Final clean data: {len(df_clean)} valid BOM items")
    return df_clean

def analyze_artist_structure(df):
    """Analyze the structure of Artist product"""
    print("\n🏗️ Analyzing Artist Product Structure")
    print("-" * 45)
    
    # F-E-Line analysis
    fe_lines = df['F_E_Line'].value_counts()
    print(f"📊 Unique F-E-Lines found: {len(fe_lines)}")
    print(f"📋 Top 10 F-E-Lines by item count:")
    for fe_line, count in fe_lines.head(10).items():
        print(f"   {fe_line}: {count} items")
    
    # Item ID analysis
    unique_items = df['Item_ID'].nunique()
    total_items = len(df)
    print(f"\n📊 Unique Item IDs: {unique_items}")
    print(f"📊 Total BOM entries: {total_items}")
    print(f"📊 Duplication rate: {((total_items - unique_items) / total_items * 100):.1f}%")
    
    # Description analysis
    unique_descriptions = df['Item_Description'].nunique()
    print(f"📊 Unique descriptions: {unique_descriptions}")
    print(f"📊 Description diversity: {(unique_descriptions / total_items * 100):.1f}%")
    
    return {
        'total_items': total_items,
        'unique_items': unique_items,
        'unique_descriptions': unique_descriptions,
        'unique_fe_lines': len(fe_lines),
        'fe_line_distribution': dict(fe_lines.head(20))
    }

def categorize_artist_components(df):
    """Categorize Artist components by type"""
    print("\n🏷️ Categorizing Artist Components")
    print("-" * 35)
    
    # Component categories specific to medical imaging
    component_categories = {
        'electronic': ['pcb', 'circuit', 'board', 'electronic', 'chip', 'processor', 
                      'memory', 'capacitor', 'resistor', 'diode', 'controller'],
        'mechanical': ['screw', 'bolt', 'nut', 'washer', 'spring', 'gear', 
                      'bearing', 'shaft', 'bracket', 'mount', 'clamp'],
        'housing': ['case', 'cover', 'housing', 'enclosure', 'panel', 
                   'door', 'lid', 'frame', 'chassis'],
        'cable': ['cable', 'wire', 'harness', 'connector', 'plug', 
                 'socket', 'terminal', 'adapter'],
        'sensor': ['sensor', 'detector', 'probe', 'transducer', 'switch', 
                  'encoder', 'coil', 'antenna'],
        'display': ['display', 'screen', 'monitor', 'led', 'lcd', 
                   'indicator', 'light', 'touchscreen'],
        'power': ['power', 'supply', 'battery', 'charger', 'adapter', 
                 'transformer', 'inverter', 'ups'],
        'cooling': ['fan', 'cooler', 'radiator', 'heat', 'thermal', 'cooling'],
        'software': ['software', 'license', 'application', 'program', 'os'],
        'medical': ['coil', 'gradient', 'magnet', 'rf', 'shim', 'bore', 
                   'patient', 'table', 'gantry']
    }
    
    # Categorize each component
    descriptions = df['Item_Description'].astype(str).str.lower()
    df['Component_Category'] = 'other'
    
    for category, keywords in component_categories.items():
        pattern = '|'.join(keywords)
        mask = descriptions.str.contains(pattern, na=False, regex=True)
        df.loc[mask, 'Component_Category'] = category
    
    # Show categorization results
    category_counts = df['Component_Category'].value_counts()
    print("📊 Component categorization results:")
    for category, count in category_counts.items():
        percentage = (count / len(df)) * 100
        print(f"   {category}: {count} items ({percentage:.1f}%)")
    
    return df, dict(category_counts)

def analyze_artist_fe_line_families(df):
    """Analyze F-E-Line based families in Artist"""
    print("\n👨‍👩‍👧‍👦 Analyzing Artist F-E-Line Families")
    print("-" * 42)
    
    fe_line_families = {}
    fe_line_groups = df.groupby('F_E_Line')
    
    for fe_line, group in fe_line_groups:
        if len(group) < 2:  # Skip single-item F-E-Lines
            continue
        
        family_data = {
            'fe_line': fe_line,
            'item_count': len(group),
            'unique_items': group['Item_ID'].nunique(),
            'component_categories': dict(group['Component_Category'].value_counts()),
            'dominant_category': group['Component_Category'].mode().iloc[0] if len(group) > 0 else 'other',
            'sample_items': list(group['Item_ID'].head(5)),
            'sample_descriptions': list(group['Item_Description'].head(5)),
            'description_diversity': group['Item_Description'].nunique() / len(group)
        }
        
        # Classify family type
        if family_data['item_count'] > 100:
            family_type = "Major Assembly"
        elif family_data['dominant_category'] != 'other':
            family_type = f"Specialized {family_data['dominant_category'].title()} Assembly"
        else:
            family_type = "Standard Assembly"
        
        family_data['family_type'] = family_type
        fe_line_families[f"Artist_FE_{fe_line}"] = family_data
    
    print(f"✅ Found {len(fe_line_families)} F-E-Line families")
    
    # Show top families
    sorted_families = sorted(fe_line_families.items(), 
                           key=lambda x: x[1]['item_count'], reverse=True)
    
    print("\n📊 Top 10 Artist F-E-Line families:")
    for i, (family_name, family_data) in enumerate(sorted_families[:10], 1):
        print(f"   {i:2d}. {family_data['fe_line']}: {family_data['item_count']} items "
              f"({family_data['family_type']})")
    
    return fe_line_families

def create_artist_visualizations(df, structure_analysis, category_counts, output_folder):
    """Create visualizations for Artist analysis"""
    print("\n📊 Creating Artist Visualizations")
    print("-" * 35)
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. F-E-Line distribution
    fe_lines = df['F_E_Line'].value_counts().head(15)
    axes[0,0].bar(range(len(fe_lines)), fe_lines.values)
    axes[0,0].set_title('Top 15 F-E-Lines by Item Count')
    axes[0,0].set_xlabel('F-E-Line Rank')
    axes[0,0].set_ylabel('Item Count')
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # 2. Component categories
    categories = list(category_counts.keys())
    counts = list(category_counts.values())
    axes[0,1].pie(counts, labels=categories, autopct='%1.1f%%')
    axes[0,1].set_title('Component Categories Distribution')
    
    # 3. Description length distribution
    desc_lengths = df['Item_Description'].astype(str).str.len()
    axes[0,2].hist(desc_lengths, bins=30, alpha=0.7)
    axes[0,2].set_title('Description Length Distribution')
    axes[0,2].set_xlabel('Characters')
    axes[0,2].set_ylabel('Frequency')
    
    # 4. Top item IDs by frequency
    top_items = df['Item_ID'].value_counts().head(10)
    axes[1,0].barh(range(len(top_items)), top_items.values)
    axes[1,0].set_title('Top 10 Most Frequent Item IDs')
    axes[1,0].set_xlabel('Frequency')
    axes[1,0].set_yticks(range(len(top_items)))
    axes[1,0].set_yticklabels([str(item)[:15] + '...' if len(str(item)) > 15 else str(item) 
                              for item in top_items.index])
    
    # 5. F-E-Line complexity (number of unique items per F-E-Line)
    fe_line_complexity = df.groupby('F_E_Line')['Item_ID'].nunique().sort_values(ascending=False).head(10)
    axes[1,1].bar(range(len(fe_line_complexity)), fe_line_complexity.values)
    axes[1,1].set_title('F-E-Line Complexity (Unique Items)')
    axes[1,1].set_xlabel('F-E-Line Rank')
    axes[1,1].set_ylabel('Unique Items')
    
    # 6. Category vs F-E-Line heatmap (simplified)
    top_fe_lines = df['F_E_Line'].value_counts().head(5).index
    top_categories = df['Component_Category'].value_counts().head(5).index
    
    heatmap_data = []
    for fe_line in top_fe_lines:
        row = []
        fe_data = df[df['F_E_Line'] == fe_line]
        for category in top_categories:
            count = len(fe_data[fe_data['Component_Category'] == category])
            row.append(count)
        heatmap_data.append(row)
    
    im = axes[1,2].imshow(heatmap_data, cmap='Blues', aspect='auto')
    axes[1,2].set_title('Category vs F-E-Line Distribution')
    axes[1,2].set_xticks(range(len(top_categories)))
    axes[1,2].set_xticklabels(top_categories, rotation=45)
    axes[1,2].set_yticks(range(len(top_fe_lines)))
    axes[1,2].set_yticklabels(top_fe_lines)
    
    # Add colorbar
    plt.colorbar(im, ax=axes[1,2])
    
    plt.tight_layout()
    
    # Save visualization
    viz_file = os.path.join(output_folder, 'artist_product_analysis.png')
    plt.savefig(viz_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Visualization saved: {viz_file}")

def save_artist_analysis_results(df, structure_analysis, category_counts, fe_line_families, output_folder):
    """Save Artist analysis results"""
    print("\n💾 Saving Artist Analysis Results")
    print("-" * 35)
    
    # Save detailed BOM data
    df.to_excel(os.path.join(output_folder, 'artist_bom_analysis.xlsx'), index=False)
    
    # Save F-E-Line families
    families_data = []
    for family_name, family_info in fe_line_families.items():
        families_data.append({
            'Family_Name': family_name,
            'F_E_Line': family_info['fe_line'],
            'Item_Count': family_info['item_count'],
            'Family_Type': family_info['family_type'],
            'Dominant_Category': family_info['dominant_category'],
            'Description_Diversity': round(family_info['description_diversity'], 3),
            'Sample_Items': '; '.join(family_info['sample_items'])
        })
    
    families_df = pd.DataFrame(families_data)
    families_df.to_excel(os.path.join(output_folder, 'artist_fe_line_families.xlsx'), index=False)
    
    # Save comprehensive report
    with open(os.path.join(output_folder, 'artist_analysis_report.txt'), 'w', encoding='utf-8') as f:
        f.write("ARTIST PRODUCT BOM ANALYSIS REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("PRODUCT OVERVIEW\n")
        f.write("-" * 20 + "\n")
        f.write(f"Product Name: Artist\n")
        f.write(f"Total BOM Items: {structure_analysis['total_items']}\n")
        f.write(f"Unique Item IDs: {structure_analysis['unique_items']}\n")
        f.write(f"Unique Descriptions: {structure_analysis['unique_descriptions']}\n")
        f.write(f"Unique F-E-Lines: {structure_analysis['unique_fe_lines']}\n\n")
        
        f.write("COMPONENT CATEGORIES\n")
        f.write("-" * 20 + "\n")
        for category, count in category_counts.items():
            percentage = (count / structure_analysis['total_items']) * 100
            f.write(f"{category}: {count} items ({percentage:.1f}%)\n")
        f.write("\n")
        
        f.write("TOP F-E-LINE FAMILIES\n")
        f.write("-" * 25 + "\n")
        sorted_families = sorted(fe_line_families.items(), 
                               key=lambda x: x[1]['item_count'], reverse=True)
        
        for family_name, family_data in sorted_families[:15]:
            f.write(f"\n{family_data['fe_line']} ({family_data['family_type']})\n")
            f.write(f"  Items: {family_data['item_count']}\n")
            f.write(f"  Dominant Category: {family_data['dominant_category']}\n")
            f.write(f"  Sample Items: {', '.join(family_data['sample_items'][:3])}\n")
    
    print("✅ Analysis results saved:")
    print(f"   - artist_bom_analysis.xlsx")
    print(f"   - artist_fe_line_families.xlsx") 
    print(f"   - artist_analysis_report.txt")
    print(f"   - artist_product_analysis.png")

def main():
    """Main analysis function for Artist product"""
    print("🎨 ARTIST PRODUCT BOM ANALYSIS")
    print("=" * 60)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Load and clean data
    df_raw = analyze_artist_bom()
    if df_raw is None:
        return
    
    df_clean = clean_artist_data(df_raw)
    if df_clean is None:
        return
    
    # Analyze structure
    structure_analysis = analyze_artist_structure(df_clean)
    
    # Categorize components
    df_categorized, category_counts = categorize_artist_components(df_clean)
    
    # Analyze F-E-Line families
    fe_line_families = analyze_artist_fe_line_families(df_categorized)
    
    # Create visualizations
    create_artist_visualizations(df_categorized, structure_analysis, category_counts, output_folder)
    
    # Save results
    save_artist_analysis_results(df_categorized, structure_analysis, category_counts, 
                                fe_line_families, output_folder)
    
    print(f"\n🎉 Artist Product Analysis Complete!")
    print("=" * 60)
    print(f"📊 Analyzed {structure_analysis['total_items']} BOM items")
    print(f"👨‍👩‍👧‍👦 Found {len(fe_line_families)} F-E-Line families")
    print(f"🏷️ Categorized into {len(category_counts)} component types")
    print(f"📁 Results saved in: {os.path.abspath(output_folder)}")

if __name__ == "__main__":
    main()
