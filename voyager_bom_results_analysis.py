import pandas as pd
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt
import seaborn as sns
from voyager_bom_advanced_clustering import main as run_clustering

# RESULTS ANALYSIS AND VISUALIZATION

def save_clustering_results_to_excel(df_clean, all_results, best_models, removed_items):
    """Save all clustering results to Excel file"""
    
    with pd.ExcelWriter('output/voyager_bom_advanced_clustering_results.xlsx', engine='openpyxl') as writer:
        
        # Save cleaned data
        df_clean.to_excel(writer, sheet_name='Cleaned_BOM_Data', index=False)
        
        # Save removed items
        if len(removed_items) > 0:
            removed_items.to_excel(writer, sheet_name='Removed_Items', index=False)
        
        # Save clustering results for each approach
        for approach, results in all_results.items():
            
            # Create results summary
            summary_data = []
            for model_name, metrics in results.items():
                summary_data.append({
                    'Model': model_name,
                    'Algorithm': model_name.split('_')[0],
                    'N_Clusters': metrics['n_clusters'],
                    'Silhouette_Score': metrics['silhouette_score'],
                    'Calinski_Harabasz_Score': metrics['calinski_harabasz_score'],
                    'Davies_Bouldin_Score': metrics['davies_bouldin_score']
                })
            
            summary_df = pd.DataFrame(summary_data)
            summary_df = summary_df.sort_values('Silhouette_Score', ascending=False)
            summary_df.to_excel(writer, sheet_name=f'{approach.title()}_Summary', index=False)
            
            # Save best model cluster assignments
            best_model_name = best_models[approach]['model']
            if best_model_name:
                best_labels = results[best_model_name]['labels']
                
                cluster_df = df_clean.copy()
                cluster_df[f'{approach}_cluster'] = best_labels
                cluster_df.to_excel(writer, sheet_name=f'{approach.title()}_Clusters', index=False)
        
        # Save best models summary
        best_summary = []
        for approach, model_info in best_models.items():
            if model_info['metrics']:
                best_summary.append({
                    'Approach': approach,
                    'Best_Model': model_info['model'],
                    'N_Clusters': model_info['metrics']['n_clusters'],
                    'Silhouette_Score': model_info['metrics']['silhouette_score'],
                    'Calinski_Harabasz_Score': model_info['metrics']['calinski_harabasz_score'],
                    'Davies_Bouldin_Score': model_info['metrics']['davies_bouldin_score']
                })
        
        best_df = pd.DataFrame(best_summary)
        best_df = best_df.sort_values('Silhouette_Score', ascending=False)
        best_df.to_excel(writer, sheet_name='Best_Models_Summary', index=False)

def generate_similarity_matrices_for_best_models(df_clean, all_results, best_models):
    """Generate similarity matrices for best performing models"""
    
    from voyager_bom_advanced_clustering import (
        generate_sbert_embeddings, cluster_by_function, cluster_by_structure,
        cluster_by_physical_assembly, cluster_by_weight, cluster_by_material,
        cluster_by_combined_weight_material
    )
    
    # Regenerate embeddings
    base_embeddings = generate_sbert_embeddings(df_clean['Item Description'])
    
    similarity_matrices = {}
    
    # Function similarity matrix
    if 'function' in best_models and best_models['function']['model']:
        func_results, func_embeddings = cluster_by_function(df_clean, base_embeddings)
        best_func_model = best_models['function']['model']
        func_labels = func_results[best_func_model]['labels']
        
        func_sim_matrix = cosine_similarity(func_embeddings)
        similarity_matrices['function'] = {
            'matrix': func_sim_matrix,
            'labels': func_labels,
            'model': best_func_model
        }
    
    # Structure similarity matrix
    if 'structure' in best_models and best_models['structure']['model']:
        struct_results, struct_embeddings = cluster_by_structure(df_clean, base_embeddings)
        best_struct_model = best_models['structure']['model']
        struct_labels = struct_results[best_struct_model]['labels']
        
        struct_sim_matrix = cosine_similarity(struct_embeddings)
        similarity_matrices['structure'] = {
            'matrix': struct_sim_matrix,
            'labels': struct_labels,
            'model': best_struct_model
        }
    
    # Physical assembly similarity matrix
    if 'physical_assembly' in best_models and best_models['physical_assembly']['model']:
        assembly_results, assembly_embeddings = cluster_by_physical_assembly(df_clean, base_embeddings)
        best_assembly_model = best_models['physical_assembly']['model']
        assembly_labels = assembly_results[best_assembly_model]['labels']
        
        assembly_sim_matrix = cosine_similarity(assembly_embeddings)
        similarity_matrices['physical_assembly'] = {
            'matrix': assembly_sim_matrix,
            'labels': assembly_labels,
            'model': best_assembly_model
        }
    
    # Weight similarity matrix
    if 'weight' in best_models and best_models['weight']['model']:
        weight_results, weight_embeddings = cluster_by_weight(df_clean, base_embeddings)
        best_weight_model = best_models['weight']['model']
        weight_labels = weight_results[best_weight_model]['labels']
        
        weight_sim_matrix = cosine_similarity(weight_embeddings)
        similarity_matrices['weight'] = {
            'matrix': weight_sim_matrix,
            'labels': weight_labels,
            'model': best_weight_model
        }
    
    # Material similarity matrix
    if 'material' in best_models and best_models['material']['model']:
        material_results, material_embeddings = cluster_by_material(df_clean, base_embeddings)
        best_material_model = best_models['material']['model']
        material_labels = material_results[best_material_model]['labels']
        
        material_sim_matrix = cosine_similarity(material_embeddings)
        similarity_matrices['material'] = {
            'matrix': material_sim_matrix,
            'labels': material_labels,
            'model': best_material_model
        }
    
    # Combined weight-material similarity matrix
    if 'combined_weight_material' in best_models and best_models['combined_weight_material']['model']:
        weight_results, weight_embeddings = cluster_by_weight(df_clean, base_embeddings)
        material_results, material_embeddings = cluster_by_material(df_clean, base_embeddings)
        combined_results, combined_embeddings = cluster_by_combined_weight_material(df_clean, weight_embeddings, material_embeddings)
        
        best_combined_model = best_models['combined_weight_material']['model']
        combined_labels = combined_results[best_combined_model]['labels']
        
        combined_sim_matrix = cosine_similarity(combined_embeddings)
        similarity_matrices['combined_weight_material'] = {
            'matrix': combined_sim_matrix,
            'labels': combined_labels,
            'model': best_combined_model
        }
    
    return similarity_matrices

def save_similarity_matrices(similarity_matrices):
    """Save similarity matrices to files"""
    
    for approach, data in similarity_matrices.items():
        matrix = data['matrix']
        labels = data['labels']
        model = data['model']
        
        # Save full similarity matrix
        matrix_df = pd.DataFrame(matrix)
        matrix_df.to_csv(f'output/similarity_matrix_{approach}_{model}.csv', index=False)
        
        # Save cluster-level similarity summary
        unique_labels = np.unique(labels)
        cluster_similarity = np.zeros((len(unique_labels), len(unique_labels)))
        
        for i, label_i in enumerate(unique_labels):
            for j, label_j in enumerate(unique_labels):
                mask_i = labels == label_i
                mask_j = labels == label_j
                
                if np.any(mask_i) and np.any(mask_j):
                    cluster_similarity[i, j] = matrix[np.ix_(mask_i, mask_j)].mean()
        
        cluster_sim_df = pd.DataFrame(cluster_similarity, 
                                    index=[f'Cluster_{label}' for label in unique_labels],
                                    columns=[f'Cluster_{label}' for label in unique_labels])
        cluster_sim_df.to_csv(f'output/cluster_similarity_{approach}_{model}.csv')

def create_comprehensive_analysis_report(df_clean, removed_items, all_results, best_models):
    """Create comprehensive analysis report"""
    
    report_lines = []
    
    # Header
    report_lines.append("VOYAGER BOM ADVANCED CLUSTERING ANALYSIS REPORT")
    report_lines.append("=" * 60)
    report_lines.append("")
    
    # Data summary
    report_lines.append("DATA SUMMARY")
    report_lines.append("-" * 20)
    report_lines.append(f"Original BOM items: {len(df_clean) + len(removed_items)}")
    report_lines.append(f"Physical items analyzed: {len(df_clean)}")
    report_lines.append(f"Non-physical items removed: {len(removed_items)}")
    report_lines.append("")
    
    # Removed items categories
    if len(removed_items) > 0:
        report_lines.append("REMOVED ITEMS CATEGORIES")
        report_lines.append("-" * 30)
        
        exclusion_patterns = [
            ('manual|documentation|doc|guide|instruction', 'Documentation'),
            ('software|firmware|program|application', 'Software'),
            ('paper|label|sticker|decal', 'Labels/Papers'),
            ('accessory|accessories|optional|spare', 'Accessories'),
            ('packaging|box|container|bag', 'Packaging'),
            ('cable tie|zip tie|fastener|screw|bolt|nut', 'Small Fasteners'),
            ('warranty|service|maintenance', 'Service Items'),
            ('training|education|course', 'Training Materials'),
            ('license|subscription|agreement', 'Licenses')
        ]
        
        for pattern, category in exclusion_patterns:
            count = removed_items['Item Description'].str.contains(pattern, case=False, na=False).sum()
            if count > 0:
                report_lines.append(f"{category}: {count} items")
        
        report_lines.append("")
    
    # Best models summary
    report_lines.append("BEST PERFORMING MODELS")
    report_lines.append("-" * 30)
    
    for approach, model_info in best_models.items():
        if model_info['metrics']:
            metrics = model_info['metrics']
            report_lines.append(f"{approach.upper()}:")
            report_lines.append(f"  Model: {model_info['model']}")
            report_lines.append(f"  Clusters: {metrics['n_clusters']}")
            report_lines.append(f"  Silhouette Score: {metrics['silhouette_score']:.4f}")
            report_lines.append(f"  Calinski-Harabasz Score: {metrics['calinski_harabasz_score']:.4f}")
            report_lines.append(f"  Davies-Bouldin Score: {metrics['davies_bouldin_score']:.4f}")
            report_lines.append("")
    
    # Save report
    with open('output/voyager_bom_clustering_analysis_report.txt', 'w') as f:
        f.write('\n'.join(report_lines))

def main_analysis():
    """Main analysis execution"""
    
    print("RUNNING ADVANCED CLUSTERING ANALYSIS")
    df_clean, removed_items, all_results, best_models = run_clustering()
    
    print("SAVING RESULTS TO EXCEL")
    save_clustering_results_to_excel(df_clean, all_results, best_models, removed_items)
    
    print("GENERATING SIMILARITY MATRICES")
    similarity_matrices = generate_similarity_matrices_for_best_models(df_clean, all_results, best_models)
    
    print("SAVING SIMILARITY MATRICES")
    save_similarity_matrices(similarity_matrices)
    
    print("CREATING COMPREHENSIVE REPORT")
    create_comprehensive_analysis_report(df_clean, removed_items, all_results, best_models)
    
    print("ANALYSIS COMPLETED")
    print("Files generated:")
    print("- output/voyager_bom_advanced_clustering_results.xlsx")
    print("- output/similarity_matrix_*.csv")
    print("- output/cluster_similarity_*.csv")
    print("- output/voyager_bom_clustering_analysis_report.txt")
    
    return df_clean, removed_items, all_results, best_models, similarity_matrices

if __name__ == "__main__":
    df_clean, removed_items, all_results, best_models, similarity_matrices = main_analysis()
