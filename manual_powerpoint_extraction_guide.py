#!/usr/bin/env python3
"""
Manual PowerPoint Extraction Guide
Creates templates and guides for manual extraction of PowerPoint graph data
"""

import pandas as pd
import os
import csv

def create_extraction_template():
    """Create template for manual PowerPoint data extraction"""
    print("📋 Creating PowerPoint extraction template...")
    
    # Sample data to show expected format
    sample_data = [
        {
            'Slide_Number': 1,
            'Component_Text': 'Main Processing Unit',
            'Product_ID': '4516256',
            'Color_Observed': 'Blue',
            'Component_Type': 'Mandatory',
            'Position_Description': 'Top center of diagram',
            'Notes': 'Core component - always required'
        },
        {
            'Slide_Number': 1,
            'Component_Text': 'Display Module 15"',
            'Product_ID': 'DISP-15-001',
            'Color_Observed': 'Purple',
            'Component_Type': 'Mandatory_Selectable',
            'Position_Description': 'Right side of main unit',
            'Notes': 'User can select 15" or 17" variant'
        },
        {
            'Slide_Number': 1,
            'Component_Text': 'Extended Memory Kit',
            'Product_ID': 'MEM-EXT-512',
            'Color_Observed': 'Red',
            'Component_Type': 'Optional',
            'Position_Description': 'Bottom right corner',
            'Notes': 'Optional upgrade component'
        },
        {
            'Slide_Number': 2,
            'Component_Text': 'Software License Pro',
            'Product_ID': 'SW-LIC-PRO-2024',
            'Color_Observed': 'Purple',
            'Component_Type': 'Mandatory_Selectable',
            'Position_Description': 'Software section',
            'Notes': 'Pro or Standard license required'
        },
        {
            'Slide_Number': 2,
            'Component_Text': 'Cooling System',
            'Product_ID': 'COOL-SYS-001',
            'Color_Observed': 'Blue',
            'Component_Type': 'Mandatory',
            'Position_Description': 'Left side thermal section',
            'Notes': 'Required for thermal management'
        }
    ]
    
    # Add empty rows for manual input
    empty_rows = []
    for i in range(50):  # 50 empty rows for manual entry
        empty_row = {
            'Slide_Number': '',
            'Component_Text': '',
            'Product_ID': '',
            'Color_Observed': '',
            'Component_Type': '',
            'Position_Description': '',
            'Notes': ''
        }
        empty_rows.append(empty_row)
    
    # Combine sample and empty data
    template_data = sample_data + empty_rows
    
    return pd.DataFrame(template_data)

def create_color_reference():
    """Create color reference guide"""
    color_reference = [
        {
            'Color_Name': 'Blue',
            'Component_Type': 'Mandatory',
            'Description': 'Always required - core functionality',
            'Business_Impact': 'Critical - affects all systems',
            'ACV_Priority': 'High',
            'Circularity_Strategy': 'Standardization + Platform reuse'
        },
        {
            'Color_Name': 'Purple',
            'Component_Type': 'Mandatory_Selectable',
            'Description': 'Required but user can choose variant',
            'Business_Impact': 'High - affects configuration options',
            'ACV_Priority': 'Medium-High',
            'Circularity_Strategy': 'Modularization + Interchangeability'
        },
        {
            'Color_Name': 'Red',
            'Component_Type': 'Optional',
            'Description': 'Customer choice - not required for basic function',
            'Business_Impact': 'Medium - affects revenue opportunities',
            'ACV_Priority': 'Medium',
            'Circularity_Strategy': 'Secondary market + Upgrade path'
        },
        {
            'Color_Name': 'Light_Blue',
            'Component_Type': 'Mandatory_Variant',
            'Description': 'Required component with light blue shade',
            'Business_Impact': 'Critical - variant of mandatory',
            'ACV_Priority': 'High',
            'Circularity_Strategy': 'Standardization focus'
        },
        {
            'Color_Name': 'Orange',
            'Component_Type': 'Special_Category',
            'Description': 'Special category - needs investigation',
            'Business_Impact': 'Variable - depends on context',
            'ACV_Priority': 'Medium',
            'Circularity_Strategy': 'Case-by-case analysis'
        },
        {
            'Color_Name': 'Green',
            'Component_Type': 'Accessory',
            'Description': 'Accessory or peripheral component',
            'Business_Impact': 'Low-Medium - additional functionality',
            'ACV_Priority': 'Low',
            'Circularity_Strategy': 'Separate lifecycle management'
        },
        {
            'Color_Name': 'Gray',
            'Component_Type': 'Inactive',
            'Description': 'Inactive, placeholder, or discontinued',
            'Business_Impact': 'None - not active',
            'ACV_Priority': 'None',
            'Circularity_Strategy': 'Not applicable'
        }
    ]
    
    return pd.DataFrame(color_reference)

def create_extraction_instructions():
    """Create detailed extraction instructions"""
    instructions = """
POWERPOINT GRAPH EXTRACTION INSTRUCTIONS
========================================

OBJECTIVE:
Extract all components from PowerPoint graphs with their colors to create
architecture mapping for BOM clustering enhancement.

STEP-BY-STEP PROCESS:

1. OPEN POWERPOINT FILE:
   - File: Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pptx
   - Go through each slide systematically

2. IDENTIFY GRAPH SLIDES:
   - Look for slides with product architecture diagrams
   - Focus on slides with colored boxes/shapes containing text
   - Skip title slides, text-only slides, or pure presentation slides

3. FOR EACH COMPONENT BOX/SHAPE:
   a) Record the slide number
   b) Copy the text content exactly as shown
   c) Identify any Product ID (numbers, codes, part numbers)
   d) Observe the color carefully:
      - Blue = Mandatory component
      - Purple/Violet = Mandatory Selectable
      - Red = Optional
      - Other colors = Note the exact color
   e) Note position in diagram for reference
   f) Add any relevant notes

4. COLOR IDENTIFICATION TIPS:
   - Look at the FILL color of boxes/shapes
   - If no fill color, check border/outline color
   - If text-only, check text color
   - Use the legend/footprint on slides for reference
   - When in doubt, describe the color as you see it

5. PRODUCT ID EXTRACTION:
   - Look for numbers like: 4516256, 2576348, etc.
   - Look for codes like: SW-LIC-PRO, DISP-15-001, etc.
   - Look for part numbers in format: ABC-123-XYZ
   - If no clear ID, leave blank or note "No ID visible"

6. COMPONENT TYPE CLASSIFICATION:
   Based on color observed:
   - Blue → Mandatory
   - Purple/Violet → Mandatory_Selectable  
   - Red → Optional
   - Other → Describe what you see

7. QUALITY CHECKS:
   - Ensure each row has at least: Slide number, Component text, Color
   - Double-check color classifications
   - Verify Product IDs are correctly extracted
   - Add notes for any unclear cases

8. SAVE DATA:
   - Fill the Excel template: powerpoint_extraction_template.xlsx
   - Save your work frequently
   - Export final data as CSV when complete

COMMON PATTERNS TO LOOK FOR:
- Product architecture diagrams
- Component hierarchy charts
- System block diagrams
- Module interconnection diagrams
- Software/hardware component maps

WHAT TO SKIP:
- Pure text slides
- Title/agenda slides
- Slides without colored components
- Navigation elements
- Slide numbers/headers/footers

TROUBLESHOOTING:
- If color is unclear, describe it (e.g., "Light purple", "Dark blue")
- If text is partially visible, extract what you can see
- If multiple components in one shape, create separate rows
- If unsure about component type, note in comments

Remember: The goal is to capture the architecture logic encoded in colors
to enhance our BOM clustering analysis!
"""
    
    return instructions

def save_extraction_package(output_folder):
    """Save complete extraction package"""
    print("💾 Creating PowerPoint extraction package...")
    
    # Create template
    template_df = create_extraction_template()
    
    # Create color reference
    color_ref_df = create_color_reference()
    
    # Create instructions
    instructions = create_extraction_instructions()
    
    # Save Excel template with multiple sheets
    template_file = os.path.join(output_folder, 'powerpoint_extraction_template.xlsx')
    with pd.ExcelWriter(template_file, engine='openpyxl') as writer:
        # Main extraction template
        template_df.to_excel(writer, sheet_name='Component_Extraction', index=False)
        
        # Color reference
        color_ref_df.to_excel(writer, sheet_name='Color_Reference', index=False)
        
        # Instructions as data
        instructions_df = pd.DataFrame([{'Instructions': instructions}])
        instructions_df.to_excel(writer, sheet_name='Instructions', index=False)
    
    # Save instructions as text file
    instructions_file = os.path.join(output_folder, 'powerpoint_extraction_instructions.txt')
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    # Save CSV template
    csv_file = os.path.join(output_folder, 'powerpoint_extraction_template.csv')
    template_df.to_csv(csv_file, index=False)
    
    print(f"✅ Extraction package created:")
    print(f"   📊 Excel template: {os.path.basename(template_file)}")
    print(f"   📄 CSV template: {os.path.basename(csv_file)}")
    print(f"   📋 Instructions: {os.path.basename(instructions_file)}")
    
    return template_file, csv_file, instructions_file

def create_sample_csv_output():
    """Create sample CSV showing expected output format"""
    print("📄 Creating sample CSV output...")
    
    sample_output = [
        ['Slide_Number', 'Component_Text', 'Product_ID', 'Color_Observed', 'Component_Type'],
        [1, 'Main Processing Unit', '4516256', 'Blue', 'Mandatory'],
        [1, 'Display Module 15"', 'DISP-15-001', 'Purple', 'Mandatory_Selectable'],
        [1, 'Extended Memory Kit', 'MEM-EXT-512', 'Red', 'Optional'],
        [2, 'Software License Pro', 'SW-LIC-PRO-2024', 'Purple', 'Mandatory_Selectable'],
        [2, 'Cooling System', 'COOL-SYS-001', 'Blue', 'Mandatory'],
        [3, 'Accessory Kit A', 'ACC-KIT-A', 'Red', 'Optional'],
        [3, 'Interface Module', 'INT-MOD-001', 'Purple', 'Mandatory_Selectable'],
        [4, 'Power Supply Unit', 'PSU-750W', 'Blue', 'Mandatory'],
        [4, 'Backup Battery', 'BAT-BACKUP', 'Red', 'Optional'],
        [5, 'Control Software', 'SW-CTRL-V2', 'Blue', 'Mandatory']
    ]
    
    return sample_output

def main():
    """Create complete manual extraction package"""
    print("📊 POWERPOINT MANUAL EXTRACTION PACKAGE CREATOR")
    print("=" * 60)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Create extraction package
    template_file, csv_file, instructions_file = save_extraction_package(output_folder)
    
    # Create sample output
    sample_output = create_sample_csv_output()
    sample_csv = os.path.join(output_folder, 'sample_powerpoint_output.csv')
    with open(sample_csv, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(sample_output)
    
    print(f"\n🎉 Manual Extraction Package Complete!")
    print("=" * 60)
    print(f"📁 Package saved in: {os.path.abspath(output_folder)}")
    
    print(f"\n📋 Next Steps:")
    print("1. Open the PowerPoint file: Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pptx")
    print("2. Read the instructions: powerpoint_extraction_instructions.txt")
    print("3. Use the template: powerpoint_extraction_template.xlsx")
    print("4. Fill in component data following the color coding:")
    print("   - Blue = Mandatory")
    print("   - Purple = Mandatory_Selectable")
    print("   - Red = Optional")
    print("5. Save as CSV when complete")
    print("6. Use the CSV to enhance BOM clustering")
    
    print(f"\n🎯 Goal:")
    print("Extract every colored component from PowerPoint graphs to create")
    print("architecture mapping that will enhance BOM clustering with business logic!")

if __name__ == "__main__":
    main()
