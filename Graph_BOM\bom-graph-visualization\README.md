# BOM Graph Visualization

This project visualizes a Bill of Materials (BOM) using a graph representation. It leverages NetworkX for graph manipulation and Matplotlib for visualization, providing an intuitive way to understand the relationships between different components in the BOM.

## Project Structure

```
bom-graph-visualization
├── src
│   ├── main.py               # Entry point of the application
│   ├── graph_utils.py        # Utility functions for graph creation and manipulation
│   ├── data_loader.py        # Functions to load BOM data from files
│   ├── visualization.py       # Functions to visualize the graph
│   └── types
│       └── __init__.py       # Custom types for type safety
├── requirements.txt           # Project dependencies
└── README.md                  # Project documentation
```

## Installation

To set up the project, clone the repository and install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

1. **Load BOM Data**: Use the `data_loader.py` to load your BOM data from an Excel or CSV file.
2. **Create Graph**: Utilize the `graph_utils.py` to create a graph representation of the BOM data.
3. **Visualize Graph**: Call the `visualization.py` functions to render the graph, which will include labels and tooltips for better understanding.

## Example

Here is a simple example of how to use the project:

```python
from src.data_loader import load_data
from src.graph_utils import create_graph
from src.visualization import visualize_graph

# Load BOM data
data = load_data('path_to_your_bom_file.xlsx')

# Create graph from data
graph = create_graph(data)

# Visualize the graph
visualize_graph(graph)
```

## Contributing

Contributions are welcome! Please feel free to submit a pull request or open an issue for any suggestions or improvements.

## License

This project is licensed under the MIT License. See the LICENSE file for more details.