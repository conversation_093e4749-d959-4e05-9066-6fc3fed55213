import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt
from sbert_encoder import encode_descriptions

def create_bom_graph(df):
    G = nx.DiGraph()

    for _, row in df.iterrows():
        item_number = row['Item Number']
        item_description = row['Item Description']
        f_e_line = row['F-E-Line']
        level = row['Level']

        G.add_node(item_number, description=item_description, f_e_line=f_e_line, level=level)

        # Add edges based on the BOM structure (assuming a parent-child relationship)
        if level > 1:  # Assuming level 1 is the top-level item
            parent_level = level - 1
            parent_items = df[df['Level'] == parent_level]['Item Number'].tolist()
            for parent_item in parent_items:
                G.add_edge(parent_item, item_number)

    return G

def visualize_graph(G):
    pos = nx.spring_layout(G)
    plt.figure(figsize=(12, 8))
    nx.draw(G, pos, with_labels=True, node_size=2000, node_color='lightblue', font_size=10, font_weight='bold', arrows=True)
    labels = nx.get_node_attributes(G, 'description')
    nx.draw_networkx_labels(G, pos, labels, font_size=8)
    plt.title("Bill of Materials Graph Visualization")
    plt.show()

def encode_bom_descriptions(df):
    descriptions = df['Item Description'].tolist()
    encoded_descriptions = encode_descriptions(descriptions)
    return encoded_descriptions

def main():
    # Load your BOM data here
    df = pd.read_csv('path_to_your_bom_data.csv')  # Update with your actual data source
    df['Encoded Descriptions'] = encode_bom_descriptions(df)
    
    bom_graph = create_bom_graph(df)
    visualize_graph(bom_graph)

if __name__ == "__main__":
    main()