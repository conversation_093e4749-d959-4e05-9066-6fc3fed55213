# Core Data Science Libraries
pandas>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0
scipy>=1.10.0

# Excel File Processing
openpyxl>=3.1.0
xlrd>=2.0.1

# Text Processing & NLP
sentence-transformers>=2.2.0
transformers>=4.30.0
torch>=2.0.0
tokenizers>=0.13.0

# Graph Visualization & Network Analysis
networkx>=3.1
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
graphviz>=0.20.0

# Machine Learning & Clustering
umap-learn>=0.5.3
hdbscan>=0.8.29
faiss-cpu>=1.7.4

# Jupyter & Interactive Development
jupyter>=1.0.0
jupyterlab>=4.0.0
ipywidgets>=8.0.0
notebook>=6.5.0

# Utilities & Performance
tqdm>=4.65.0
joblib>=1.3.0
numba>=0.57.0

# Development & Testing
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.4.0

# Optional: For advanced text similarity
spacy>=3.6.0
nltk>=3.8.0

# Optional: For web interfaces
streamlit>=1.25.0
dash>=2.11.0

pdf2image
opencv-python
pytesseract
torch
ultralytics
networkx
numpy
Pillow

from pdf2image import convert_from_path

def load_pdf(path, dpi=300):
    """
    Convert PDF pages to PIL images.
    """
    images = convert_from_path(path, dpi=dpi, fmt='png')
    return images

import cv2
import numpy as np

def preprocess(img):
    """
    - Convert to grayscale
    - Binarize
    - Denoise
    """
    gray = cv2.cvtColor(np.array(img), cv2.COLOR_BGR2GRAY)
    _, bw = cv2.threshold(gray, 0, 255, cv2.THRESH_OTSU)
    denoised = cv2.medianBlur(bw, 3)
    return denoised

import torch
import cv2
import numpy as np

# Load a pre-trained or custom-trained YOLOv5 model
model = torch.hub.load('ultralytics/yolov5', 'custom', path='best.pt', trust_repo=True)

def detect_regions(img):
    """
    Runs YOLO inference; returns DataFrame-like list of detections:
      [{'label': 'box', 'xmin':…, 'ymin':…, 'xmax':…, 'ymax':…}, …]
    """
    results = model(img)  # CPU
    detections = []
    for *xyxy, conf, cls in results.xyxy[0]:
        label = model.names[int(cls)]
        x1, y1, x2, y2 = map(int, xyxy)
        detections.append({'label': label, 'xmin': x1, 'ymin': y1, 'xmax': x2, 'ymax': y2})
    return detections

import cv2
import numpy as np
import networkx as nx
from pytesseract import image_to_data

def mask_text(img):
    """
    Finds text via Tesseract, paints over it in white.
    """
    data = image_to_data(img, output_type='dict')
    for i, text in enumerate(data['text']):
        if text.strip():
            x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
            cv2.rectangle(img, (x, y), (x+w, y+h), (255,255,255), -1)
    return img

def compute_centroids(detections):
    """
    For each 'box' or 'satellite', compute its center point.
    """
    items = []
    for det in detections:
        cx = (det['xmin'] + det['xmax']) / 2
        cy = (det['ymin'] + det['ymax']) / 2
        items.append({'label': det['label'], 'centroid': (cx, cy)})
    return items

def build_hierarchy(items, threshold=100):
    """
    Simple nearest‑neighbor: if 'satellite' within threshold of 'box',
    assign as child; else top‑level.
    """
    G = nx.DiGraph()
    boxes = [i for i in items if i['label']=='box']
    sats = [i for i in items if i['label']=='satellite']
    for b in boxes:
        G.add_node(str(b['centroid']), label='box')
    for s in sats:
        G.add_node(str(s['centroid']), label='satellite')
        # find nearest box
        dists = [np.linalg.norm(np.array(s['centroid'])-np.array(b['centroid'])) for b in boxes]
        if dists:
            idx = np.argmin(dists)
            if dists[idx] < threshold:
                parent = str(boxes[idx]['centroid'])
                G.add_edge(parent, str(s['centroid']))
    return G

import sys
import networkx as nx
import json

def process(path):
    images = load_pdf(path)
    full_graphs = []

    for img in images:
        proc = preprocess(img)
        dets = detect_regions(proc)
        no_text = mask_text(proc.copy())
        items = compute_centroids(dets)
        G = build_hierarchy(items)
        # serialize as adjacency list
        data = nx.readwrite.json_graph.adjacency_data(G)
        full_graphs.append(data)

    # write out
    with open('architecture.json', 'w') as f:
        json.dump(full_graphs, f, indent=2)
    print("Saved architecture.json")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python main.py <path/to/diagram.pdf>")
        sys.exit(1)
    process(sys.argv[1])
