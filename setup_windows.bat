@echo off
echo 🔧 BOM Analysis Environment Setup for Windows
echo =============================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo Please install Python from https://python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python found:
python --version
echo.

REM Create virtual environment
echo 🔄 Creating virtual environment...
if exist "bom_analysis_env" (
    echo 📁 Virtual environment already exists
) else (
    python -m venv bom_analysis_env
    if %errorlevel% neq 0 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created successfully
)
echo.

REM Activate virtual environment and install packages
echo 🔄 Activating environment and installing packages...
call bom_analysis_env\Scripts\activate.bat

echo 🔄 Upgrading pip...
python -m pip install --upgrade pip

echo 🔄 Installing requirements...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install requirements
    echo.
    echo Trying to install core packages individually...
    pip install pandas numpy scikit-learn openpyxl matplotlib
    pip install sentence-transformers transformers torch
    pip install networkx seaborn plotly jupyter
)

echo.
echo 🔄 Setting up Jupyter kernel...
python -m ipykernel install --user --name=bom-analysis --display-name="BOM Analysis"

echo.
echo 🎉 Setup completed successfully!
echo.
echo 📋 To use the environment:
echo 1. Run: bom_analysis_env\Scripts\activate.bat
echo 2. Then: python bom_similarity_analysis.py
echo 3. Or: jupyter lab
echo.
echo 📁 Make sure to place your Excel files in the 'input' folder
echo.
pause
