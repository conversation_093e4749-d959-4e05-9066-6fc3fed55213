#!/usr/bin/env python3
"""
Advanced BOM Similarity Analysis
Builds upon the fast analysis with deeper insights, clustering, and advanced visualizations.
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import AgglomerativeClustering, DBSCAN
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
from scipy.spatial.distance import squareform
import matplotlib.pyplot as plt
import networkx as nx
import traceback
from datetime import datetime
import warnings
import pickle
import hashlib
import random
from collections import defaultdict, Counter
warnings.filterwarnings('ignore')

def load_cached_data():
    """Load the cached BOM data and similarity results"""
    cache_folder = 'cache'
    output_folder = 'output'
    
    # Load cached BOM data
    cache_data_file = os.path.join(cache_folder, 'bom_data.pkl')
    if not os.path.exists(cache_data_file):
        print("❌ No cached BOM data found. Please run fast_global_bom_analysis.py first.")
        return None, None
    
    try:
        with open(cache_data_file, 'rb') as f:
            bom_data = pickle.load(f)
        print(f"✅ Loaded cached BOM data: {len(bom_data)} files")
        
        # Load similarity results if available
        similarity_file = os.path.join(output_folder, 'fast_bom_similarity_analysis.xlsx')
        if os.path.exists(similarity_file):
            similarity_df = pd.read_excel(similarity_file, sheet_name='Similarities')
            print(f"✅ Loaded similarity results: {len(similarity_df)} similar pairs")
            return bom_data, similarity_df
        else:
            print("⚠️  No similarity results found. Will perform fresh analysis.")
            return bom_data, None
            
    except Exception as e:
        print(f"❌ Error loading cached data: {e}")
        return None, None

def analyze_similarity_patterns(similarity_df, bom_data):
    """Analyze patterns in the similarity data"""
    print("\n🔍 ANALYZING SIMILARITY PATTERNS")
    print("=" * 50)
    
    if similarity_df is None or len(similarity_df) == 0:
        print("❌ No similarity data to analyze")
        return {}
    
    patterns = {}
    
    # 1. Distribution of similarity scores
    print("📊 Similarity Score Distribution:")
    score_ranges = [
        (0.9, 1.0, "Exact matches (90-100%)"),
        (0.8, 0.9, "High similarity (80-90%)"),
        (0.7, 0.8, "Medium similarity (70-80%)"),
        (0.6, 0.7, "Low similarity (60-70%)")
    ]
    
    for min_score, max_score, label in score_ranges:
        count = len(similarity_df[(similarity_df['Similarity_Score'] >= min_score) & 
                                 (similarity_df['Similarity_Score'] < max_score)])
        percentage = (count / len(similarity_df)) * 100
        print(f"  {label}: {count:,} pairs ({percentage:.1f}%)")
        patterns[label] = count
    
    # 2. Cross-file vs Same-file analysis
    cross_file_count = similarity_df['Cross_File_Match'].sum()
    same_file_count = len(similarity_df) - cross_file_count
    print(f"\n📁 File Distribution:")
    print(f"  Cross-file matches: {cross_file_count:,} ({(cross_file_count/len(similarity_df)*100):.1f}%)")
    print(f"  Same-file matches: {same_file_count:,} ({(same_file_count/len(similarity_df)*100):.1f}%)")
    
    # 3. Level distribution analysis
    print(f"\n📊 Level Distribution:")
    level_combinations = similarity_df.groupby(['Level_1', 'Level_2']).size().sort_values(ascending=False)
    for (level1, level2), count in level_combinations.head(10).items():
        percentage = (count / len(similarity_df)) * 100
        print(f"  Level {level1} ↔ Level {level2}: {count:,} pairs ({percentage:.1f}%)")
    
    # 4. Most similar files
    print(f"\n📁 Most Connected Files:")
    file_connections = defaultdict(int)
    for _, row in similarity_df.iterrows():
        if row['Cross_File_Match']:
            file_connections[row['File_1']] += 1
            file_connections[row['File_2']] += 1
    
    top_files = sorted(file_connections.items(), key=lambda x: x[1], reverse=True)[:10]
    for file_name, connection_count in top_files:
        print(f"  {file_name[:50]}...: {connection_count:,} connections")
    
    # 5. Most frequently similar items
    print(f"\n🔧 Most Frequently Similar Items:")
    item_frequency = Counter()
    for _, row in similarity_df.iterrows():
        item_frequency[row['Item_ID_1']] += 1
        item_frequency[row['Item_ID_2']] += 1
    
    top_items = item_frequency.most_common(10)
    for item_id, frequency in top_items:
        print(f"  {item_id}: appears in {frequency:,} similar pairs")
    
    patterns['cross_file_ratio'] = cross_file_count / len(similarity_df)
    patterns['top_files'] = top_files[:5]
    patterns['top_items'] = top_items[:5]
    
    return patterns

def create_similarity_clusters(similarity_df, n_clusters=20):
    """Create clusters of similar items using advanced clustering"""
    print(f"\n🌳 CREATING SIMILARITY CLUSTERS")
    print("=" * 50)
    
    if similarity_df is None or len(similarity_df) == 0:
        print("❌ No similarity data for clustering")
        return None
    
    # Get unique items and their connections
    unique_items = set()
    item_connections = defaultdict(list)
    
    for _, row in similarity_df.iterrows():
        item1, item2 = row['Item_ID_1'], row['Item_ID_2']
        unique_items.add(item1)
        unique_items.add(item2)
        item_connections[item1].append((item2, row['Similarity_Score']))
        item_connections[item2].append((item1, row['Similarity_Score']))
    
    unique_items = list(unique_items)
    print(f"📊 Unique items in similarity network: {len(unique_items)}")
    
    if len(unique_items) < 3:
        print("❌ Not enough unique items for clustering")
        return None
    
    # Create similarity matrix for clustering
    print("🔍 Creating similarity matrix for clustering...")
    n_items = min(len(unique_items), 1000)  # Limit for performance
    selected_items = unique_items[:n_items]
    
    similarity_matrix = np.zeros((n_items, n_items))
    np.fill_diagonal(similarity_matrix, 1.0)
    
    # Fill similarity matrix
    item_to_idx = {item: idx for idx, item in enumerate(selected_items)}
    
    for _, row in similarity_df.iterrows():
        item1, item2 = row['Item_ID_1'], row['Item_ID_2']
        if item1 in item_to_idx and item2 in item_to_idx:
            idx1, idx2 = item_to_idx[item1], item_to_idx[item2]
            similarity_matrix[idx1, idx2] = row['Similarity_Score']
            similarity_matrix[idx2, idx1] = row['Similarity_Score']
    
    # Perform hierarchical clustering
    print("🔗 Performing hierarchical clustering...")
    distance_matrix = 1 - similarity_matrix
    condensed_distances = squareform(distance_matrix, checks=False)
    linkage_matrix = linkage(condensed_distances, method='ward')
    
    # Get cluster labels
    cluster_labels = fcluster(linkage_matrix, n_clusters, criterion='maxclust')
    
    # Organize clusters
    clusters = defaultdict(list)
    for idx, cluster_id in enumerate(cluster_labels):
        clusters[cluster_id].append(selected_items[idx])
    
    # Filter to significant clusters
    significant_clusters = {k: v for k, v in clusters.items() if len(v) > 1}
    
    print(f"✅ Created {len(significant_clusters)} clusters with multiple items")
    
    # Show cluster summary
    cluster_sizes = sorted([len(cluster) for cluster in significant_clusters.values()], reverse=True)
    print(f"📊 Cluster sizes: {cluster_sizes[:10]}")
    
    return {
        'clusters': significant_clusters,
        'similarity_matrix': similarity_matrix,
        'linkage_matrix': linkage_matrix,
        'selected_items': selected_items
    }

def create_network_analysis(similarity_df, min_similarity=0.8):
    """Create network analysis of item similarities"""
    print(f"\n🕸️  NETWORK ANALYSIS")
    print("=" * 50)
    
    if similarity_df is None or len(similarity_df) == 0:
        print("❌ No similarity data for network analysis")
        return None
    
    # Filter high-similarity connections
    high_sim_df = similarity_df[similarity_df['Similarity_Score'] >= min_similarity]
    print(f"📊 High similarity connections (≥{min_similarity}): {len(high_sim_df):,}")
    
    if len(high_sim_df) == 0:
        print(f"❌ No connections above similarity threshold {min_similarity}")
        return None
    
    # Create network graph
    print("🔍 Creating similarity network...")
    G = nx.Graph()
    
    # Add edges with similarity as weight
    for _, row in high_sim_df.iterrows():
        G.add_edge(row['Item_ID_1'], row['Item_ID_2'], 
                  weight=row['Similarity_Score'],
                  cross_file=row['Cross_File_Match'])
    
    print(f"📊 Network statistics:")
    print(f"  Nodes (unique items): {G.number_of_nodes():,}")
    print(f"  Edges (connections): {G.number_of_edges():,}")
    
    # Calculate network metrics
    if G.number_of_nodes() > 0:
        # Connected components
        components = list(nx.connected_components(G))
        print(f"  Connected components: {len(components)}")
        
        if len(components) > 0:
            largest_component_size = max(len(comp) for comp in components)
            print(f"  Largest component size: {largest_component_size}")
        
        # Degree centrality (most connected items)
        if G.number_of_nodes() <= 1000:  # Only for manageable sizes
            centrality = nx.degree_centrality(G)
            top_central = sorted(centrality.items(), key=lambda x: x[1], reverse=True)[:10]
            
            print(f"🔗 Most connected items:")
            for item, centrality_score in top_central:
                degree = G.degree(item)
                print(f"  {item}: {degree} connections (centrality: {centrality_score:.3f})")
    
    return {
        'graph': G,
        'components': components,
        'high_similarity_df': high_sim_df
    }

def create_advanced_visualizations(similarity_df, cluster_results, network_results, output_folder):
    """Create advanced visualizations"""
    print(f"\n📊 CREATING ADVANCED VISUALIZATIONS")
    print("=" * 50)
    
    # 1. Similarity Score Distribution
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.hist(similarity_df['Similarity_Score'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('Distribution of Similarity Scores')
    plt.xlabel('Similarity Score')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)
    
    # 2. Cross-file vs Same-file comparison
    plt.subplot(2, 2, 2)
    cross_file_counts = similarity_df['Cross_File_Match'].value_counts()
    labels = ['Same File', 'Cross File']
    colors = ['lightcoral', 'lightgreen']
    plt.pie(cross_file_counts.values, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    plt.title('Cross-File vs Same-File Matches')
    
    # 3. Similarity by Level combinations
    plt.subplot(2, 2, 3)
    level_combinations = similarity_df.groupby(['Level_1', 'Level_2']).size().sort_values(ascending=False).head(10)
    level_labels = [f"L{l1}↔L{l2}" for l1, l2 in level_combinations.index]
    plt.bar(range(len(level_combinations)), level_combinations.values, color='orange', alpha=0.7)
    plt.title('Top Level Combinations')
    plt.xlabel('Level Combinations')
    plt.ylabel('Number of Matches')
    plt.xticks(range(len(level_combinations)), level_labels, rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 4. Similarity Score vs Cross-file scatter
    plt.subplot(2, 2, 4)
    cross_file_data = similarity_df[similarity_df['Cross_File_Match']]
    same_file_data = similarity_df[~similarity_df['Cross_File_Match']]
    
    if len(cross_file_data) > 0:
        plt.scatter(cross_file_data['Similarity_Score'], 
                   np.random.normal(1, 0.1, len(cross_file_data)), 
                   alpha=0.5, label='Cross-File', s=1)
    if len(same_file_data) > 0:
        plt.scatter(same_file_data['Similarity_Score'], 
                   np.random.normal(0, 0.1, len(same_file_data)), 
                   alpha=0.5, label='Same-File', s=1)
    
    plt.title('Similarity Scores by File Type')
    plt.xlabel('Similarity Score')
    plt.ylabel('File Type')
    plt.yticks([0, 1], ['Same File', 'Cross File'])
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, 'advanced_similarity_analysis.png'), dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ Saved: advanced_similarity_analysis.png")
    
    # 5. Network visualization (if network results available)
    if network_results and network_results['graph'].number_of_nodes() <= 100:
        plt.figure(figsize=(15, 10))
        G = network_results['graph']
        
        # Use spring layout for better visualization
        pos = nx.spring_layout(G, k=1, iterations=50)
        
        # Draw edges
        nx.draw_networkx_edges(G, pos, alpha=0.5, width=0.5, edge_color='gray')
        
        # Draw nodes
        node_sizes = [G.degree(node) * 20 + 50 for node in G.nodes()]
        nx.draw_networkx_nodes(G, pos, node_size=node_sizes, 
                              node_color='lightblue', alpha=0.8)
        
        # Draw labels for high-degree nodes only
        high_degree_nodes = [node for node in G.nodes() if G.degree(node) > 3]
        labels = {node: str(node)[:10] for node in high_degree_nodes}
        nx.draw_networkx_labels(G, pos, labels, font_size=8)
        
        plt.title('Item Similarity Network\n(Node size = number of connections)')
        plt.axis('off')
        plt.tight_layout()
        plt.savefig(os.path.join(output_folder, 'similarity_network.png'), dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ Saved: similarity_network.png")

def main():
    print("🚀 Starting Advanced BOM Similarity Analysis")
    print("=" * 70)
    print(f"⏰ Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Load cached data and previous results
    bom_data, similarity_df = load_cached_data()
    
    if bom_data is None:
        return
    
    if similarity_df is None:
        print("❌ No similarity results found. Please run fast_global_bom_analysis.py first.")
        return
    
    print(f"📊 Starting advanced analysis on {len(similarity_df):,} similar pairs...")
    
    # 1. Analyze similarity patterns
    patterns = analyze_similarity_patterns(similarity_df, bom_data)
    
    # 2. Create advanced clusters
    cluster_results = create_similarity_clusters(similarity_df, n_clusters=25)
    
    # 3. Network analysis
    network_results = create_network_analysis(similarity_df, min_similarity=0.8)
    
    # 4. Create advanced visualizations
    create_advanced_visualizations(similarity_df, cluster_results, network_results, output_folder)
    
    print(f"\n🎉 Advanced BOM Similarity Analysis Complete!")
    print("=" * 60)
    print(f"⏰ End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Advanced reports saved to: {os.path.abspath(output_folder)}")
    print("\n📋 Generated advanced analysis files:")
    print("  • advanced_similarity_analysis.png - Comprehensive visualization dashboard")
    print("  • similarity_network.png - Network graph of item connections")

if __name__ == "__main__":
    main()
