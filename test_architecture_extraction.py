#!/usr/bin/env python3
"""
Test script for ColPali Architecture Extraction
"""

import os
import sys

# Add colpali_architecture to path
sys.path.append('colpali_architecture')

from colpali_architecture.main import process_document

def test_extraction():
    """
    Test the architecture extraction pipeline
    """
    print("Testing ColPali Architecture Extraction Pipeline")
    print("=" * 60)
    
    # Test with PIM document
    pdf_path = r"input\PIM\3.0T Signa PET MR.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"Test file not found: {pdf_path}")
        print("Available PIM files:")
        pim_folder = r"input\PIM"
        if os.path.exists(pim_folder):
            for file in os.listdir(pim_folder):
                if file.endswith('.pdf'):
                    print(f"  - {file}")
        return False
    
    print(f"Testing with: {pdf_path}")
    print("Processing first 3 pages for testing...")
    
    try:
        # Process document (limit to 3 pages for testing)
        results = process_document(pdf_path, output_dir='output/architecture', max_pages=3)
        
        if results:
            print("\nTest Results:")
            print(f"  Pages processed: {results['processed_pages']}")
            print(f"  Total detections: {sum(s['total_detections'] for s in results['page_summaries'])}")
            print(f"  Total boxes: {sum(s['boxes'] for s in results['page_summaries'])}")
            print(f"  Total satellites: {sum(s['satellites'] for s in results['page_summaries'])}")
            
            print("\nFiles generated:")
            print("  - output/architecture/architecture_extraction.json")
            print("  - output/architecture/extraction_summary.txt")
            print("  - output/architecture/graph_page_*.png (if matplotlib available)")
            
            return True
        else:
            print("Test failed - no results generated")
            return False
            
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """
    Check if required dependencies are installed
    """
    print("Checking dependencies...")
    
    required_packages = [
        'pdf2image',
        'cv2',
        'torch',
        'ultralytics',
        'networkx',
        'numpy',
        'PIL'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                from PIL import Image
            else:
                __import__(package)
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ✗ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {missing_packages}")
        print("Install with: pip install -r colpali_architecture/requirements.txt")
        return False
    else:
        print("All dependencies available!")
        return True

def main():
    """
    Main test execution
    """
    print("ColPali Architecture Extraction - Test Suite")
    print("=" * 70)
    
    # Check dependencies
    if not check_dependencies():
        print("Please install missing dependencies before testing")
        return
    
    print()
    
    # Run extraction test
    success = test_extraction()
    
    if success:
        print("\n✓ Test completed successfully!")
        print("The architecture extraction pipeline is working correctly.")
    else:
        print("\n✗ Test failed!")
        print("Please check error messages above.")

if __name__ == "__main__":
    main()
