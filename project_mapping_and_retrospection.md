# BOM Cluster Analysis Project - Complete Mapping & Retrospection

## Project Overview
**Project Name**: BOM Similarity Analysis and Product Family Clustering  
**Objective**: Analyze BOM (Bill of Materials) files to find similarities, create hierarchical graphs, and identify product families  
**Date Range**: 2025-01-06  
**Total Files Created**: 15+ analysis scripts + multiple output files  

---

## File Structure & Mapping

###  Input Data
```
input/
├── *.xlsx files (28 BOM Excel files)
└── Structure: Sheet 2 contains BOM data
    ├── Column 1: F-E-Line (Level hierarchy like 1-1-2, 2-3-1)
    ├── Column 2: Sequence 
    ├── Column 3: Item_ID (actual part numbers)
    └── Column 4: Item_Description
```

###  Analysis Scripts (Latest Versions)

#### 🔍 **1. Data Quality & Exploration**
| File | Purpose | Status | Key Features |
|------|---------|--------|--------------|
| `quick_data_check.py` | Initial data structure inspection | ✅ Working | Shows actual column content, identifies F-E-Line format |
| `analyze_fe_line_patterns.py` | Analyze F-E-Line patterns across files | ✅ Working | Discovers unique patterns like 1-1-X format |
| `data_quality_check.py` | Comprehensive data validation | ⚠️ Needs Excel fix | Validates similarity results accuracy |

#### 🔄 **2. Similarity Analysis (Evolution)**
| File | Purpose | Status | Notes |
|------|---------|--------|-------|
| `fast_global_bom_analysis.py` | Initial similarity analysis | ❌ Incorrect | Used wrong column mapping (Item ID vs F-E-Line) |
| `corrected_bom_analysis.py` | Fixed column mapping | ✅ Working | Corrected to use F-E-Line as Item ID |
| `comprehensive_match_analysis.py` | Complete pairwise analysis | ⚠️ Large dataset | 49M+ matches, exceeded Excel limits |
| `complete_pairwise_similarity.py` | **FINAL SIMILARITY ANALYSIS** | ✅ **BEST** | Naive double for-loop, 176,369 matches found |

####  **3. Level-by-Level Analysis**
| File | Purpose | Status | Key Insights |
|------|---------|--------|--------------|
| `level_by_level_analysis.py` | Analyze matches by BOM level | ✅ Working | Shows standardization patterns by level |

####  **4. Description Similarity**
| File | Purpose | Status | Features |
|------|---------|--------|----------|
| `description_similarity_analysis.py` | Text similarity analysis | ✅ Working | TF-IDF + 50% threshold, 211K+ pairs |
| `efficient_match_analysis.py` | Manageable similarity results | ✅ Working | Handles large datasets efficiently |

####  **5. Graph Analysis (Evolution)**
| File | Purpose | Status | Notes |
|------|---------|--------|-------|
| `bom_graph_analysis.py` | Initial graph attempt | ❌ Wrong interpretation | Misunderstood F-E-Line concept |
| `corrected_bom_graph_analysis.py` | Fixed F-E-Line interpretation | ❌ Still wrong | Assumed 1-1-X format only |
| `correct_fe_line_graph_analysis.py` | Sequential F-E-Line processing | ⚠️ Partial | Better but incomplete |
| `final_correct_bom_graph.py` | **FINAL GRAPH ANALYSIS** | ✅ **BEST** | Proper F-E-Line subgraphs with branching |

####  **6. Product Family Clustering**
| File | Purpose | Status | Features |
|------|---------|--------|----------|
| `product_family_clustering.py` | **FINAL CLUSTERING ANALYSIS** | ✅ **LATEST** | Combines graphs + similarity for product families |

---

##  Output Files Generated

###  output/
```
output/
├── 📈 Similarity Analysis Results
│   ├── complete_pairwise_similarity_analysis.xlsx (176,369 matches)
│   ├── perfect_matches_sample.xlsx
│   ├── description_matches_sample.xlsx
│   └── comprehensive_analysis_summary.xlsx
│
├──  Level Analysis
│   ├── level_by_level_analysis.xlsx
│   └── level_by_level_summary.txt
│
├──  Graph Structures  
│   ├── fe_line_bom_graph_*.png (Hierarchical visualizations)
│   ├── fe_line_graph_data_*.json (Graph structure data)
│   └── fe_line_pattern_analysis.txt
│
├──  Product Families
│   ├── product_family_components.xlsx
│   ├── product_families_summary.xlsx
│   ├── product_family_clustering_analysis.png
│   └── product_family_clustering_summary.txt
│
└── 📋 Cache & Intermediate
    ├── cache/bom_data.pkl (Preprocessed BOM data)
    └── Various summary and analysis files
```

---

## Key Discoveries & Learnings

###  **Critical Corrections Made**
1. **F-E-Line Definition**: Initially confused F-E-Line with Item ID. **F-E-Line = Level column (1st column)** with hierarchical format
2. **Column Mapping**: Corrected from wrong assumption to proper structure:
   - Column 1: **F-E-Line** (Level hierarchy)
   - Column 2: Sequence  
   - Column 3: **Item_ID** (actual part numbers)
   - Column 4: Item_Description
3. **Graph Structure**: F-E-Line creates subgraphs, not individual nodes

###  **Major Findings**
- **412,299 total items** analyzed across 28 BOM files
- **176,369 similarity matches** found (50%+ threshold)
- **49+ million perfect matches** (same ID + same description)
- **81 F-E-Line components** with unique patterns
- **8 product families** identified through clustering

###  **Graph Structure Understanding**
- Each unique F-E-Line value creates its own subgraph
- Within F-E-Line: hierarchy based on Level numbers (1, 2, 3...)
- Levels can jump (start at 2, 3) and return to ANY previous level
- Returns create new branches in the hierarchy

---

##  **Best/Final Files to Use**

### For Similarity Analysis:
**`complete_pairwise_similarity.py`** - Most accurate, complete pairwise comparison

### For Graph Analysis:
**`final_correct_bom_graph.py`** - Proper F-E-Line subgraphs with branching

### For Product Families:
**`product_family_clustering.py`** - Combines everything for product family identification

### For Data Exploration:
**`analyze_fe_line_patterns.py`** - Understand F-E-Line patterns in your data

---

## 🔄 **Workflow for Future Analysis**

### 1. **Data Preparation**
```bash
py analyze_fe_line_patterns.py  # Understand your F-E-Line patterns
```

### 2. **Similarity Analysis**
```bash
py complete_pairwise_similarity.py  # Find all similar items
```

### 3. **Graph Structure**
```bash
py final_correct_bom_graph.py  # Create F-E-Line hierarchical graphs
```

### 4. **Product Families**
```bash
py product_family_clustering.py  # Identify product families
```

---

##  **Key Concepts Established**

### **F-E-Line (Level Column)**
- **Definition**: First column with hierarchical format (e.g., 1-1-2, 2-3-1)
- **Purpose**: Creates subgraph structure in BOM hierarchy
- **Behavior**: Each unique F-E-Line = separate tree/subgraph

### **Similarity Types Found**
1. **Perfect Matches**: Same Item ID + Same Description (cross-file duplicates)
2. **Description Matches**: Same Description + Different Item ID (alternatives)
3. **Similar Descriptions**: 50%+ text similarity (potential substitutes)

### **Product Family Characteristics**
- **Complex Assembly**: High complexity + many transitions
- **Simple Component**: Low levels + few items  
- **Major Assembly**: High item count (>1000)
- **Branched Structure**: Many level returns
- **Standard Component**: Default category

---

##  **Next Steps & Future Work**

### **Immediate Opportunities**
1. **Expand Analysis**: Process all 28 files (currently limited to 2-5 for demo)
2. **Refine Clustering**: Tune clustering parameters for better product families
3. **Validation**: Manual validation of identified product families
4. **Integration**: Combine with business rules and domain knowledge

### **Advanced Features**
1. **Cost Analysis**: Add cost data to similarity analysis
2. **Supplier Analysis**: Include supplier information in clustering
3. **Time Series**: Track BOM evolution over time
4. **Optimization**: Suggest standardization opportunities

### **Technical Improvements**
1. **Performance**: Optimize for larger datasets
2. **Visualization**: Interactive dashboards
3. **Export**: Integration with PLM/ERP systems
4. **Automation**: Scheduled analysis pipeline

---

## **Contact & Maintenance**
- **Created**: 2025-01-06
- **Last Updated**: 2025-01-06  
- **Status**: Active Development
- **Dependencies**: pandas, numpy, sklearn, matplotlib, networkx, openpyxl

---

*This mapping serves as a complete guide to navigate through all the analysis work completed and provides a clear path for future development and analysis.*

import sys
from pdf_loader import load_pdf
from preprocessor import preprocess
from detector import detect_regions
from extractor import mask_text, compute_centroids, build_hierarchy
import networkx as nx
import json

def process(path):
    images = load_pdf(path)
    full_graphs = []

    for img in images:
        proc = preprocess(img)
        dets = detect_regions(proc)
        no_text = mask_text(proc.copy())
        items = compute_centroids(dets)
        G = build_hierarchy(items)
        # serialize as adjacency list
        data = nx.readwrite.json_graph.adjacency_data(G)
        full_graphs.append(data)

    # write out
    with open('architecture.json', 'w') as f:
        json.dump(full_graphs, f, indent=2)
    print("Saved architecture.json")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python main.py <path/to/diagram.pdf>")
        sys.exit(1)
    process(sys.argv[1])
