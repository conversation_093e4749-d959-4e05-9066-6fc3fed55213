#!/usr/bin/env python3
"""
Analyze F-E-Line Patterns
First, let's see what the actual Level column (F-E-Line) formats look like in your data.
"""

import os
import pandas as pd
from collections import Counter
import re

def analyze_fe_line_patterns():
    """Analyze all the different F-E-Line patterns in the data"""
    print("🔍 Analyzing F-E-Line (Level column) patterns in your data...")
    
    input_folder = 'input'
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    
    all_fe_line_patterns = []
    file_patterns = {}
    
    for i, file in enumerate(excel_files[:5], 1):  # Check first 5 files
        print(f"\n[{i}/5] Analyzing: {file}")
        file_path = os.path.join(input_folder, file)
        
        try:
            # Read the second sheet
            xls = pd.ExcelFile(file_path)
            if len(xls.sheet_names) < 2:
                print(f"    ⚠️  No second sheet")
                continue
                
            df = pd.read_excel(file_path, sheet_name=1)
            
            if len(df.columns) < 4:
                print(f"    ⚠️  Not enough columns")
                continue
            
            # Get the first column (F-E-Line / Level)
            level_column = df.iloc[:, 0]
            level_column = level_column.dropna()
            level_column = level_column[level_column.astype(str).str.strip() != '']
            
            print(f"    📊 Total Level entries: {len(level_column)}")
            
            # Get unique patterns
            unique_patterns = level_column.astype(str).unique()
            print(f"    📊 Unique F-E-Line patterns: {len(unique_patterns)}")
            
            # Show sample patterns
            print(f"    📋 Sample patterns:")
            for j, pattern in enumerate(unique_patterns[:10]):
                print(f"      {j+1:2d}. '{pattern}'")
            
            if len(unique_patterns) > 10:
                print(f"      ... and {len(unique_patterns)-10} more patterns")
            
            # Analyze pattern types
            pattern_types = {
                'numeric_only': [],
                'dash_separated': [],
                'dot_separated': [],
                'mixed_format': [],
                'other': []
            }
            
            for pattern in unique_patterns:
                pattern_str = str(pattern).strip()
                
                if re.match(r'^\d+$', pattern_str):
                    pattern_types['numeric_only'].append(pattern_str)
                elif '-' in pattern_str and re.match(r'^[\d\-]+$', pattern_str):
                    pattern_types['dash_separated'].append(pattern_str)
                elif '.' in pattern_str and re.match(r'^[\d\.]+$', pattern_str):
                    pattern_types['dot_separated'].append(pattern_str)
                elif re.match(r'^[\d\-\.]+$', pattern_str):
                    pattern_types['mixed_format'].append(pattern_str)
                else:
                    pattern_types['other'].append(pattern_str)
            
            print(f"    🔍 Pattern type breakdown:")
            for ptype, patterns in pattern_types.items():
                if patterns:
                    print(f"      {ptype}: {len(patterns)} patterns")
                    # Show examples
                    examples = patterns[:3]
                    print(f"        Examples: {examples}")
            
            file_patterns[file] = {
                'total_entries': len(level_column),
                'unique_patterns': len(unique_patterns),
                'pattern_types': {k: len(v) for k, v in pattern_types.items()},
                'sample_patterns': unique_patterns[:20].tolist()
            }
            
            all_fe_line_patterns.extend(unique_patterns)
            
        except Exception as e:
            print(f"    ❌ Error: {e}")
            continue
    
    # Overall analysis
    print(f"\n📊 OVERALL F-E-LINE PATTERN ANALYSIS")
    print("=" * 50)
    
    all_unique_patterns = list(set(all_fe_line_patterns))
    print(f"Total unique F-E-Line patterns across all files: {len(all_unique_patterns)}")
    
    # Count pattern frequencies
    pattern_counter = Counter(all_fe_line_patterns)
    most_common = pattern_counter.most_common(20)
    
    print(f"\n🔝 Top 20 most frequent F-E-Line patterns:")
    for i, (pattern, count) in enumerate(most_common, 1):
        print(f"  {i:2d}. '{pattern}' appears {count} times")
    
    # Analyze overall pattern types
    overall_pattern_types = {
        'numeric_only': [],
        'dash_separated': [],
        'dot_separated': [],
        'mixed_format': [],
        'alphanumeric': [],
        'other': []
    }
    
    for pattern in all_unique_patterns:
        pattern_str = str(pattern).strip()
        
        if re.match(r'^\d+$', pattern_str):
            overall_pattern_types['numeric_only'].append(pattern_str)
        elif '-' in pattern_str and re.match(r'^[\d\-]+$', pattern_str):
            overall_pattern_types['dash_separated'].append(pattern_str)
        elif '.' in pattern_str and re.match(r'^[\d\.]+$', pattern_str):
            overall_pattern_types['dot_separated'].append(pattern_str)
        elif re.match(r'^[\d\-\.]+$', pattern_str):
            overall_pattern_types['mixed_format'].append(pattern_str)
        elif re.match(r'^[a-zA-Z0-9\-\.]+$', pattern_str):
            overall_pattern_types['alphanumeric'].append(pattern_str)
        else:
            overall_pattern_types['other'].append(pattern_str)
    
    print(f"\n🔍 OVERALL PATTERN TYPE BREAKDOWN:")
    for ptype, patterns in overall_pattern_types.items():
        if patterns:
            print(f"  {ptype}: {len(patterns)} unique patterns")
            # Show examples
            examples = sorted(patterns)[:5]
            print(f"    Examples: {examples}")
    
    # Save detailed analysis
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    with open(os.path.join(output_folder, 'fe_line_pattern_analysis.txt'), 'w', encoding='utf-8') as f:
        f.write("F-E-LINE (LEVEL COLUMN) PATTERN ANALYSIS\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("FILE-BY-FILE ANALYSIS\n")
        f.write("=" * 25 + "\n")
        for file, data in file_patterns.items():
            f.write(f"\nFile: {file}\n")
            f.write(f"  Total entries: {data['total_entries']}\n")
            f.write(f"  Unique patterns: {data['unique_patterns']}\n")
            f.write(f"  Pattern types: {data['pattern_types']}\n")
            f.write(f"  Sample patterns: {data['sample_patterns'][:10]}\n")
        
        f.write(f"\n\nOVERALL ANALYSIS\n")
        f.write("=" * 20 + "\n")
        f.write(f"Total unique patterns: {len(all_unique_patterns)}\n\n")
        
        f.write("Most frequent patterns:\n")
        for i, (pattern, count) in enumerate(most_common, 1):
            f.write(f"  {i:2d}. '{pattern}' ({count} times)\n")
        
        f.write(f"\nPattern type breakdown:\n")
        for ptype, patterns in overall_pattern_types.items():
            if patterns:
                f.write(f"  {ptype}: {len(patterns)} patterns\n")
                f.write(f"    Examples: {sorted(patterns)[:10]}\n")
    
    print(f"\n✅ Detailed analysis saved to: output/fe_line_pattern_analysis.txt")
    
    return overall_pattern_types, file_patterns

def main():
    print("🚀 Starting F-E-Line Pattern Analysis")
    print("=" * 50)
    print("🎯 Let's see what your actual Level column formats look like!")
    
    pattern_types, file_patterns = analyze_fe_line_patterns()
    
    print(f"\n🎉 F-E-Line Pattern Analysis Complete!")
    print("=" * 50)
    print("💡 Now I understand your actual F-E-Line formats!")
    print("📁 Check output/fe_line_pattern_analysis.txt for detailed breakdown")

if __name__ == "__main__":
    main()
