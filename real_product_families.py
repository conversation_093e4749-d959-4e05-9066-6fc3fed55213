#!/usr/bin/env python3
"""
Real Product Families Creation
Uses actual BOM data, real F-E-Line structures, and true similarity matches
to create authentic product families based on real component relationships
"""

import os
import pandas as pd
import numpy as np
import json
from collections import defaultdict, Counter
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import AgglomerativeClustering, DBSCAN
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt
import re

def load_real_bom_data():
    """Load real BOM data from input files"""
    print("Loading real BOM data...")
    
    input_folder = 'input'
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    
    all_real_data = []
    
    for file in excel_files[:5]:  # Process first 5 files for real analysis
        print(f"  Processing: {file}")
        file_path = os.path.join(input_folder, file)
        
        try:
            xls = pd.ExcelFile(file_path)
            if len(xls.sheet_names) < 2:
                continue
                
            df = pd.read_excel(file_path, sheet_name=1)
            
            if len(df.columns) < 4:
                continue
            
            # Real column mapping
            df = df.iloc[:, :4]
            df.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
            
            # Clean real data
            df = df.dropna(subset=['F_E_Line', 'Item_ID', 'Item_Description'])
            df = df[df['F_E_Line'].astype(str).str.strip() != '']
            df = df[df['Item_ID'].astype(str).str.strip() != '']
            df = df[df['Item_Description'].astype(str).str.strip() != '']
            
            # Remove numeric-only Item IDs (sequence numbers)
            df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
            
            # Add metadata
            df['Source_File'] = file
            df['File_Row'] = range(len(df))
            
            all_real_data.append(df)
            
        except Exception as e:
            print(f"    Error processing {file}: {e}")
            continue
    
    if all_real_data:
        combined_data = pd.concat(all_real_data, ignore_index=True)
        print(f"✅ Loaded {len(combined_data)} real BOM items from {len(all_real_data)} files")
        return combined_data
    else:
        print("❌ No real BOM data could be loaded")
        return None

def load_real_similarity_data():
    """Load real similarity analysis results"""
    print("Loading real similarity data...")
    
    output_folder = 'output'
    similarity_file = os.path.join(output_folder, 'complete_pairwise_similarity_analysis.xlsx')
    
    if os.path.exists(similarity_file):
        try:
            similarity_data = pd.read_excel(similarity_file, engine='openpyxl')
            print(f"✅ Loaded {len(similarity_data)} real similarity matches")
            return similarity_data
        except Exception as e:
            print(f"❌ Error loading similarity data: {e}")
            return None
    else:
        print("❌ No similarity data file found")
        return None

def analyze_real_item_descriptions(bom_data):
    """Analyze real item descriptions to identify component types"""
    print("Analyzing real item descriptions...")
    
    descriptions = bom_data['Item_Description'].astype(str).str.lower()
    
    # Define real component categories based on common BOM terminology
    component_categories = {
        'electronic': ['pcb', 'circuit', 'board', 'electronic', 'chip', 'processor', 'memory', 'capacitor', 'resistor', 'diode'],
        'mechanical': ['screw', 'bolt', 'nut', 'washer', 'spring', 'gear', 'bearing', 'shaft', 'bracket', 'mount'],
        'housing': ['case', 'cover', 'housing', 'enclosure', 'panel', 'door', 'lid', 'frame'],
        'cable': ['cable', 'wire', 'harness', 'connector', 'plug', 'socket', 'terminal'],
        'sensor': ['sensor', 'detector', 'probe', 'transducer', 'switch', 'button'],
        'display': ['display', 'screen', 'monitor', 'led', 'lcd', 'indicator', 'light'],
        'power': ['power', 'supply', 'battery', 'charger', 'adapter', 'transformer'],
        'filter': ['filter', 'strainer', 'mesh', 'screen'],
        'gasket': ['gasket', 'seal', 'o-ring', 'rubber'],
        'tube': ['tube', 'pipe', 'hose', 'fitting', 'valve']
    }
    
    # Categorize each item
    bom_data['Component_Category'] = 'other'
    
    for category, keywords in component_categories.items():
        mask = descriptions.str.contains('|'.join(keywords), na=False)
        bom_data.loc[mask, 'Component_Category'] = category
    
    # Analyze description patterns
    description_analysis = {
        'total_items': len(bom_data),
        'unique_descriptions': bom_data['Item_Description'].nunique(),
        'category_distribution': dict(bom_data['Component_Category'].value_counts()),
        'most_common_descriptions': dict(bom_data['Item_Description'].value_counts().head(20)),
        'description_length_stats': {
            'mean': float(descriptions.str.len().mean()),
            'median': float(descriptions.str.len().median()),
            'min': int(descriptions.str.len().min()),
            'max': int(descriptions.str.len().max())
        }
    }
    
    print(f"✅ Categorized {len(bom_data)} items into {len(component_categories)} categories")
    return bom_data, description_analysis

def create_real_fe_line_families(bom_data):
    """Create families based on real F-E-Line structures"""
    print("Creating families based on real F-E-Line structures...")
    
    fe_line_families = {}
    
    # Group by F-E-Line
    fe_line_groups = bom_data.groupby('F_E_Line')
    
    for fe_line, group in fe_line_groups:
        if len(group) < 2:  # Skip single-item F-E-Lines
            continue
            
        family_characteristics = {
            'fe_line': fe_line,
            'item_count': len(group),
            'unique_items': group['Item_ID'].nunique(),
            'files_involved': group['Source_File'].nunique(),
            'file_list': list(group['Source_File'].unique()),
            'component_categories': dict(group['Component_Category'].value_counts()),
            'dominant_category': group['Component_Category'].mode().iloc[0] if len(group['Component_Category'].mode()) > 0 else 'mixed',
            'sample_items': list(group['Item_ID'].head(5)),
            'sample_descriptions': list(group['Item_Description'].head(5)),
            'description_diversity': group['Item_Description'].nunique() / len(group)
        }
        
        # Classify family type based on real characteristics
        if family_characteristics['files_involved'] > 1:
            if family_characteristics['description_diversity'] < 0.3:
                family_type = "Standardized Cross-Product Component"
            else:
                family_type = "Common Assembly Pattern"
        else:
            if family_characteristics['item_count'] > 50:
                family_type = "Complex Product Assembly"
            elif family_characteristics['dominant_category'] != 'other':
                family_type = f"Specialized {family_characteristics['dominant_category'].title()} Assembly"
            else:
                family_type = "Product-Specific Assembly"
        
        family_characteristics['family_type'] = family_type
        fe_line_families[f"FE_Family_{fe_line}"] = family_characteristics
    
    print(f"✅ Created {len(fe_line_families)} F-E-Line based families")
    return fe_line_families

def create_similarity_based_families(bom_data, similarity_data):
    """Create families based on real similarity matches"""
    print("Creating families based on real similarity matches...")
    
    if similarity_data is None:
        print("⚠️  No similarity data available")
        return {}
    
    # Build similarity network from real matches
    similarity_network = defaultdict(set)
    
    for _, row in similarity_data.iterrows():
        item1 = str(row.get('Item_ID_1', ''))
        item2 = str(row.get('Item_ID_2', ''))
        similarity_score = row.get('Similarity_Score', 0)
        
        if similarity_score > 0.7:  # High similarity threshold
            similarity_network[item1].add(item2)
            similarity_network[item2].add(item1)
    
    # Find connected components (similarity families)
    visited = set()
    similarity_families = {}
    family_id = 0
    
    for item in similarity_network:
        if item in visited:
            continue
            
        # BFS to find connected component
        family_items = set()
        queue = [item]
        
        while queue:
            current = queue.pop(0)
            if current in visited:
                continue
                
            visited.add(current)
            family_items.add(current)
            
            for neighbor in similarity_network[current]:
                if neighbor not in visited:
                    queue.append(neighbor)
        
        if len(family_items) >= 3:  # Minimum family size
            # Get details for family items
            family_data = bom_data[bom_data['Item_ID'].isin(family_items)]
            
            family_characteristics = {
                'family_id': family_id,
                'item_count': len(family_items),
                'items': list(family_items),
                'files_involved': family_data['Source_File'].nunique(),
                'file_list': list(family_data['Source_File'].unique()),
                'component_categories': dict(family_data['Component_Category'].value_counts()),
                'dominant_category': family_data['Component_Category'].mode().iloc[0] if len(family_data['Component_Category'].mode()) > 0 else 'mixed',
                'sample_descriptions': list(family_data['Item_Description'].head(5)),
                'cross_file_family': family_data['Source_File'].nunique() > 1
            }
            
            # Classify family type
            if family_characteristics['cross_file_family']:
                family_type = "Cross-Product Similar Components"
            else:
                family_type = "Product-Specific Similar Components"
            
            family_characteristics['family_type'] = family_type
            similarity_families[f"Sim_Family_{family_id}"] = family_characteristics
            family_id += 1
    
    print(f"✅ Created {len(similarity_families)} similarity-based families")
    return similarity_families

def create_description_based_families(bom_data):
    """Create families based on real description patterns"""
    print("Creating families based on real description patterns...")
    
    # Use TF-IDF to find description similarities
    descriptions = bom_data['Item_Description'].astype(str)
    
    # Create TF-IDF vectors
    vectorizer = TfidfVectorizer(
        max_features=1000,
        stop_words='english',
        ngram_range=(1, 2),
        min_df=2
    )
    
    try:
        tfidf_matrix = vectorizer.fit_transform(descriptions)
        
        # Use hierarchical clustering on TF-IDF vectors
        n_clusters = min(20, len(bom_data) // 10)  # Adaptive cluster count
        
        if n_clusters >= 2:
            clustering = AgglomerativeClustering(
                n_clusters=n_clusters,
                metric='cosine',
                linkage='average'
            )
            
            cluster_labels = clustering.fit_predict(tfidf_matrix.toarray())
            bom_data['Description_Cluster'] = cluster_labels
            
            description_families = {}
            
            for cluster_id in range(n_clusters):
                cluster_data = bom_data[bom_data['Description_Cluster'] == cluster_id]
                
                if len(cluster_data) < 3:  # Skip small clusters
                    continue
                
                family_characteristics = {
                    'cluster_id': cluster_id,
                    'item_count': len(cluster_data),
                    'unique_items': cluster_data['Item_ID'].nunique(),
                    'files_involved': cluster_data['Source_File'].nunique(),
                    'file_list': list(cluster_data['Source_File'].unique()),
                    'component_categories': dict(cluster_data['Component_Category'].value_counts()),
                    'dominant_category': cluster_data['Component_Category'].mode().iloc[0] if len(cluster_data['Component_Category'].mode()) > 0 else 'mixed',
                    'sample_descriptions': list(cluster_data['Item_Description'].head(10)),
                    'common_words': extract_common_words(cluster_data['Item_Description'])
                }
                
                # Classify based on description patterns
                if family_characteristics['files_involved'] > 2:
                    family_type = "Cross-Product Description Family"
                else:
                    family_type = "Product-Specific Description Family"
                
                family_characteristics['family_type'] = family_type
                description_families[f"Desc_Family_{cluster_id}"] = family_characteristics
            
            print(f"✅ Created {len(description_families)} description-based families")
            return description_families
        
    except Exception as e:
        print(f"⚠️  Error in description clustering: {e}")
        return {}

def extract_common_words(descriptions):
    """Extract common words from descriptions"""
    all_words = []
    for desc in descriptions:
        words = re.findall(r'\b[a-zA-Z]{3,}\b', str(desc).lower())
        all_words.extend(words)
    
    word_counts = Counter(all_words)
    return dict(word_counts.most_common(10))

def consolidate_real_families(fe_line_families, similarity_families, description_families, bom_data):
    """Consolidate all family types into final real product families"""
    print("Consolidating into final real product families...")
    
    consolidated_families = {}
    
    # Add F-E-Line families (highest priority - structural)
    for family_name, family_data in fe_line_families.items():
        if family_data['item_count'] >= 5:  # Minimum size for real families
            consolidated_families[family_name] = {
                **family_data,
                'family_source': 'F-E-Line Structure',
                'business_relevance': 'High - Structural Assembly'
            }
    
    # Add cross-product similarity families
    for family_name, family_data in similarity_families.items():
        if family_data['cross_file_family'] and family_data['item_count'] >= 3:
            consolidated_families[family_name] = {
                **family_data,
                'family_source': 'Cross-Product Similarity',
                'business_relevance': 'High - Standardization Opportunity'
            }
    
    # Add significant description families
    for family_name, family_data in description_families.items():
        if family_data['files_involved'] > 1 and family_data['item_count'] >= 5:
            consolidated_families[family_name] = {
                **family_data,
                'family_source': 'Description Pattern',
                'business_relevance': 'Medium - Functional Grouping'
            }
    
    # Calculate business metrics for each family
    for family_name, family_data in consolidated_families.items():
        # Estimate business impact
        if family_data.get('files_involved', 0) > 2:
            standardization_potential = 'High'
        elif family_data.get('files_involved', 0) > 1:
            standardization_potential = 'Medium'
        else:
            standardization_potential = 'Low'
        
        family_data['standardization_potential'] = standardization_potential
        
        # Cost reduction potential
        item_count = family_data.get('item_count', 0)
        if item_count > 20:
            cost_reduction_potential = 'High'
        elif item_count > 10:
            cost_reduction_potential = 'Medium'
        else:
            cost_reduction_potential = 'Low'
        
        family_data['cost_reduction_potential'] = cost_reduction_potential
    
    print(f"✅ Consolidated into {len(consolidated_families)} final real product families")
    return consolidated_families

def save_real_families_analysis(consolidated_families, bom_data, description_analysis, output_folder):
    """Save real families analysis results"""
    print("Saving real families analysis...")
    
    # Save consolidated families summary
    families_summary = []
    for family_name, family_data in consolidated_families.items():
        summary_row = {
            'Family_Name': family_name,
            'Family_Type': family_data.get('family_type', ''),
            'Family_Source': family_data.get('family_source', ''),
            'Item_Count': family_data.get('item_count', 0),
            'Files_Involved': family_data.get('files_involved', 0),
            'Dominant_Category': family_data.get('dominant_category', ''),
            'Business_Relevance': family_data.get('business_relevance', ''),
            'Standardization_Potential': family_data.get('standardization_potential', ''),
            'Cost_Reduction_Potential': family_data.get('cost_reduction_potential', ''),
            'Sample_Items': '; '.join(family_data.get('sample_items', family_data.get('items', []))[:3])
        }
        families_summary.append(summary_row)
    
    families_df = pd.DataFrame(families_summary)
    families_df.to_excel(os.path.join(output_folder, 'real_product_families_summary.xlsx'), index=False)
    
    # Save detailed analysis
    with open(os.path.join(output_folder, 'real_product_families_analysis.txt'), 'w', encoding='utf-8') as f:
        f.write("REAL PRODUCT FAMILIES ANALYSIS\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("DATA OVERVIEW\n")
        f.write("=" * 15 + "\n")
        f.write(f"Total BOM items analyzed: {description_analysis['total_items']}\n")
        f.write(f"Unique descriptions: {description_analysis['unique_descriptions']}\n")
        f.write(f"Real product families identified: {len(consolidated_families)}\n\n")
        
        f.write("COMPONENT CATEGORIES\n")
        f.write("=" * 20 + "\n")
        for category, count in description_analysis['category_distribution'].items():
            f.write(f"{category}: {count} items\n")
        f.write("\n")
        
        f.write("REAL PRODUCT FAMILIES\n")
        f.write("=" * 25 + "\n")
        
        for family_name, family_data in consolidated_families.items():
            f.write(f"\n{family_name}\n")
            f.write("-" * len(family_name) + "\n")
            f.write(f"Type: {family_data.get('family_type', 'Unknown')}\n")
            f.write(f"Source: {family_data.get('family_source', 'Unknown')}\n")
            f.write(f"Items: {family_data.get('item_count', 0)}\n")
            f.write(f"Files: {family_data.get('files_involved', 0)}\n")
            f.write(f"Category: {family_data.get('dominant_category', 'Mixed')}\n")
            f.write(f"Business Relevance: {family_data.get('business_relevance', 'Unknown')}\n")
            f.write(f"Standardization Potential: {family_data.get('standardization_potential', 'Unknown')}\n")
            
            if 'sample_descriptions' in family_data:
                f.write(f"Sample Descriptions:\n")
                for desc in family_data['sample_descriptions'][:3]:
                    f.write(f"  - {desc}\n")
            f.write("\n")
    
    print("✅ Real families analysis saved")

def main():
    print("Starting Real Product Families Creation")
    print("=" * 60)
    print("Using actual BOM data, real F-E-Line structures, and true similarity matches")
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Load real data
    bom_data = load_real_bom_data()
    if bom_data is None:
        print("❌ Cannot proceed without real BOM data")
        return
    
    similarity_data = load_real_similarity_data()
    
    # Analyze real descriptions
    bom_data, description_analysis = analyze_real_item_descriptions(bom_data)
    
    # Create different types of real families
    fe_line_families = create_real_fe_line_families(bom_data)
    similarity_families = create_similarity_based_families(bom_data, similarity_data)
    description_families = create_description_based_families(bom_data)
    
    # Consolidate into final real families
    consolidated_families = consolidate_real_families(
        fe_line_families, similarity_families, description_families, bom_data
    )
    
    # Save results
    save_real_families_analysis(consolidated_families, bom_data, description_analysis, output_folder)
    
    print(f"\nReal Product Families Creation Complete!")
    print("=" * 60)
    print(f"Real BOM items analyzed: {len(bom_data)}")
    print(f"Real product families created: {len(consolidated_families)}")
    print(f"Family sources: F-E-Line structure, Cross-product similarity, Description patterns")
    print(f"\nFiles created:")
    print(f"- real_product_families_summary.xlsx")
    print(f"- real_product_families_analysis.txt")

if __name__ == "__main__":
    main()
