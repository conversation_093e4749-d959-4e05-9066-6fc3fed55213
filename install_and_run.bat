@echo off
echo 🔧 Install Required Packages and Run BOM Analysis
echo =================================================
echo.

REM Find Python
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    set PIP_CMD=pip
) else (
    py --version >nul 2>&1
    if %errorlevel% equ 0 (
        set PYTHON_CMD=py
        set PIP_CMD=py -m pip
    ) else (
        echo ❌ Python not found!
        pause
        exit /b 1
    )
)

echo ✅ Python found: %PYTHON_CMD%
%PYTHON_CMD% --version

echo.
echo 📦 Installing required packages...
echo Installing pandas...
%PIP_CMD% install pandas

echo Installing numpy...
%PIP_CMD% install numpy

echo Installing scikit-learn...
%PIP_CMD% install scikit-learn

echo Installing openpyxl...
%PIP_CMD% install openpyxl

echo.
echo 🧪 Testing packages...
%PYTHON_CMD% -c "import pandas, numpy, sklearn, openpyxl; print('✅ All packages installed successfully!')"

echo.
echo 🔍 Running BOM similarity analysis...
%PYTHON_CMD% bom_similarity_analysis.py

echo.
echo 🎉 Complete! Check the 'output' folder for results.
pause
