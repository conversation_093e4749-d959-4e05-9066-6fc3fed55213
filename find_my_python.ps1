# Find Python installations on Windows
Write-Host "🔍 Searching for Python installations..." -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check Python launcher
Write-Host "1. Checking Python Launcher (py):" -ForegroundColor Yellow
try {
    $pyVersion = py --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python Launcher found: $pyVersion" -ForegroundColor Green
        Write-Host "Available versions:" -ForegroundColor Green
        py -0
        Write-Host ""
        Write-Host "🎯 SOLUTION: Use 'py' instead of 'python'" -ForegroundColor Cyan
        $found = $true
    }
} catch {
    Write-Host "❌ Python launcher not available" -ForegroundColor Red
}

Write-Host ""

# Check common installation paths
Write-Host "2. Checking common Python paths:" -ForegroundColor Yellow

$commonPaths = @(
    "C:\Program Files\Python*",
    "C:\Program Files (x86)\Python*",
    "$env:LOCALAPPDATA\Programs\Python*",
    "$env:APPDATA\Python*"
)

foreach ($path in $commonPaths) {
    $pythonDirs = Get-ChildItem -Path (Split-Path $path) -Filter (Split-Path $path -Leaf) -Directory -ErrorAction SilentlyContinue
    foreach ($dir in $pythonDirs) {
        $pythonExe = Join-Path $dir.FullName "python.exe"
        if (Test-Path $pythonExe) {
            Write-Host "✅ Found: $pythonExe" -ForegroundColor Green
            try {
                $version = & $pythonExe --version 2>&1
                Write-Host "   Version: $version" -ForegroundColor Green
                $found = $true
            } catch {
                Write-Host "   ❌ Not working" -ForegroundColor Red
            }
        }
    }
}

Write-Host ""

# Check Microsoft Store Python
Write-Host "3. Checking Microsoft Store Python:" -ForegroundColor Yellow
$storePython = "$env:LOCALAPPDATA\Microsoft\WindowsApps\python.exe"
if (Test-Path $storePython) {
    try {
        $version = & $storePython --version 2>&1
        Write-Host "✅ Microsoft Store Python: $version" -ForegroundColor Green
        Write-Host "   Path: $storePython" -ForegroundColor Green
        $found = $true
    } catch {
        Write-Host "❌ Microsoft Store Python not working" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Microsoft Store Python not found" -ForegroundColor Red
}

Write-Host ""

# Check existing virtual environment
Write-Host "4. Checking existing virtual environments:" -ForegroundColor Yellow
$venvPaths = @(
    "$env:USERPROFILE\OneDrive - GE HealthCare\virtualenv\Scripts\python.exe",
    "$env:USERPROFILE\venv\Scripts\python.exe",
    "$env:USERPROFILE\.virtualenvs\*\Scripts\python.exe"
)

foreach ($venvPath in $venvPaths) {
    if ($venvPath -like "*\*\*") {
        # Handle wildcard paths
        $parentDir = Split-Path (Split-Path $venvPath)
        if (Test-Path $parentDir) {
            $venvDirs = Get-ChildItem -Path $parentDir -Directory -ErrorAction SilentlyContinue
            foreach ($venvDir in $venvDirs) {
                $pythonExe = Join-Path $venvDir.FullName "Scripts\python.exe"
                if (Test-Path $pythonExe) {
                    Write-Host "✅ Found virtual env: $pythonExe" -ForegroundColor Green
                    try {
                        $version = & $pythonExe --version 2>&1
                        Write-Host "   Version: $version" -ForegroundColor Green
                        $found = $true
                    } catch {
                        Write-Host "   ❌ Not working" -ForegroundColor Red
                    }
                }
            }
        }
    } else {
        if (Test-Path $venvPath) {
            Write-Host "✅ Found virtual env: $venvPath" -ForegroundColor Green
            try {
                $version = & $venvPath --version 2>&1
                Write-Host "   Version: $version" -ForegroundColor Green
                $found = $true
            } catch {
                Write-Host "   ❌ Not working" -ForegroundColor Red
            }
        }
    }
}

Write-Host ""

# Search PATH
Write-Host "5. Checking PATH environment:" -ForegroundColor Yellow
try {
    $pythonInPath = Get-Command python -ErrorAction SilentlyContinue
    if ($pythonInPath) {
        Write-Host "✅ Python found in PATH: $($pythonInPath.Source)" -ForegroundColor Green
        $version = python --version 2>&1
        Write-Host "   Version: $version" -ForegroundColor Green
        $found = $true
    } else {
        Write-Host "❌ Python not in PATH" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Python not accessible via PATH" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan

if ($found) {
    Write-Host "🎉 Python installation(s) found!" -ForegroundColor Green
    Write-Host ""
    Write-Host "💡 Recommendations:" -ForegroundColor Cyan
    Write-Host "• If 'py' launcher works, use: py -m venv bom_analysis_env" -ForegroundColor White
    Write-Host "• If you found a full path, use that path to create your environment" -ForegroundColor White
    Write-Host "• Choose the most recent Python version (3.8+)" -ForegroundColor White
} else {
    Write-Host "❌ No working Python installation found" -ForegroundColor Red
    Write-Host ""
    Write-Host "📥 Please install Python:" -ForegroundColor Yellow
    Write-Host "1. Go to https://python.org/downloads/" -ForegroundColor White
    Write-Host "2. Download Python 3.11 or 3.12" -ForegroundColor White
    Write-Host "3. During installation, check 'Add Python to PATH'" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to continue"
