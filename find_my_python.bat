@echo off
echo 🔍 Searching for Python on your system...
echo ==========================================
echo.

echo 1. Checking if 'py' launcher works:
py --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ Python Launcher found! You can use 'py' instead of 'python'
    echo Available Python versions:
    py -0
    echo.
    echo 🎯 SOLUTION: Use 'py' instead of 'python' in commands
    echo Example: py -m pip install pandas
    goto :found
) else (
    echo ❌ Python launcher not found
)
echo.

echo 2. Searching in Program Files:
for /d %%i in ("C:\Program Files\Python*") do (
    echo Found: %%i
    if exist "%%i\python.exe" (
        echo ✅ Python executable: %%i\python.exe
        "%%i\python.exe" --version 2>nul
    )
)
echo.

echo 3. Searching in User Programs:
for /d %%i in ("%LOCALAPPDATA%\Programs\Python*") do (
    echo Found: %%i
    if exist "%%i\python.exe" (
        echo ✅ Python executable: %%i\python.exe
        "%%i\python.exe" --version 2>nul
    )
)
echo.

echo 4. Checking Microsoft Store Python:
if exist "%LOCALAPPDATA%\Microsoft\WindowsApps\python.exe" (
    echo ✅ Found Microsoft Store Python
    "%LOCALAPPDATA%\Microsoft\WindowsApps\python.exe" --version 2>nul
    if %errorlevel% equ 0 (
        echo 🎯 SOLUTION: Microsoft Store Python is available
        goto :found
    )
)
echo.

echo 5. Checking your existing virtual environment:
if exist "%USERPROFILE%\OneDrive - GE HealthCare\virtualenv\Scripts\python.exe" (
    echo ✅ Found existing virtual environment
    "%USERPROFILE%\OneDrive - GE HealthCare\virtualenv\Scripts\python.exe" --version 2>nul
    if %errorlevel% equ 0 (
        echo 🎯 SOLUTION: You have a working Python in your virtual environment!
        echo Path: %USERPROFILE%\OneDrive - GE HealthCare\virtualenv\Scripts\python.exe
        goto :found
    )
)
echo.

echo 6. Searching entire C: drive for python.exe (this may take a moment):
for /f "tokens=*" %%i in ('dir C:\python.exe /s /b 2^>nul') do (
    echo Found: %%i
    "%%i" --version 2>nul
    if %errorlevel% equ 0 (
        echo ✅ This Python works!
    )
)

echo.
echo ==========================================
goto :end

:found
echo.
echo 🎉 Python found! You can now proceed with setup.
echo.

:end
echo.
echo 💡 Next steps:
echo.
echo If Python was found above:
echo   • Note the working path/command
echo   • Use that path to create your environment
echo.
echo If no Python was found:
echo   • Download from https://python.org/downloads/
echo   • Install with "Add to PATH" checked
echo.
pause
