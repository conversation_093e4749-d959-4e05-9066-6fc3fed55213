#!/usr/bin/env python3
"""
CORRECTED BOM Graph Analysis and Visualization
F-E-Line refers to the FIRST column (Level) with hierarchical format like 1-1-2, 2-3-1, etc.
Creates hierarchical graphs based on the Level column structure.
"""

import os
import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from collections import defaultdict
import json
import re

def parse_fe_line(level_str):
    """Parse F-E-Line format like '1-1-2' into hierarchical components"""
    try:
        level_str = str(level_str).strip()
        # Split by dash to get hierarchical levels
        parts = level_str.split('-')
        if len(parts) >= 1:
            # Convert to integers, handling any non-numeric parts
            numeric_parts = []
            for part in parts:
                try:
                    numeric_parts.append(int(part))
                except ValueError:
                    # If part contains non-numeric, extract first number
                    match = re.search(r'(\d+)', part)
                    if match:
                        numeric_parts.append(int(match.group(1)))
                    else:
                        numeric_parts.append(0)
            return numeric_parts
        return [0]
    except:
        return [0]

def get_level_depth(fe_line_parts):
    """Get the depth/level of the F-E-Line (how many parts it has)"""
    return len(fe_line_parts)

def get_parent_fe_line(fe_line_parts):
    """Get the parent F-E-Line by removing the last part"""
    if len(fe_line_parts) <= 1:
        return None  # Root level has no parent
    return fe_line_parts[:-1]

def load_bom_file_for_corrected_graph(file_path):
    """Load a single BOM file with CORRECTED F-E-Line interpretation"""
    try:
        # Read the second sheet
        xls = pd.ExcelFile(file_path)
        if len(xls.sheet_names) < 2:
            return None
            
        df = pd.read_excel(file_path, sheet_name=1)
        
        if len(df.columns) < 4:
            return None
        
        # CORRECTED column mapping: F-E-Line (Level), Sequence, Item_ID, Description
        df = df.iloc[:, :4]
        df.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
        
        # Clean data
        df = df.dropna(subset=['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description'])
        df = df[df['F_E_Line'].astype(str).str.strip() != '']
        df = df[df['Item_ID'].astype(str).str.strip() != '']
        df = df[df['Item_Description'].astype(str).str.strip() != '']
        
        # Remove numeric-only Item IDs (these are sequence numbers)
        df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
        
        # Parse F-E-Line hierarchical structure
        df['F_E_Line_Parts'] = df['F_E_Line'].apply(parse_fe_line)
        df['Level_Depth'] = df['F_E_Line_Parts'].apply(get_level_depth)
        df['Main_Level'] = df['F_E_Line_Parts'].apply(lambda x: x[0] if len(x) > 0 else 0)
        
        # Add row index for tracking
        df['Row_Index'] = range(len(df))
        
        return df
        
    except Exception as e:
        print(f"Error loading file: {e}")
        return None

def create_corrected_bom_graph_structure(df, file_name):
    """Create CORRECTED graph structure based on F-E-Line hierarchy"""
    print(f"🔍 Creating CORRECTED graph structure for: {file_name}")
    
    file_graph = {
        'file_name': file_name,
        'file_root': f"FILE_ROOT_{file_name}",
        'total_items': len(df),
        'nodes': {},
        'edges': [],
        'fe_line_groups': defaultdict(list)
    }
    
    # Add file root node (grand father of all)
    file_root = file_graph['file_root']
    file_graph['nodes'][file_root] = {
        'id': file_root,
        'level_depth': -1,
        'fe_line': 'FILE_ROOT',
        'fe_line_parts': [],
        'description': f"File Root: {file_name}",
        'item_type': 'file_root',
        'row_index': -1
    }
    
    # Group items by their F-E-Line for analysis
    for _, row in df.iterrows():
        fe_line = str(row['F_E_Line'])
        file_graph['fe_line_groups'][fe_line].append(row)
    
    print(f"  📊 Found {len(file_graph['fe_line_groups'])} unique F-E-Line patterns")
    
    # Create nodes for each item
    node_map = {}  # Map F-E-Line parts to node IDs
    
    for idx, (_, row) in enumerate(df.iterrows()):
        fe_line = str(row['F_E_Line'])
        fe_line_parts = row['F_E_Line_Parts']
        level_depth = row['Level_Depth']
        
        # Create unique node ID
        node_id = f"NODE_{idx}_{fe_line}"
        
        # Create node
        file_graph['nodes'][node_id] = {
            'id': node_id,
            'level_depth': level_depth,
            'fe_line': fe_line,
            'fe_line_parts': fe_line_parts,
            'main_level': row['Main_Level'],
            'sequence': str(row['Sequence']),
            'item_id': str(row['Item_ID']),
            'description': str(row['Item_Description'])[:50],
            'full_description': str(row['Item_Description']),
            'item_type': 'component',
            'row_index': row['Row_Index']
        }
        
        # Map this F-E-Line pattern to this node
        fe_line_key = '-'.join(map(str, fe_line_parts))
        node_map[fe_line_key] = node_id
        
        # Determine parent based on F-E-Line hierarchy
        parent_fe_line_parts = get_parent_fe_line(fe_line_parts)
        
        if parent_fe_line_parts is None:
            # This is a root level item (like 1, 2, 3), connect to file root
            parent_id = file_root
        else:
            # Find parent node with matching F-E-Line pattern
            parent_key = '-'.join(map(str, parent_fe_line_parts))
            
            if parent_key in node_map:
                parent_id = node_map[parent_key]
            else:
                # Create intermediate parent node if it doesn't exist
                parent_node_id = f"INTERMEDIATE_{parent_key}"
                if parent_node_id not in file_graph['nodes']:
                    file_graph['nodes'][parent_node_id] = {
                        'id': parent_node_id,
                        'level_depth': len(parent_fe_line_parts),
                        'fe_line': '-'.join(map(str, parent_fe_line_parts)),
                        'fe_line_parts': parent_fe_line_parts,
                        'description': f"Intermediate Level: {'-'.join(map(str, parent_fe_line_parts))}",
                        'item_type': 'intermediate',
                        'row_index': -1
                    }
                    node_map[parent_key] = parent_node_id
                parent_id = parent_node_id
        
        # Create edge from parent to child
        file_graph['edges'].append({
            'parent': parent_id,
            'child': node_id,
            'parent_depth': file_graph['nodes'][parent_id]['level_depth'],
            'child_depth': level_depth
        })
    
    print(f"  ✅ Created {len(file_graph['nodes'])} nodes and {len(file_graph['edges'])} edges")
    return file_graph

def create_corrected_networkx_graph(file_graph):
    """Convert corrected graph structure to NetworkX graph"""
    G = nx.DiGraph()
    
    # Add all nodes
    for node_id, node_data in file_graph['nodes'].items():
        G.add_node(node_id,
                   level_depth=node_data['level_depth'],
                   node_type=node_data['item_type'],
                   label=node_data.get('description', ''),
                   fe_line=node_data.get('fe_line', ''),
                   item_id=node_data.get('item_id', ''))
    
    # Add all edges
    for edge in file_graph['edges']:
        G.add_edge(edge['parent'], edge['child'])
    
    return G

def visualize_corrected_bom_graph(G, file_name, output_folder):
    """Create visualization of the CORRECTED BOM graph"""
    print(f"📊 Creating CORRECTED visualization for: {file_name}")
    
    plt.figure(figsize=(24, 18))
    
    # Create hierarchical layout based on level depth
    pos = create_corrected_hierarchical_layout(G)
    
    # Define colors for different node types and levels
    node_colors = []
    node_sizes = []
    
    for node in G.nodes():
        node_type = G.nodes[node].get('node_type', 'component')
        level_depth = G.nodes[node].get('level_depth', 0)
        
        if node_type == 'file_root':
            node_colors.append('red')
            node_sizes.append(1200)
        elif node_type == 'intermediate':
            node_colors.append('orange')
            node_sizes.append(400)
        else:
            # Color by level depth
            if level_depth == 1:
                node_colors.append('lightblue')
                node_sizes.append(300)
            elif level_depth == 2:
                node_colors.append('lightgreen')
                node_sizes.append(250)
            elif level_depth == 3:
                node_colors.append('lightyellow')
                node_sizes.append(200)
            elif level_depth == 4:
                node_colors.append('lightpink')
                node_sizes.append(150)
            else:
                node_colors.append('lightgray')
                node_sizes.append(100)
    
    # Draw the graph
    nx.draw(G, pos, 
            node_color=node_colors,
            node_size=node_sizes,
            with_labels=False,
            arrows=True,
            arrowsize=15,
            edge_color='gray',
            alpha=0.7,
            arrowstyle='->')
    
    # Add labels for important nodes only (to avoid clutter)
    important_nodes = {}
    for node in G.nodes():
        node_type = G.nodes[node].get('node_type', 'component')
        if node_type in ['file_root', 'intermediate']:
            label = G.nodes[node].get('label', '')[:20]
            important_nodes[node] = label
    
    nx.draw_networkx_labels(G, pos, important_nodes, font_size=6, font_weight='bold')
    
    # Create legend
    legend_elements = [
        mpatches.Patch(color='red', label='File Root'),
        mpatches.Patch(color='orange', label='Intermediate Levels'),
        mpatches.Patch(color='lightblue', label='Level Depth 1 (e.g., 1, 2, 3)'),
        mpatches.Patch(color='lightgreen', label='Level Depth 2 (e.g., 1-1, 2-3)'),
        mpatches.Patch(color='lightyellow', label='Level Depth 3 (e.g., 1-1-2)'),
        mpatches.Patch(color='lightpink', label='Level Depth 4 (e.g., 1-1-2-1)'),
        mpatches.Patch(color='lightgray', label='Deeper Levels')
    ]
    
    plt.legend(handles=legend_elements, loc='upper right', fontsize=8)
    plt.title(f'CORRECTED BOM Hierarchical Graph (F-E-Line Structure): {file_name}', 
              fontsize=14, fontweight='bold')
    
    # Save the visualization
    safe_filename = re.sub(r'[^\w\-_\.]', '_', file_name)
    viz_file = os.path.join(output_folder, f'corrected_bom_graph_{safe_filename}.png')
    plt.savefig(viz_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ CORRECTED visualization saved: {viz_file}")
    return viz_file

def create_corrected_hierarchical_layout(G):
    """Create hierarchical layout based on level depth"""
    pos = {}
    
    # Group nodes by level depth
    levels = defaultdict(list)
    for node in G.nodes():
        level_depth = G.nodes[node].get('level_depth', 0)
        levels[level_depth].append(node)
    
    # Position nodes level by level
    y_spacing = 3.0
    x_spacing = 2.0
    
    for level_depth, nodes in levels.items():
        y = -level_depth * y_spacing  # Deeper levels go down
        
        # Distribute nodes horizontally
        if len(nodes) == 1:
            x_positions = [0]
        else:
            total_width = (len(nodes) - 1) * x_spacing
            x_positions = [i * x_spacing - total_width/2 for i in range(len(nodes))]
        
        for i, node in enumerate(nodes):
            pos[node] = (x_positions[i], y)
    
    return pos

def save_corrected_graph_data(file_graph, output_folder):
    """Save corrected graph data as JSON"""
    safe_filename = re.sub(r'[^\w\-_\.]', '_', file_graph['file_name'])
    json_file = os.path.join(output_folder, f'corrected_bom_graph_data_{safe_filename}.json')
    
    # Convert to JSON-serializable format
    json_data = {
        'file_name': file_graph['file_name'],
        'file_root': file_graph['file_root'],
        'total_items': file_graph['total_items'],
        'total_nodes': len(file_graph['nodes']),
        'total_edges': len(file_graph['edges']),
        'unique_fe_lines': len(file_graph['fe_line_groups']),
        'nodes': file_graph['nodes'],
        'edges': file_graph['edges']
    }
    
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ CORRECTED graph data saved: {json_file}")
    return json_file

def main():
    print("🚀 Starting CORRECTED BOM Graph Analysis")
    print("=" * 70)
    print("🎯 F-E-Line = FIRST column (Level) with format like 1-1-2, 2-3-1")
    print("📊 Creating hierarchical graphs based on Level column structure")
    
    input_folder = 'input'
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Get all Excel files
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    print(f"📁 Found {len(excel_files)} Excel files to analyze")
    
    all_file_graphs = []
    
    # Process first 3 files for demonstration
    for i, file in enumerate(excel_files[:3], 1):
        print(f"\n[{i}/3] Processing: {file}")
        file_path = os.path.join(input_folder, file)
        
        # Load BOM data with corrected interpretation
        df = load_bom_file_for_corrected_graph(file_path)
        if df is None:
            print(f"❌ Failed to load {file}")
            continue
        
        print(f"✅ Loaded {len(df)} items")
        print(f"📊 F-E-Line examples: {df['F_E_Line'].head(5).tolist()}")
        print(f"📊 Level depths found: {sorted(df['Level_Depth'].unique())}")
        
        # Create corrected graph structure
        file_graph = create_corrected_bom_graph_structure(df, file)
        all_file_graphs.append(file_graph)
        
        # Create NetworkX graph
        G = create_corrected_networkx_graph(file_graph)
        print(f"📊 CORRECTED graph: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
        
        # Create visualization
        visualize_corrected_bom_graph(G, file, output_folder)
        
        # Save graph data
        save_corrected_graph_data(file_graph, output_folder)
    
    print(f"\n🎉 CORRECTED BOM Graph Analysis Complete!")
    print("=" * 70)
    print(f"📊 Processed {len(all_file_graphs)} files")
    print(f"📁 Files created in: {os.path.abspath(output_folder)}")
    print(f"   • corrected_bom_graph_*.png - CORRECTED hierarchical visualizations")
    print(f"   • corrected_bom_graph_data_*.json - CORRECTED graph structure data")
    
    print(f"\n💡 KEY CORRECTION:")
    print(f"   F-E-Line now correctly refers to the Level column (1st column)")
    print(f"   Hierarchical structure: 1-1-2 means Level 1 → Sub-level 1 → Sub-sub-level 2")

if __name__ == "__main__":
    main()
