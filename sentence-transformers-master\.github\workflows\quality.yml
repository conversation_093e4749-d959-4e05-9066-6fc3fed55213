name: Quality

on:
  push:
    branches:
      - master
      - v*-release
      - v*-pre
  pull_request:
    branches:
      - master
      - v*-release
      - v*-pre
  workflow_dispatch:

jobs:

  check_code_quality:
    name: Check code quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Python environment
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      - name: Install pre-commit
        run: |
          python -m pip install --upgrade pip
          python -m pip install pre-commit
      - name: Code quality
        run: |
          make check