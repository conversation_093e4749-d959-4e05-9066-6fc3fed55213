#!/usr/bin/env python3
"""
Run Enhanced Algorithm Test - Get Real Empirical Results
Test on subset first, then scale up
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import re
from difflib import SequenceMatcher
import time
import warnings
warnings.filterwarnings('ignore')

class RealEnhancedAlgorithmTest:
    def __init__(self):
        """Initialize with real data loading"""
        self.bom_data = None
        self.pcm_data = None
        self.sbert_model = None
        self.test_results = {}
        
    def load_real_data(self):
        """Load actual data files"""
        print("📊 LOADING REAL DATA")
        print("=" * 30)
        
        try:
            # Load BOM data
            self.bom_data = pd.read_excel('output/voyager_complete_bom_pcm_table.xlsx', sheet_name='Complete_Table')
            print(f"✅ BOM data loaded: {len(self.bom_data)} items")
            
            # Load PCM data  
            self.pcm_data = pd.read_csv('output/real_voyager_pcm_codes.csv')
            print(f"✅ PCM data loaded: {len(self.pcm_data)} codes")
            
            # Load SBERT model
            print("🧠 Loading SBERT model...")
            self.sbert_model = SentenceTransformer('all-MiniLM-L6-v2')
            print("✅ SBERT model loaded")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False
    
    def enhanced_preprocess(self, text):
        """Enhanced preprocessing with domain knowledge"""
        if pd.isna(text) or text == '':
            return ''
        
        text = str(text).lower()
        
        # Medical device domain mappings (real improvements)
        domain_mappings = {
            'tdi': 'time domain imaging',
            '1.5t': '1.5 tesla',
            '3.0t': '3.0 tesla', 
            'mr': 'magnetic resonance',
            'mri': 'magnetic resonance imaging',
            'rf': 'radio frequency',
            'coil': 'magnetic coil',
            'array': 'coil array',
            'phantom': 'test phantom',
            'gating': 'respiratory gating'
        }
        
        # Apply mappings
        for abbrev, full_form in domain_mappings.items():
            text = re.sub(r'\b' + abbrev + r'\b', full_form, text)
        
        # Clean text
        text = re.sub(r'[^\w\s\-\.]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def calculate_ensemble_similarity(self, bom_desc, pcm_desc, bom_id, pcm_code):
        """Calculate real ensemble similarity with multiple metrics"""
        
        # Preprocess both descriptions
        bom_clean = self.enhanced_preprocess(bom_desc)
        pcm_clean = self.enhanced_preprocess(pcm_desc)
        
        similarities = {}
        
        # 1. Semantic similarity (SBERT) - Real calculation
        try:
            if bom_clean and pcm_clean:
                bom_embedding = self.sbert_model.encode([bom_clean])
                pcm_embedding = self.sbert_model.encode([pcm_clean])
                semantic_sim = cosine_similarity(bom_embedding, pcm_embedding)[0][0]
            else:
                semantic_sim = 0.0
            similarities['semantic'] = float(semantic_sim)
        except Exception as e:
            similarities['semantic'] = 0.0
        
        # 2. Lexical similarity (Jaccard) - Real calculation
        bom_words = set(bom_clean.split()) if bom_clean else set()
        pcm_words = set(pcm_clean.split()) if pcm_clean else set()
        
        if len(bom_words) > 0 and len(pcm_words) > 0:
            intersection = len(bom_words.intersection(pcm_words))
            union = len(bom_words.union(pcm_words))
            jaccard_sim = intersection / union if union > 0 else 0.0
        else:
            jaccard_sim = 0.0
        similarities['lexical'] = jaccard_sim
        
        # 3. String similarity (Levenshtein-based) - Real calculation
        if bom_clean and pcm_clean:
            string_sim = SequenceMatcher(None, bom_clean, pcm_clean).ratio()
        else:
            string_sim = 0.0
        similarities['string'] = string_sim
        
        # 4. Code pattern matching - Real calculation
        bom_codes = re.findall(r'[A-Z]\d{4,6}[A-Z]{0,2}', str(bom_id))
        pcm_codes = re.findall(r'[A-Z]\d{4,6}[A-Z]{0,2}', str(pcm_code))
        
        code_sim = 0.0
        if bom_codes and pcm_codes:
            for bc in bom_codes:
                for pc in pcm_codes:
                    if len(bc) >= 2 and len(pc) >= 2:
                        if bc[:2] == pc[:2]:  # Same prefix
                            code_sim = max(code_sim, 0.8)
                        elif bc[:1] == pc[:1]:  # Same first letter
                            code_sim = max(code_sim, 0.4)
        similarities['code'] = code_sim
        
        # 5. Ensemble score (weighted combination) - Real calculation
        weights = {
            'semantic': 0.4,
            'lexical': 0.3, 
            'string': 0.2,
            'code': 0.1
        }
        
        ensemble_score = sum(similarities[metric] * weights[metric] for metric in weights.keys())
        similarities['ensemble'] = ensemble_score
        
        return similarities
    
    def test_on_subset(self, subset_size=100):
        """Test enhanced algorithm on small subset first"""
        print(f"\n🧪 TESTING ENHANCED ALGORITHM ON {subset_size} ITEMS")
        print("=" * 60)
        
        # Take random subset for testing
        test_bom = self.bom_data.sample(n=min(subset_size, len(self.bom_data)), random_state=42)
        pcm_list = self.pcm_data.to_dict('records')
        
        print(f"📊 Test subset: {len(test_bom)} BOM items vs {len(pcm_list)} PCM codes")
        
        # Test multiple thresholds
        thresholds = [0.6, 0.7, 0.8, 0.9]
        results = {}
        
        start_time = time.time()
        
        for threshold in thresholds:
            print(f"\n🎯 Testing threshold: {threshold}")
            
            matches = []
            processing_times = []
            
            for idx, (_, bom_row) in enumerate(test_bom.iterrows()):
                item_start = time.time()
                
                bom_desc = str(bom_row['Item_Description'])
                bom_id = str(bom_row['Item_ID'])
                
                best_score = 0.0
                best_match = None
                best_similarities = {}
                
                # Test against all PCM codes
                for pcm_item in pcm_list:
                    pcm_desc = str(pcm_item.get('description', ''))
                    pcm_code = str(pcm_item.get('code', ''))
                    
                    similarities = self.calculate_ensemble_similarity(
                        bom_desc, pcm_desc, bom_id, pcm_code
                    )
                    
                    ensemble_score = similarities['ensemble']
                    
                    if ensemble_score > best_score:
                        best_score = ensemble_score
                        best_match = pcm_item
                        best_similarities = similarities
                
                processing_times.append(time.time() - item_start)
                
                # Apply threshold
                if best_score >= threshold and best_match is not None:
                    matches.append({
                        'bom_id': bom_id,
                        'bom_desc': bom_desc[:50] + '...',
                        'pcm_code': best_match['code'],
                        'pcm_desc': best_match['description'][:50] + '...',
                        'ensemble_score': best_score,
                        'semantic': best_similarities['semantic'],
                        'lexical': best_similarities['lexical'],
                        'string': best_similarities['string'],
                        'code': best_similarities['code']
                    })
                
                if (idx + 1) % 20 == 0:
                    print(f"   Processed {idx + 1}/{len(test_bom)} items...")
            
            # Calculate real metrics
            total_items = len(test_bom)
            matched_items = len(matches)
            coverage = (matched_items / total_items) * 100
            
            if matches:
                avg_ensemble = np.mean([m['ensemble_score'] for m in matches])
                avg_semantic = np.mean([m['semantic'] for m in matches])
                avg_lexical = np.mean([m['lexical'] for m in matches])
                avg_string = np.mean([m['string'] for m in matches])
                avg_code = np.mean([m['code'] for m in matches])
                
                high_quality = len([m for m in matches if m['ensemble_score'] > 0.8])
                excellent_quality = len([m for m in matches if m['ensemble_score'] > 0.9])
            else:
                avg_ensemble = avg_semantic = avg_lexical = avg_string = avg_code = 0.0
                high_quality = excellent_quality = 0
            
            avg_processing_time = np.mean(processing_times)
            
            results[threshold] = {
                'total_items': total_items,
                'matched_items': matched_items,
                'coverage_percent': coverage,
                'avg_ensemble_score': avg_ensemble,
                'avg_semantic_score': avg_semantic,
                'avg_lexical_score': avg_lexical,
                'avg_string_score': avg_string,
                'avg_code_score': avg_code,
                'high_quality_matches': high_quality,
                'excellent_matches': excellent_quality,
                'avg_processing_time_sec': avg_processing_time,
                'matches': matches
            }
            
            print(f"   ✅ Results:")
            print(f"      Matched: {matched_items}/{total_items} ({coverage:.1f}%)")
            print(f"      Avg Ensemble Score: {avg_ensemble:.3f}")
            print(f"      High Quality (>0.8): {high_quality}")
            print(f"      Excellent (>0.9): {excellent_quality}")
            print(f"      Avg Processing Time: {avg_processing_time:.3f}s per item")
        
        total_time = time.time() - start_time
        print(f"\n⏱️ Total test time: {total_time:.1f} seconds")
        
        self.test_results = results
        return results
    
    def compare_with_original_real(self):
        """Compare with original algorithm using real data"""
        print(f"\n📊 REAL COMPARISON WITH ORIGINAL ALGORITHM")
        print("=" * 60)
        
        # Get original algorithm results from the same subset
        test_bom = self.bom_data.sample(n=100, random_state=42)
        
        # Original algorithm metrics (from actual data)
        original_matches = 0
        original_total = len(test_bom)
        original_scores = []
        
        for _, row in test_bom.iterrows():
            if row['Item_ID_PCM'] != '' and pd.notna(row['Item_ID_PCM']):
                original_matches += 1
                if row['Similarity_Score'] > 0:
                    original_scores.append(row['Similarity_Score'])
        
        original_coverage = (original_matches / original_total) * 100
        original_avg_score = np.mean(original_scores) if original_scores else 0.0
        original_high_quality = len([s for s in original_scores if s > 0.8])
        
        print(f"📈 REAL COMPARISON RESULTS:")
        print(f"{'Metric':<25} {'Original':<15} {'Enhanced (0.7)':<15} {'Enhanced (0.8)':<15} {'Improvement':<15}")
        print("-" * 85)
        
        if 0.7 in self.test_results and 0.8 in self.test_results:
            enh_07 = self.test_results[0.7]
            enh_08 = self.test_results[0.8]
            
            # Coverage comparison
            coverage_imp_07 = ((enh_07['coverage_percent'] / max(original_coverage, 0.1)) - 1) * 100
            coverage_imp_08 = ((enh_08['coverage_percent'] / max(original_coverage, 0.1)) - 1) * 100
            
            print(f"{'Coverage %':<25} {original_coverage:<15.1f} {enh_07['coverage_percent']:<15.1f} {enh_08['coverage_percent']:<15.1f} {coverage_imp_08:<15.1f}")
            
            # Score comparison
            score_imp_07 = ((enh_07['avg_ensemble_score'] / max(original_avg_score, 0.001)) - 1) * 100
            score_imp_08 = ((enh_08['avg_ensemble_score'] / max(original_avg_score, 0.001)) - 1) * 100
            
            print(f"{'Avg Score':<25} {original_avg_score:<15.3f} {enh_07['avg_ensemble_score']:<15.3f} {enh_08['avg_ensemble_score']:<15.3f} {score_imp_08:<15.1f}")
            
            # High quality comparison
            hq_imp_08 = ((enh_08['high_quality_matches'] / max(original_high_quality, 1)) - 1) * 100
            
            print(f"{'High Quality (>0.8)':<25} {original_high_quality:<15d} {enh_07['high_quality_matches']:<15d} {enh_08['high_quality_matches']:<15d} {hq_imp_08:<15.1f}")
        
        return {
            'original': {
                'coverage': original_coverage,
                'avg_score': original_avg_score,
                'high_quality': original_high_quality,
                'total_items': original_total
            }
        }
    
    def create_real_results_visualization(self):
        """Create visualization with real results"""
        print(f"\n📊 CREATING REAL RESULTS VISUALIZATION")
        print("=" * 50)
        
        if not self.test_results:
            print("❌ No test results to visualize")
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Enhanced Algorithm: REAL Empirical Results (100 Item Test)', fontsize=16, fontweight='bold')
        
        thresholds = list(self.test_results.keys())
        
        # 1. Coverage vs Threshold (Real Data)
        coverages = [self.test_results[t]['coverage_percent'] for t in thresholds]
        ax1.plot(thresholds, coverages, 'bo-', linewidth=3, markersize=10, label='Enhanced Algorithm')
        ax1.axhline(y=100, color='r', linestyle='--', linewidth=2, label='Original (100%)')
        ax1.set_title('Coverage Rate vs Threshold (Real Results)', fontweight='bold')
        ax1.set_xlabel('Threshold')
        ax1.set_ylabel('Coverage (%)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Add actual values on points
        for i, (t, c) in enumerate(zip(thresholds, coverages)):
            ax1.annotate(f'{c:.1f}%', (t, c), textcoords="offset points", xytext=(0,10), ha='center')
        
        # 2. Average Ensemble Score vs Threshold (Real Data)
        avg_scores = [self.test_results[t]['avg_ensemble_score'] for t in thresholds]
        ax2.plot(thresholds, avg_scores, 'go-', linewidth=3, markersize=10, label='Enhanced Algorithm')
        ax2.axhline(y=0.010, color='r', linestyle='--', linewidth=2, label='Original (~0.010)')
        ax2.set_title('Average Ensemble Score vs Threshold (Real Results)', fontweight='bold')
        ax2.set_xlabel('Threshold')
        ax2.set_ylabel('Average Ensemble Score')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Add actual values on points
        for i, (t, s) in enumerate(zip(thresholds, avg_scores)):
            ax2.annotate(f'{s:.3f}', (t, s), textcoords="offset points", xytext=(0,10), ha='center')
        
        # 3. High Quality Matches vs Threshold (Real Data)
        high_quality = [self.test_results[t]['high_quality_matches'] for t in thresholds]
        ax3.bar(thresholds, high_quality, color=['lightblue', 'blue', 'darkblue', 'navy'], alpha=0.7)
        ax3.set_title('High Quality Matches (>0.8) vs Threshold (Real Results)', fontweight='bold')
        ax3.set_xlabel('Threshold')
        ax3.set_ylabel('Number of High Quality Matches')
        ax3.grid(True, alpha=0.3)
        
        # Add values on bars
        for i, (t, hq) in enumerate(zip(thresholds, high_quality)):
            ax3.text(t, hq + 0.1, str(hq), ha='center', va='bottom', fontweight='bold')
        
        # 4. Ensemble Component Breakdown (Real Data from 0.8 threshold)
        if 0.8 in self.test_results:
            components = ['Semantic', 'Lexical', 'String', 'Code']
            scores = [
                self.test_results[0.8]['avg_semantic_score'],
                self.test_results[0.8]['avg_lexical_score'], 
                self.test_results[0.8]['avg_string_score'],
                self.test_results[0.8]['avg_code_score']
            ]
            
            bars = ax4.bar(components, scores, color=['skyblue', 'lightgreen', 'orange', 'pink'])
            ax4.set_title('Real Ensemble Component Scores (Threshold 0.8)', fontweight='bold')
            ax4.set_ylabel('Average Component Score')
            ax4.grid(True, alpha=0.3)
            
            # Add values on bars
            for bar, score in zip(bars, scores):
                ax4.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                        f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('output/real_enhanced_algorithm_results.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ Real results visualization saved: real_enhanced_algorithm_results.png")
    
    def export_real_results(self):
        """Export real test results"""
        print(f"\n💾 EXPORTING REAL TEST RESULTS")
        print("=" * 40)
        
        if not self.test_results:
            print("❌ No results to export")
            return
        
        # Create summary DataFrame
        summary_data = []
        for threshold, results in self.test_results.items():
            summary_data.append({
                'Threshold': threshold,
                'Total_Items': results['total_items'],
                'Matched_Items': results['matched_items'],
                'Coverage_Percent': results['coverage_percent'],
                'Avg_Ensemble_Score': results['avg_ensemble_score'],
                'Avg_Semantic_Score': results['avg_semantic_score'],
                'Avg_Lexical_Score': results['avg_lexical_score'],
                'Avg_String_Score': results['avg_string_score'],
                'Avg_Code_Score': results['avg_code_score'],
                'High_Quality_Matches': results['high_quality_matches'],
                'Excellent_Matches': results['excellent_matches'],
                'Avg_Processing_Time_Sec': results['avg_processing_time_sec']
            })
        
        summary_df = pd.DataFrame(summary_data)
        
        # Export to Excel
        with pd.ExcelWriter('output/real_enhanced_algorithm_test_results.xlsx', engine='openpyxl') as writer:
            summary_df.to_excel(writer, sheet_name='Summary_Results', index=False)
            
            # Export detailed matches for each threshold
            for threshold, results in self.test_results.items():
                if results['matches']:
                    matches_df = pd.DataFrame(results['matches'])
                    sheet_name = f'Matches_Threshold_{str(threshold).replace(".", "_")}'
                    matches_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"✅ Real test results exported: output/real_enhanced_algorithm_test_results.xlsx")

def main():
    """Main execution - Get REAL empirical results"""
    print("🧪 ENHANCED ALGORITHM: REAL EMPIRICAL TESTING")
    print("=" * 70)
    print("NO MORE ESTIMATES - GETTING ACTUAL RESULTS")
    print("")
    
    tester = RealEnhancedAlgorithmTest()
    
    # Load real data
    if not tester.load_real_data():
        print("❌ Failed to load data")
        return None
    
    # Test on subset first (100 items)
    print("🎯 Phase 1: Testing on 100-item subset")
    results = tester.test_on_subset(subset_size=100)
    
    if results:
        # Compare with original
        tester.compare_with_original_real()
        
        # Create visualizations
        tester.create_real_results_visualization()
        
        # Export results
        tester.export_real_results()
        
        print(f"\n✅ REAL ENHANCED ALGORITHM TEST COMPLETE")
        print(f"📊 Actual empirical results obtained (no more estimates!)")
        print(f"📈 Files generated:")
        print(f"   - real_enhanced_algorithm_test_results.xlsx")
        print(f"   - real_enhanced_algorithm_results.png")
        
        # Print key real findings
        if 0.8 in results:
            r = results[0.8]
            print(f"\n🎯 KEY REAL FINDINGS (Threshold 0.8):")
            print(f"   Real Coverage: {r['coverage_percent']:.1f}%")
            print(f"   Real Avg Score: {r['avg_ensemble_score']:.3f}")
            print(f"   Real High Quality: {r['high_quality_matches']} matches")
            print(f"   Real Processing Time: {r['avg_processing_time_sec']:.3f}s per item")
    
    return tester

if __name__ == "__main__":
    tester = main()
