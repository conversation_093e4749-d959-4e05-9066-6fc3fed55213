#!/usr/bin/env python3
"""
Test script to verify BOM Analysis environment installation
"""

import sys
import importlib
import subprocess

def test_python_version():
    """Test Python version compatibility."""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 8:
        print("✅ Python version is compatible")
        return True
    else:
        print("❌ Python version is not compatible (need 3.8+)")
        return False

def test_package_import(package_name, display_name=None):
    """Test if a package can be imported."""
    if display_name is None:
        display_name = package_name
    
    try:
        module = importlib.import_module(package_name)
        version = getattr(module, '__version__', 'unknown')
        print(f"✅ {display_name}: {version}")
        return True
    except ImportError:
        print(f"❌ {display_name}: Not installed")
        return False

def test_core_packages():
    """Test core data science packages."""
    print("\n📦 Testing Core Packages:")
    print("-" * 30)
    
    packages = [
        ('pandas', 'Pandas'),
        ('numpy', 'NumPy'),
        ('sklearn', 'Scikit-learn'),
        ('scipy', 'SciPy'),
        ('openpyxl', 'OpenPyXL'),
    ]
    
    results = []
    for package, name in packages:
        results.append(test_package_import(package, name))
    
    return all(results)

def test_ml_packages():
    """Test machine learning packages."""
    print("\n🤖 Testing ML/NLP Packages:")
    print("-" * 30)
    
    packages = [
        ('sentence_transformers', 'Sentence Transformers'),
        ('transformers', 'Transformers'),
        ('torch', 'PyTorch'),
        ('networkx', 'NetworkX'),
    ]
    
    results = []
    for package, name in packages:
        results.append(test_package_import(package, name))
    
    return all(results)

def test_visualization_packages():
    """Test visualization packages."""
    print("\n📊 Testing Visualization Packages:")
    print("-" * 30)
    
    packages = [
        ('matplotlib', 'Matplotlib'),
        ('seaborn', 'Seaborn'),
        ('plotly', 'Plotly'),
    ]
    
    results = []
    for package, name in packages:
        results.append(test_package_import(package, name))
    
    return all(results)

def test_jupyter():
    """Test Jupyter installation."""
    print("\n📓 Testing Jupyter:")
    print("-" * 30)
    
    packages = [
        ('jupyter', 'Jupyter'),
        ('jupyterlab', 'JupyterLab'),
        ('IPython', 'IPython'),
    ]
    
    results = []
    for package, name in packages:
        results.append(test_package_import(package, name))
    
    return all(results)

def test_bom_analysis_script():
    """Test if the main BOM analysis script can be imported."""
    print("\n🔧 Testing BOM Analysis Script:")
    print("-" * 30)
    
    try:
        import bom_similarity_analysis
        print("✅ bom_similarity_analysis.py: Can be imported")
        
        # Test if main functions exist
        functions = ['load_bom_data', 'calculate_similarity', 'main']
        for func in functions:
            if hasattr(bom_similarity_analysis, func):
                print(f"✅ Function '{func}': Available")
            else:
                print(f"❌ Function '{func}': Missing")
        
        return True
    except ImportError as e:
        print(f"❌ bom_similarity_analysis.py: Cannot import - {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 BOM Analysis Environment Test")
    print("=" * 40)
    
    # Test Python version
    python_ok = test_python_version()
    
    # Test package installations
    core_ok = test_core_packages()
    ml_ok = test_ml_packages()
    viz_ok = test_visualization_packages()
    jupyter_ok = test_jupyter()
    script_ok = test_bom_analysis_script()
    
    # Summary
    print("\n📋 Test Summary:")
    print("=" * 20)
    
    tests = [
        ("Python Version", python_ok),
        ("Core Packages", core_ok),
        ("ML/NLP Packages", ml_ok),
        ("Visualization", viz_ok),
        ("Jupyter", jupyter_ok),
        ("BOM Script", script_ok),
    ]
    
    all_passed = True
    for test_name, passed in tests:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:.<20} {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 All tests passed! Your environment is ready!")
        print("\n🚀 You can now:")
        print("   • Run: python bom_similarity_analysis.py")
        print("   • Start: jupyter lab")
        print("   • Use: quick_start.bat for easy access")
    else:
        print("⚠️  Some tests failed. Please check the installation.")
        print("\n🔧 Try running:")
        print("   • setup_windows.bat (to reinstall)")
        print("   • pip install -r requirements.txt")
    
    print("\n📁 Don't forget to place your Excel files in the 'input/' folder!")

if __name__ == "__main__":
    main()
