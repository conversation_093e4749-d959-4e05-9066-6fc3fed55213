#!/usr/bin/env python3
"""
Quick test script to verify the BOM analysis logic works correctly
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

def test_basic_functionality():
    """Test basic functionality of the new BOM analysis logic"""
    print("🧪 Testing BOM Analysis Logic...")
    
    # Create sample data that mimics the BOM structure
    sample_data1 = {
        'Level': [1, 1, 2, 2, 3],
        'Item ID': ['ABC123', 'DEF456', 'GHI789', 'JKL012', 'MNO345'],
        'F-E-Line': ['Line1', 'Line2', 'Line1', 'Line3', 'Line2'],
        'Item Description': ['Motor Assembly', 'Control Unit', 'Bearing Set', 'Sensor Module', 'Cable Harness']
    }
    
    sample_data2 = {
        'Level': [1, 1, 2, 2, 3],
        'Item ID': ['ABC123', 'XYZ999', 'GHI789', 'PQR678', 'STU901'],
        'F-E-Line': ['Line1', 'Line4', 'Line1', 'Line5', 'Line6'],
        'Item Description': ['Motor Assembly', 'Power Supply', 'Bearing Set', 'Temperature Sensor', 'Wire Assembly']
    }
    
    df1 = pd.DataFrame(sample_data1)
    df2 = pd.DataFrame(sample_data2)
    
    print(f"📊 Sample Data 1: {len(df1)} rows")
    print(df1)
    print(f"\n📊 Sample Data 2: {len(df2)} rows")
    print(df2)
    
    # Test Level filtering (Level 1 and 2 only)
    print("\n🎯 Testing Level 1&2 filtering...")
    df1['Level_Numeric'] = df1['Level'].astype(str).str.extract('(\d+)').astype(float)
    df2['Level_Numeric'] = df2['Level'].astype(str).str.extract('(\d+)').astype(float)
    
    df1_filtered = df1[df1['Level_Numeric'].isin([1.0, 2.0])]
    df2_filtered = df2[df2['Level_Numeric'].isin([1.0, 2.0])]
    
    print(f"✅ Filtered Data 1 (Level 1&2): {len(df1_filtered)} rows")
    print(df1_filtered[['Level', 'Item ID', 'Item Description']])
    print(f"\n✅ Filtered Data 2 (Level 1&2): {len(df2_filtered)} rows")
    print(df2_filtered[['Level', 'Item ID', 'Item Description']])
    
    # Test similarity calculation
    print("\n🔍 Testing similarity calculation...")
    
    # Item IDs and descriptions
    item_ids1 = df1_filtered['Item ID'].astype(str).tolist()
    item_ids2 = df2_filtered['Item ID'].astype(str).tolist()
    descriptions1 = df1_filtered['Item Description'].tolist()
    descriptions2 = df2_filtered['Item Description'].tolist()
    
    # TF-IDF for descriptions
    desc_vectorizer = TfidfVectorizer(min_df=1, ngram_range=(1, 2), lowercase=True)
    desc_tfidf_matrix = desc_vectorizer.fit_transform(descriptions1 + descriptions2)
    desc_tfidf1 = desc_tfidf_matrix[:len(descriptions1)]
    desc_tfidf2 = desc_tfidf_matrix[len(descriptions1):]
    desc_similarity_matrix = cosine_similarity(desc_tfidf1, desc_tfidf2)
    
    # TF-IDF for Item IDs
    id_vectorizer = TfidfVectorizer(min_df=1, analyzer='char_wb', ngram_range=(2, 4), lowercase=True)
    id_tfidf_matrix = id_vectorizer.fit_transform(item_ids1 + item_ids2)
    id_tfidf1 = id_tfidf_matrix[:len(item_ids1)]
    id_tfidf2 = id_tfidf_matrix[len(item_ids1):]
    id_similarity_matrix = cosine_similarity(id_tfidf1, id_tfidf2)
    
    print("📈 Similarity Results:")
    print("Description Similarity Matrix:")
    print(desc_similarity_matrix)
    print("\nID Similarity Matrix:")
    print(id_similarity_matrix)
    
    # Test combined scoring
    print("\n🎯 Testing combined scoring...")
    matches = []
    
    for i in range(len(df1_filtered)):
        for j in range(len(df2_filtered)):
            desc_sim = desc_similarity_matrix[i, j]
            id_sim = id_similarity_matrix[i, j]
            
            # Check exact matches
            exact_id_match = item_ids1[i].lower().strip() == item_ids2[j].lower().strip()
            
            # Combined score
            combined_score = (desc_sim * 0.5) + (id_sim * 0.35)
            if exact_id_match:
                combined_score += 0.3
            combined_score = min(combined_score, 1.0)
            
            if combined_score > 0.6:
                match = {
                    'item1': item_ids1[i],
                    'item2': item_ids2[j],
                    'desc1': descriptions1[i],
                    'desc2': descriptions2[j],
                    'combined_score': combined_score,
                    'desc_sim': desc_sim,
                    'id_sim': id_sim,
                    'exact_id': exact_id_match
                }
                matches.append(match)
    
    print(f"🎉 Found {len(matches)} matches above threshold (0.6):")
    for match in matches:
        print(f"  • {match['item1']} ↔ {match['item2']}: {match['combined_score']:.3f}")
        print(f"    Desc: {match['desc1']} ↔ {match['desc2']}")
        print(f"    Scores - Combined: {match['combined_score']:.3f}, Desc: {match['desc_sim']:.3f}, ID: {match['id_sim']:.3f}, Exact ID: {match['exact_id']}")
        print()
    
    print("✅ Test completed successfully!")

if __name__ == "__main__":
    test_basic_functionality()
