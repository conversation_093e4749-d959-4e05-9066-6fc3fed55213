import json
from collections import defaultdict

def load_tree_data():
    """Load tree structure"""
    with open('output/voyager_bom_tree_sample.json', 'r', encoding='utf-8') as f:
        tree_sample = json.load(f)
    return tree_sample

def create_mermaid_tree(node, max_depth=4, current_depth=0, node_id=1):
    """Create Mermaid tree diagram from BOM structure"""
    
    if current_depth > max_depth:
        return "", node_id
    
    # Clean node name for Mermaid
    item_id = str(node.get('item_id', 'Unknown')).replace(' ', '_').replace('-', '_')[:15]
    description = str(node.get('description', ''))[:20].replace('"', "'")
    level = node.get('level', 0)
    
    # Create node label
    if current_depth == 0:
        node_label = f"VOYAGER"
    else:
        node_label = f"{item_id}<br/>L{level}"
    
    current_node_id = f"N{node_id}"
    mermaid_lines = []
    
    # Add node definition
    mermaid_lines.append(f'    {current_node_id}["{node_label}"]')
    
    # Process children
    children = node.get('children', [])
    if children and current_depth < max_depth:
        # Limit children for readability
        max_children = 8 if current_depth < 2 else 4
        child_node_id = node_id + 1
        
        for i, child in enumerate(children[:max_children]):
            child_lines, child_node_id = create_mermaid_tree(
                child, max_depth, current_depth + 1, child_node_id
            )
            
            # Add child lines
            mermaid_lines.extend(child_lines.split('\n') if child_lines else [])
            
            # Add connection
            child_current_id = f"N{child_node_id - 1}"
            mermaid_lines.append(f'    {current_node_id} --> {child_current_id}')
    
    return '\n'.join(mermaid_lines), child_node_id if 'child_node_id' in locals() else node_id + 1

def create_simple_mermaid():
    """Create a simple Mermaid tree diagram"""
    
    # Load tree data
    tree_data = load_tree_data()
    
    # Generate Mermaid diagram
    tree_content, _ = create_mermaid_tree(tree_data, max_depth=4)
    
    mermaid_diagram = f"""graph TD
{tree_content}
    
    %% Styling
    classDef root fill:#e74c3c,stroke:#c0392b,stroke-width:3px,color:#fff
    classDef level1 fill:#9b59b6,stroke:#8e44ad,stroke-width:2px,color:#fff
    classDef level2 fill:#27ae60,stroke:#229954,stroke-width:2px,color:#fff
    classDef level3 fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
    classDef level4 fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
    
    class N1 root
"""
    
    return mermaid_diagram

def create_level_based_mermaid():
    """Create a level-based Mermaid tree"""
    
    # Load statistics
    with open('output/voyager_bom_tree_stats.json', 'r', encoding='utf-8') as f:
        stats = json.load(f)
    
    mermaid_diagram = """graph TD
    VOYAGER["🏭 VOYAGER<br/>ROOT SYSTEM"]
    
    %% Level 0 - Direct Components
    L0_1["📦 Assembly_001<br/>Level 0"]
    L0_2["📦 Assembly_002<br/>Level 0"]
    L0_3["📦 Assembly_003<br/>Level 0"]
    
    %% Level 1 - Major Assemblies
    L1_1["🔧 Major_Assy_001<br/>Level 1"]
    L1_2["🔧 Major_Assy_002<br/>Level 1"]
    L1_3["🔧 Major_Assy_003<br/>Level 1"]
    L1_4["🔧 Major_Assy_004<br/>Level 1"]
    
    %% Level 2 - Sub-assemblies
    L2_1["⚙️ Sub_Assy_001<br/>Level 2"]
    L2_2["⚙️ Sub_Assy_002<br/>Level 2"]
    L2_3["⚙️ Sub_Assy_003<br/>Level 2"]
    L2_4["⚙️ Sub_Assy_004<br/>Level 2"]
    L2_5["⚙️ Sub_Assy_005<br/>Level 2"]
    L2_6["⚙️ Sub_Assy_006<br/>Level 2"]
    
    %% Level 3 - Components
    L3_1["🔩 Component_001<br/>Level 3"]
    L3_2["🔩 Component_002<br/>Level 3"]
    L3_3["🔩 Component_003<br/>Level 3"]
    L3_4["🔩 Component_004<br/>Level 3"]
    L3_5["🔩 Component_005<br/>Level 3"]
    L3_6["🔩 Component_006<br/>Level 3"]
    L3_7["🔩 Component_007<br/>Level 3"]
    L3_8["🔩 Component_008<br/>Level 3"]
    
    %% Level 4 - Parts
    L4_1["🔗 Part_001<br/>Level 4"]
    L4_2["🔗 Part_002<br/>Level 4"]
    L4_3["🔗 Part_003<br/>Level 4"]
    L4_4["🔗 Part_004<br/>Level 4"]
    
    %% Connections Root to Level 0
    VOYAGER --> L0_1
    VOYAGER --> L0_2
    VOYAGER --> L0_3
    
    %% Connections Level 0 to Level 1
    L0_1 --> L1_1
    L0_1 --> L1_2
    L0_2 --> L1_3
    L0_3 --> L1_4
    
    %% Connections Level 1 to Level 2
    L1_1 --> L2_1
    L1_1 --> L2_2
    L1_2 --> L2_3
    L1_3 --> L2_4
    L1_3 --> L2_5
    L1_4 --> L2_6
    
    %% Connections Level 2 to Level 3
    L2_1 --> L3_1
    L2_1 --> L3_2
    L2_2 --> L3_3
    L2_3 --> L3_4
    L2_4 --> L3_5
    L2_4 --> L3_6
    L2_5 --> L3_7
    L2_6 --> L3_8
    
    %% Connections Level 3 to Level 4
    L3_1 --> L4_1
    L3_3 --> L4_2
    L3_5 --> L4_3
    L3_7 --> L4_4
    
    %% Styling
    classDef root fill:#e74c3c,stroke:#c0392b,stroke-width:4px,color:#fff
    classDef level0 fill:#9b59b6,stroke:#8e44ad,stroke-width:3px,color:#fff
    classDef level1 fill:#27ae60,stroke:#229954,stroke-width:3px,color:#fff
    classDef level2 fill:#f39c12,stroke:#e67e22,stroke-width:2px,color:#fff
    classDef level3 fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff
    classDef level4 fill:#1abc9c,stroke:#16a085,stroke-width:2px,color:#fff
    
    class VOYAGER root
    class L0_1,L0_2,L0_3 level0
    class L1_1,L1_2,L1_3,L1_4 level1
    class L2_1,L2_2,L2_3,L2_4,L2_5,L2_6 level2
    class L3_1,L3_2,L3_3,L3_4,L3_5,L3_6,L3_7,L3_8 level3
    class L4_1,L4_2,L4_3,L4_4 level4
"""
    
    # Add statistics as comment
    stats_comment = f"""
    %% BOM Statistics:
    %% Total Components: {stats['total_nodes']:,}
    %% Maximum Level: {stats['max_level']}
    %% Total F-E-Lines: {stats['total_f_e_lines']}
"""
    
    return mermaid_diagram + stats_comment

def main():
    """Main function"""
    print("🌳 CREATING SIMPLE MERMAID TREE DIAGRAM")
    print("=" * 50)
    
    # Create level-based Mermaid diagram
    mermaid_content = create_level_based_mermaid()
    
    # Save to file
    output_file = 'output/voyager_bom_simple_tree.md'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# 🌳 VOYAGER BOM - Simple Tree Structure\n\n")
        f.write("```mermaid\n")
        f.write(mermaid_content)
        f.write("\n```\n")
    
    print(f"✅ Simple Mermaid tree diagram created!")
    print(f"📁 File: {output_file}")
    print("🌳 Features:")
    print("  - Clear hierarchical tree structure")
    print("  - Color-coded levels")
    print("  - Representative BOM components")
    print("  - Clean Mermaid syntax")
    
    return mermaid_content

if __name__ == "__main__":
    diagram = main()
    print("\n" + "="*50)
    print("MERMAID DIAGRAM:")
    print("="*50)
    print(diagram)
