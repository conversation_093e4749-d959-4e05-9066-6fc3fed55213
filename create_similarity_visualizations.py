#!/usr/bin/env python3
"""
Create comprehensive visualizations for similarity analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.spatial.distance import squareform
import networkx as nx
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

def create_similarity_matrix_visualization():
    """Create 68x68 similarity matrix visualization"""
    
    # Load the data
    df = pd.read_csv('output/comprehensive_colpali_extraction.csv')
    pages_2_3 = df[df['page_number'].isin([2, 3])].copy().reset_index(drop=True)
    
    # Load semantic similarity results
    semantic_df = pd.read_csv('output/semantic_similarity_matches.csv')
    
    # Create 68x68 similarity matrix
    n_components = len(pages_2_3)
    similarity_matrix = np.zeros((n_components, n_components))
    
    # Fill diagonal with 1.0 (self-similarity)
    np.fill_diagonal(similarity_matrix, 1.0)
    
    # Create component ID to index mapping
    id_to_idx = {row['product_id']: idx for idx, row in pages_2_3.iterrows()}
    
    # Fill matrix with semantic similarities
    for _, match in semantic_df.iterrows():
        id1, id2 = match['component1_id'], match['component2_id']
        if id1 in id_to_idx and id2 in id_to_idx:
            idx1, idx2 = id_to_idx[id1], id_to_idx[id2]
            similarity = match['semantic_similarity']
            similarity_matrix[idx1][idx2] = similarity
            similarity_matrix[idx2][idx1] = similarity  # Symmetric
    
    # Create visualization
    plt.figure(figsize=(16, 14))
    
    # Create heatmap
    mask = similarity_matrix == 0
    sns.heatmap(similarity_matrix, 
                mask=mask,
                annot=False, 
                cmap='RdYlBu_r', 
                vmin=0, vmax=1,
                square=True,
                linewidths=0.1,
                cbar_kws={'label': 'Semantic Similarity Score'})
    
    plt.title('68x68 Component Similarity Matrix (Pages 2-3)\nSBERT Semantic Similarity ≥ 0.7', 
              fontsize=16, fontweight='bold')
    plt.xlabel('Component Index', fontsize=12)
    plt.ylabel('Component Index', fontsize=12)
    
    # Add component IDs as labels (every 5th for readability)
    component_ids = pages_2_3['product_id'].tolist()
    tick_positions = range(0, len(component_ids), 5)
    tick_labels = [component_ids[i] for i in tick_positions]
    
    plt.xticks(tick_positions, tick_labels, rotation=45, ha='right')
    plt.yticks(tick_positions, tick_labels, rotation=0)
    
    plt.tight_layout()
    plt.savefig('output/similarity_matrix_68x68.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 68x68 Similarity Matrix saved: output/similarity_matrix_68x68.png")
    
    return similarity_matrix, pages_2_3

def create_dendrogram_visualization(similarity_matrix, pages_2_3):
    """Create hierarchical clustering dendrogram"""

    try:
        # Convert similarity to distance, ensuring non-negative values
        distance_matrix = 1 - similarity_matrix
        distance_matrix = np.maximum(distance_matrix, 0)  # Ensure non-negative

        # Ensure diagonal is 0 (distance to self)
        np.fill_diagonal(distance_matrix, 0)

        # Add small epsilon to avoid zero distances causing issues
        epsilon = 1e-10
        distance_matrix = distance_matrix + epsilon
        np.fill_diagonal(distance_matrix, 0)  # Keep diagonal as 0

        # Convert to condensed distance matrix for linkage
        condensed_distances = squareform(distance_matrix, checks=False)

        # Perform hierarchical clustering
        linkage_matrix = linkage(condensed_distances, method='average')  # Changed from ward to average

        # Create dendrogram
        plt.figure(figsize=(20, 10))

        dendrogram(linkage_matrix,
                   labels=pages_2_3['product_id'].tolist(),
                   leaf_rotation=90,
                   leaf_font_size=8)

        plt.title('Component Similarity Dendrogram (Pages 2-3)\nHierarchical Clustering based on SBERT Semantic Similarity',
                  fontsize=16, fontweight='bold')
        plt.xlabel('Component ID', fontsize=12)
        plt.ylabel('Distance (1 - Similarity)', fontsize=12)

        plt.tight_layout()
        plt.savefig('output/similarity_dendrogram.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("✅ Similarity Dendrogram saved: output/similarity_dendrogram.png")

    except Exception as e:
        print(f"⚠️ Could not create dendrogram: {e}")
        print("   Skipping dendrogram visualization...")

def create_spatial_clustering_visualization():
    """Create spatial clustering visualization"""
    
    # Load spatial clusters
    with open('output/spatial_clusters_analysis.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Load the data
    df = pd.read_csv('output/comprehensive_colpali_extraction.csv')
    pages_2_3 = df[df['page_number'].isin([2, 3])].copy()
    
    # Create spatial plots
    fig, axes = plt.subplots(1, 2, figsize=(20, 8))
    
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    
    for i, page_num in enumerate([2, 3]):
        ax = axes[i]
        page_data = pages_2_3[pages_2_3['page_number'] == page_num]
        
        # Plot all components
        scatter = ax.scatter(page_data['center_x'], page_data['center_y'], 
                           c=page_data['color_detected'].map({'Blue': 'blue', 'Purple': 'purple', 'Red': 'red'}),
                           s=100, alpha=0.7, edgecolors='black', linewidth=1)
        
        # Add component IDs as labels
        for _, row in page_data.iterrows():
            ax.annotate(row['product_id'], 
                       (row['center_x'], row['center_y']),
                       xytext=(5, 5), textcoords='offset points',
                       fontsize=6, ha='left')
        
        ax.set_title(f'Page {page_num} - Spatial Distribution\n{len(page_data)} Components', 
                    fontsize=14, fontweight='bold')
        ax.set_xlabel('X Position (pixels)', fontsize=12)
        ax.set_ylabel('Y Position (pixels)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.invert_yaxis()  # Invert Y axis to match image coordinates
    
    plt.tight_layout()
    plt.savefig('output/spatial_clustering_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Spatial Clustering Visualization saved: output/spatial_clustering_visualization.png")

def create_component_family_network():
    """Create network visualization of component families"""
    
    # Load exact matches
    exact_df = pd.read_csv('output/exact_description_matches.csv')
    
    # Create network graph
    G = nx.Graph()
    
    # Add nodes and edges from exact matches
    for _, row in exact_df.iterrows():
        G.add_edge(row['component1_id'], row['component2_id'], 
                  weight=row['similarity_score'],
                  match_type=row['match_type'])
    
    # Create visualization
    plt.figure(figsize=(16, 12))
    
    # Use spring layout for better visualization
    pos = nx.spring_layout(G, k=3, iterations=50)
    
    # Draw network
    nx.draw_networkx_nodes(G, pos, node_size=300, node_color='lightblue', alpha=0.7)
    nx.draw_networkx_edges(G, pos, alpha=0.5, width=1)
    nx.draw_networkx_labels(G, pos, font_size=8, font_weight='bold')
    
    plt.title('Component Family Network\nBased on Exact Description Matches', 
              fontsize=16, fontweight='bold')
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig('output/component_family_network.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Component Family Network saved: output/component_family_network.png")

def create_pattern_detection_report():
    """Create comprehensive pattern detection report"""
    
    # Load all analysis results
    exact_df = pd.read_csv('output/exact_description_matches.csv')
    semantic_df = pd.read_csv('output/semantic_similarity_matches.csv')
    
    # Load original data
    df = pd.read_csv('output/comprehensive_colpali_extraction.csv')
    pages_2_3 = df[df['page_number'].isin([2, 3])].copy()
    
    report = []
    report.append("COMPREHENSIVE PATTERN DETECTION REPORT")
    report.append("=" * 60)
    report.append("")
    
    # 1. Exact Match Patterns
    report.append("1. EXACT DESCRIPTION MATCH PATTERNS:")
    report.append("-" * 40)
    
    desc_groups = exact_df.groupby('shared_description')
    for desc, group in desc_groups:
        unique_components = set(group['component1_id'].tolist() + group['component2_id'].tolist())
        report.append(f"  '{desc}': {len(unique_components)} components")
        for comp_id in sorted(unique_components):
            comp_data = pages_2_3[pages_2_3['product_id'] == comp_id].iloc[0]
            report.append(f"    - {comp_id} (Page {comp_data['page_number']}, {comp_data['color_detected']})")
        report.append("")
    
    # 2. ID Family Patterns
    report.append("2. PRODUCT ID FAMILY PATTERNS:")
    report.append("-" * 35)
    
    id_families = defaultdict(list)
    for _, row in pages_2_3.iterrows():
        prefix = row['product_id'].split('-')[0].split('_')[0]
        if prefix.isalpha():
            id_families[prefix].append(row)
    
    for family, components in sorted(id_families.items()):
        if len(components) > 1:
            report.append(f"  {family} Family: {len(components)} components")
            for comp in components:
                report.append(f"    - {comp['product_id']}: {comp['component_text']} (Page {comp['page_number']})")
            report.append("")
    
    # 3. High Semantic Similarity Clusters
    report.append("3. HIGH SEMANTIC SIMILARITY CLUSTERS (≥0.9):")
    report.append("-" * 45)
    
    high_sim = semantic_df[semantic_df['semantic_similarity'] >= 0.9]
    similarity_groups = defaultdict(list)
    
    for _, row in high_sim.iterrows():
        key = f"{row['component1_text']} <-> {row['component2_text']}"
        similarity_groups[key].append(row)
    
    for pattern, matches in similarity_groups.items():
        if len(matches) >= 1:
            report.append(f"  Pattern: {pattern}")
            report.append(f"    Similarity: {matches[0]['semantic_similarity']:.3f}")
            report.append(f"    Components: {len(matches)} pairs")
            report.append("")
    
    # 4. Cross-Page Relationships
    report.append("4. CROSS-PAGE COMPONENT RELATIONSHIPS:")
    report.append("-" * 40)
    
    cross_page_exact = exact_df[exact_df['component1_page'] != exact_df['component2_page']]
    cross_page_semantic = semantic_df[semantic_df['component1_page'] != semantic_df['component2_page']]
    
    report.append(f"  Exact matches across pages: {len(cross_page_exact)}")
    report.append(f"  Semantic matches across pages: {len(cross_page_semantic)}")
    report.append("")
    
    # Save report
    with open('output/pattern_detection_report.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("✅ Pattern Detection Report saved: output/pattern_detection_report.txt")

def create_cross_validation_metrics():
    """Create cross-validation metrics for known relationships"""
    
    # Load data
    exact_df = pd.read_csv('output/exact_description_matches.csv')
    semantic_df = pd.read_csv('output/semantic_similarity_matches.csv')
    
    # Calculate metrics
    metrics = {}
    
    # Precision: How many semantic matches are also exact matches?
    semantic_pairs = set()
    for _, row in semantic_df.iterrows():
        pair = tuple(sorted([row['component1_id'], row['component2_id']]))
        semantic_pairs.add(pair)
    
    exact_pairs = set()
    for _, row in exact_df.iterrows():
        pair = tuple(sorted([row['component1_id'], row['component2_id']]))
        exact_pairs.add(pair)
    
    # True positives: semantic matches that are also exact matches
    true_positives = len(semantic_pairs.intersection(exact_pairs))
    
    # False positives: semantic matches that are not exact matches
    false_positives = len(semantic_pairs - exact_pairs)
    
    # False negatives: exact matches that were not found by semantic analysis
    false_negatives = len(exact_pairs - semantic_pairs)
    
    # Calculate metrics
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    metrics = {
        'true_positives': true_positives,
        'false_positives': false_positives,
        'false_negatives': false_negatives,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'total_semantic_matches': len(semantic_pairs),
        'total_exact_matches': len(exact_pairs)
    }
    
    # Save metrics
    with open('output/cross_validation_metrics.txt', 'w', encoding='utf-8') as f:
        f.write("CROSS-VALIDATION METRICS\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"True Positives: {metrics['true_positives']}\n")
        f.write(f"False Positives: {metrics['false_positives']}\n")
        f.write(f"False Negatives: {metrics['false_negatives']}\n\n")
        f.write(f"Precision: {metrics['precision']:.3f}\n")
        f.write(f"Recall: {metrics['recall']:.3f}\n")
        f.write(f"F1-Score: {metrics['f1_score']:.3f}\n\n")
        f.write(f"Total Semantic Matches: {metrics['total_semantic_matches']}\n")
        f.write(f"Total Exact Matches: {metrics['total_exact_matches']}\n")
    
    print("✅ Cross-Validation Metrics saved: output/cross_validation_metrics.txt")
    print(f"   Precision: {metrics['precision']:.3f}, Recall: {metrics['recall']:.3f}, F1: {metrics['f1_score']:.3f}")
    
    return metrics

def main():
    """Main execution function"""
    print("🎨 CREATING COMPREHENSIVE SIMILARITY VISUALIZATIONS")
    print("=" * 60)
    
    # Create all visualizations
    similarity_matrix, pages_2_3 = create_similarity_matrix_visualization()
    create_dendrogram_visualization(similarity_matrix, pages_2_3)
    create_spatial_clustering_visualization()
    create_component_family_network()
    create_pattern_detection_report()
    metrics = create_cross_validation_metrics()
    
    print("\n🎯 VISUALIZATION SUMMARY:")
    print("=" * 30)
    print("✅ 68x68 Similarity Matrix")
    print("✅ Hierarchical Clustering Dendrogram")
    print("✅ Spatial Clustering Plots")
    print("✅ Component Family Network")
    print("✅ Pattern Detection Report")
    print("✅ Cross-Validation Metrics")
    
    print(f"\n📊 VALIDATION RESULTS:")
    print(f"   Precision: {metrics['precision']:.3f}")
    print(f"   Recall: {metrics['recall']:.3f}")
    print(f"   F1-Score: {metrics['f1_score']:.3f}")

if __name__ == "__main__":
    main()
