#!/usr/bin/env python3
"""
Visualiser la table complète Voyager BOM + PCM
"""

import pandas as pd
import numpy as np

def view_complete_table():
    """Visualiser la table complète"""
    print("📊 VISUALISATION TABLE COMPLÈTE VOYAGER BOM + PCM")
    print("=" * 70)
    
    try:
        # Charger la table complète
        df = pd.read_excel('output/voyager_complete_bom_pcm_table.xlsx', sheet_name='Complete_Table')
        
        print(f"📋 Table chargée: {len(df)} lignes, {len(df.columns)} colonnes")
        print(f"📊 Colonnes: {list(df.columns)}")
        
        # Statistiques générales
        print(f"\n📈 STATISTIQUES GÉNÉRALES")
        print("=" * 40)
        
        total_items = len(df)
        with_pcm = len(df[df['Item_ID_PCM'] != ''])
        without_pcm = total_items - with_pcm
        coverage = (with_pcm / total_items) * 100
        
        print(f"Total items BOM: {total_items:,}")
        print(f"Avec correspondance PCM: {with_pcm:,} ({coverage:.1f}%)")
        print(f"Sans correspondance PCM: {without_pcm:,} ({100-coverage:.1f}%)")
        
        # Analyse par mandatoriness
        print(f"\n🎨 ANALYSE PAR MANDATORINESS")
        print("=" * 40)
        
        mandatoriness_counts = df[df['Mandatoriness'] != '']['Mandatoriness'].value_counts()
        for mandatoriness, count in mandatoriness_counts.items():
            percentage = (count / with_pcm) * 100
            print(f"{mandatoriness}: {count} items ({percentage:.1f}%)")
        
        # Exemples de correspondances
        print(f"\n📋 EXEMPLES DE CORRESPONDANCES BOM-PCM")
        print("=" * 50)
        
        # Prendre les meilleures correspondances
        good_matches = df[(df['Item_ID_PCM'] != '') & (df['Similarity_Score'] > 0.7)].head(10)
        
        for idx, row in good_matches.iterrows():
            print(f"\n🔗 Correspondance {idx + 1}:")
            print(f"   BOM: {row['Item_ID']} - {row['Item_Description'][:50]}...")
            print(f"   PCM: {row['Item_ID_PCM']} - {row['Description_PCM'][:50]}...")
            print(f"   Mandatoriness: [{row['Mandatoriness']}]")
            print(f"   Similarité: {row['Similarity_Score']:.3f}")
            print(f"   Page: {row['Page']}, Position: ({row['Position_X']}, {row['Position_Y']})")
            if row['Similar_in_PCM']:
                print(f"   Similaires PCM: {row['Similar_in_PCM']}")
        
        # Analyse par niveau BOM
        print(f"\n📊 ANALYSE PAR NIVEAU BOM")
        print("=" * 35)
        
        level_analysis = df.groupby('Level').agg({
            'Item_ID': 'count',
            'Item_ID_PCM': lambda x: (x != '').sum(),
            'Similarity_Score': 'mean'
        }).round(3)
        level_analysis.columns = ['Total_Items', 'With_PCM', 'Avg_Similarity']
        level_analysis['Coverage_%'] = (level_analysis['With_PCM'] / level_analysis['Total_Items'] * 100).round(1)
        
        print(level_analysis.head(10).to_string())
        
        # Analyse par F-E-Line
        print(f"\n📊 ANALYSE PAR F-E-LINE (TOP 10)")
        print("=" * 40)
        
        fe_line_analysis = df.groupby('F_E_Line').agg({
            'Item_ID': 'count',
            'Item_ID_PCM': lambda x: (x != '').sum()
        })
        fe_line_analysis.columns = ['Total_Items', 'With_PCM']
        fe_line_analysis['Coverage_%'] = (fe_line_analysis['With_PCM'] / fe_line_analysis['Total_Items'] * 100).round(1)
        fe_line_analysis = fe_line_analysis.sort_values('Total_Items', ascending=False)
        
        print(fe_line_analysis.head(10).to_string())
        
        # Items avec similarités PCM
        print(f"\n🔗 ITEMS AVEC SIMILARITÉS DANS LE PCM")
        print("=" * 45)
        
        with_similar = df[(df['Similar_in_PCM'] != '') & (df['Similar_in_PCM'].notna())]
        print(f"Items avec similarités PCM: {len(with_similar)}")
        
        if len(with_similar) > 0:
            print(f"\nExemples:")
            for idx, row in with_similar.head(5).iterrows():
                print(f"  {row['Item_ID_PCM']}: {row['Similar_in_PCM']}")
        
        # Distribution des scores de similarité
        print(f"\n📈 DISTRIBUTION SCORES DE SIMILARITÉ")
        print("=" * 45)
        
        similarity_scores = df[df['Similarity_Score'] > 0]['Similarity_Score']
        if len(similarity_scores) > 0:
            print(f"Score moyen: {similarity_scores.mean():.3f}")
            print(f"Score médian: {similarity_scores.median():.3f}")
            print(f"Score min: {similarity_scores.min():.3f}")
            print(f"Score max: {similarity_scores.max():.3f}")
            
            # Répartition par tranches
            bins = [0, 0.6, 0.7, 0.8, 0.9, 1.0]
            labels = ['0.6-0.7', '0.7-0.8', '0.8-0.9', '0.9-1.0']
            score_distribution = pd.cut(similarity_scores, bins=bins, labels=labels, include_lowest=True).value_counts()
            
            print(f"\nRépartition par tranches:")
            for label, count in score_distribution.items():
                percentage = (count / len(similarity_scores)) * 100
                print(f"  {label}: {count} ({percentage:.1f}%)")
        
        return df
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_mandatoriness_patterns():
    """Analyser les patterns de mandatoriness"""
    print(f"\n🎨 ANALYSE DÉTAILLÉE MANDATORINESS")
    print("=" * 50)
    
    try:
        df = pd.read_excel('output/voyager_complete_bom_pcm_table.xlsx', sheet_name='Complete_Table')
        
        # Analyser les patterns par mandatoriness
        mandatoriness_data = df[df['Mandatoriness'] != ''].copy()
        
        for mandatoriness in ['Blue', 'Purple', 'Red']:
            subset = mandatoriness_data[mandatoriness_data['Mandatoriness'] == mandatoriness]
            
            if len(subset) > 0:
                print(f"\n📊 {mandatoriness} (Mandatory: {'Oui' if mandatoriness == 'Blue' else 'Sélectionnable' if mandatoriness == 'Purple' else 'Optionnel'})")
                print(f"   Nombre d'items: {len(subset)}")
                print(f"   Score similarité moyen: {subset['Similarity_Score'].mean():.3f}")
                
                # Exemples
                print(f"   Exemples:")
                for idx, row in subset.head(3).iterrows():
                    print(f"     {row['Item_ID_PCM']}: {row['Description_PCM'][:40]}...")
        
    except Exception as e:
        print(f"❌ Erreur analyse mandatoriness: {e}")

def main():
    """Fonction principale"""
    df = view_complete_table()
    
    if df is not None:
        analyze_mandatoriness_patterns()
        
        print(f"\n✅ VISUALISATION TERMINÉE")
        print(f"📊 Table complète analysée avec succès")
        print(f"📋 Fichier Excel: output/voyager_complete_bom_pcm_table.xlsx")
        print(f"🎯 Prêt pour utilisation et validation")
    
    return df

if __name__ == "__main__":
    df = main()
