#!/usr/bin/env python3
"""
Comprehensive Similarity Analysis for ColPali Components
Starting with exact matches, then expanding to multi-dimensional analysis
"""

import pandas as pd
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.cluster import DBSCAN
from sklearn.metrics.pairwise import cosine_similarity
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.spatial.distance import pdist, squareform
import matplotlib.pyplot as plt
import seaborn as sns
import re
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveSimilarityAnalyzer:
    def __init__(self, data_path='output/comprehensive_colpali_extraction.csv'):
        """Initialize the analyzer with ColPali extraction data"""
        self.df = pd.read_csv(data_path)
        self.sbert_model = None
        self.similarity_results = {}
        
    def load_sbert_model(self):
        """Load SBERT model for semantic similarity"""
        print("🔄 Loading SBERT model...")
        self.sbert_model = SentenceTransformer('all-MiniLM-L6-v2')
        print("✅ SBERT model loaded successfully")
        
    def exact_match_analysis(self, subset_df=None):
        """Phase 1: Exact description and ID matching analysis"""
        df = subset_df if subset_df is not None else self.df
        
        print("\n🎯 PHASE 1: EXACT MATCH ANALYSIS")
        print("=" * 50)
        
        # 1. Exact Description Matches
        exact_desc_matches = []
        desc_groups = df.groupby('component_text')
        
        for desc, group in desc_groups:
            if len(group) > 1:
                for i, row1 in group.iterrows():
                    for j, row2 in group.iterrows():
                        if i < j:  # Avoid duplicates
                            exact_desc_matches.append({
                                'component1_id': row1['product_id'],
                                'component2_id': row2['product_id'],
                                'component1_page': row1['page_number'],
                                'component2_page': row2['page_number'],
                                'shared_description': desc,
                                'match_type': 'exact_description',
                                'similarity_score': 1.0
                            })
        
        # 2. Exact Product ID Matches
        exact_id_matches = []
        id_groups = df.groupby('product_id')
        
        for prod_id, group in id_groups:
            if len(group) > 1:
                for i, row1 in group.iterrows():
                    for j, row2 in group.iterrows():
                        if i < j:  # Avoid duplicates
                            exact_id_matches.append({
                                'component1_id': row1['product_id'],
                                'component2_id': row2['product_id'],
                                'component1_page': row1['page_number'],
                                'component2_page': row2['page_number'],
                                'shared_product_id': prod_id,
                                'match_type': 'exact_product_id',
                                'similarity_score': 1.0
                            })
        
        print(f"📊 Exact Description Matches: {len(exact_desc_matches)}")
        print(f"📊 Exact Product ID Matches: {len(exact_id_matches)}")
        
        # Store results
        self.similarity_results['exact_desc_matches'] = exact_desc_matches
        self.similarity_results['exact_id_matches'] = exact_id_matches
        
        return exact_desc_matches, exact_id_matches
    
    def terminology_analysis(self, subset_df=None):
        """Analyze terminology patterns and component families"""
        df = subset_df if subset_df is not None else self.df
        
        print("\n🔍 TERMINOLOGY ANALYSIS")
        print("=" * 30)
        
        # Extract component categories
        categories = defaultdict(list)
        for idx, row in df.iterrows():
            text = row['component_text']
            # Extract key terms
            if 'System' in text:
                categories['System'].append(row)
            if 'Interface' in text:
                categories['Interface'].append(row)
            if 'Controller' in text or 'Control' in text:
                categories['Controller'].append(row)
            if 'Board' in text:
                categories['Board'].append(row)
            if 'Module' in text:
                categories['Module'].append(row)
            if 'Processor' in text or 'Processing' in text:
                categories['Processor'].append(row)
            if 'Communication' in text:
                categories['Communication'].append(row)
            if 'Data' in text:
                categories['Data'].append(row)
            if 'Sensor' in text:
                categories['Sensor'].append(row)
            if 'Memory' in text:
                categories['Memory'].append(row)
        
        # Analyze ID prefixes
        id_families = defaultdict(list)
        for idx, row in df.iterrows():
            prod_id = row['product_id']
            # Extract prefix patterns
            prefix_match = re.match(r'^([A-Z]+)', prod_id)
            if prefix_match:
                prefix = prefix_match.group(1)
                id_families[prefix].append(row)
        
        print("📋 Component Categories:")
        for category, components in categories.items():
            print(f"  {category}: {len(components)} components")
            
        print("\n🆔 ID Family Patterns:")
        for family, components in id_families.items():
            print(f"  {family}: {len(components)} components")
        
        self.similarity_results['terminology_categories'] = dict(categories)
        self.similarity_results['id_families'] = dict(id_families)
        
        return categories, id_families
    
    def parent_child_bom_analysis(self):
        """Analyze parent-child relationships using BOM arborescence"""
        print("\n🌳 PARENT-CHILD BOM ANALYSIS")
        print("=" * 35)
        
        # Load BOM data for comparison
        try:
            bom_df = pd.read_excel('output/voyager_5408509_bom_pcm_aggregated.xlsx')
            print(f"📊 BOM Data Loaded: {len(bom_df)} items")
            
            # Analyze F-E-Line hierarchy patterns
            fe_line_patterns = bom_df['F_E_Line'].value_counts().head(10)
            print("🔗 Top F-E-Line Patterns:")
            for pattern, count in fe_line_patterns.items():
                print(f"  {pattern}: {count} items")
                
            # Analyze Level hierarchy
            level_distribution = bom_df['Level'].value_counts().sort_index()
            print("\n📊 BOM Level Distribution:")
            for level, count in level_distribution.items():
                print(f"  Level {level}: {count} items")
                
            self.similarity_results['bom_hierarchy'] = {
                'fe_line_patterns': fe_line_patterns.to_dict(),
                'level_distribution': level_distribution.to_dict()
            }
            
        except Exception as e:
            print(f"⚠️ Could not load BOM data: {e}")
            
    def sbert_semantic_similarity(self, subset_df=None, threshold=0.7):
        """SBERT semantic similarity analysis"""
        df = subset_df if subset_df is not None else self.df
        
        if self.sbert_model is None:
            self.load_sbert_model()
            
        print(f"\n🧠 SBERT SEMANTIC SIMILARITY (threshold: {threshold})")
        print("=" * 50)
        
        # Get component descriptions
        descriptions = df['component_text'].tolist()
        
        # Generate embeddings
        print("🔄 Generating embeddings...")
        embeddings = self.sbert_model.encode(descriptions)
        
        # Calculate similarity matrix
        similarity_matrix = cosine_similarity(embeddings)
        
        # Find high similarity pairs
        semantic_matches = []
        n_components = len(df)
        
        for i in range(n_components):
            for j in range(i+1, n_components):
                similarity = similarity_matrix[i][j]
                if similarity >= threshold:
                    row1 = df.iloc[i]
                    row2 = df.iloc[j]
                    semantic_matches.append({
                        'component1_id': row1['product_id'],
                        'component2_id': row2['product_id'],
                        'component1_text': row1['component_text'],
                        'component2_text': row2['component_text'],
                        'component1_page': row1['page_number'],
                        'component2_page': row2['page_number'],
                        'semantic_similarity': similarity,
                        'match_type': 'semantic_similarity'
                    })
        
        print(f"📊 Semantic Matches Found: {len(semantic_matches)}")
        
        self.similarity_results['semantic_matches'] = semantic_matches
        self.similarity_results['similarity_matrix'] = similarity_matrix
        
        return semantic_matches, similarity_matrix
    
    def spatial_clustering_analysis(self, subset_df=None, distance_threshold=200):
        """Spatial clustering analysis"""
        df = subset_df if subset_df is not None else self.df
        
        print(f"\n📍 SPATIAL CLUSTERING (threshold: {distance_threshold} pixels)")
        print("=" * 50)
        
        # Group by page for spatial analysis
        spatial_clusters = {}
        
        for page_num in df['page_number'].unique():
            page_df = df[df['page_number'] == page_num].copy()
            
            if len(page_df) < 2:
                continue
                
            # Extract coordinates
            coordinates = page_df[['center_x', 'center_y']].values
            
            # Perform DBSCAN clustering
            clustering = DBSCAN(eps=distance_threshold, min_samples=2).fit(coordinates)
            page_df['spatial_cluster'] = clustering.labels_
            
            # Analyze clusters
            clusters = {}
            for cluster_id in set(clustering.labels_):
                if cluster_id != -1:  # Ignore noise points
                    cluster_components = page_df[page_df['spatial_cluster'] == cluster_id]
                    clusters[cluster_id] = cluster_components.to_dict('records')
            
            spatial_clusters[page_num] = clusters
            print(f"  Page {page_num}: {len(clusters)} spatial clusters")
        
        self.similarity_results['spatial_clusters'] = spatial_clusters
        return spatial_clusters
    
    def color_based_grouping(self, subset_df=None):
        """Color-based grouping analysis (Blue→Purple→Red chains)"""
        df = subset_df if subset_df is not None else self.df
        
        print("\n🎨 COLOR-BASED GROUPING ANALYSIS")
        print("=" * 40)
        
        color_groups = df.groupby('color_detected')
        
        for color, group in color_groups:
            print(f"  {color}: {len(group)} components")
            
        # Analyze color transitions and dependencies
        color_chains = []
        
        # Group by page to analyze spatial color relationships
        for page_num in df['page_number'].unique():
            page_df = df[df['page_number'] == page_num].copy()
            
            # Sort by Y position to find potential chains
            page_df = page_df.sort_values('center_y')
            
            # Look for Blue→Purple→Red patterns
            for i in range(len(page_df)-2):
                comp1 = page_df.iloc[i]
                comp2 = page_df.iloc[i+1]
                comp3 = page_df.iloc[i+2]
                
                colors = [comp1['color_detected'], comp2['color_detected'], comp3['color_detected']]
                if colors == ['Blue', 'Purple', 'Red']:
                    color_chains.append({
                        'page': page_num,
                        'chain': [comp1['product_id'], comp2['product_id'], comp3['product_id']],
                        'pattern': 'Blue→Purple→Red'
                    })
        
        print(f"🔗 Color Chains Found: {len(color_chains)}")
        
        self.similarity_results['color_groups'] = {color: group.to_dict('records') for color, group in color_groups}
        self.similarity_results['color_chains'] = color_chains
        
        return color_groups, color_chains
    
    def run_limited_analysis(self, pages=[2, 3]):
        """Run analysis on limited dataset (Pages 2-3)"""
        print(f"🧪 RUNNING LIMITED ANALYSIS ON PAGES {pages}")
        print("=" * 60)
        
        # Filter to specified pages
        subset_df = self.df[self.df['page_number'].isin(pages)].copy()
        print(f"📊 Analyzing {len(subset_df)} components from pages {pages}")
        
        # Run all analyses
        exact_desc, exact_id = self.exact_match_analysis(subset_df)
        categories, id_families = self.terminology_analysis(subset_df)
        self.parent_child_bom_analysis()
        semantic_matches, similarity_matrix = self.sbert_semantic_similarity(subset_df, threshold=0.7)
        spatial_clusters = self.spatial_clustering_analysis(subset_df, distance_threshold=200)
        color_groups, color_chains = self.color_based_grouping(subset_df)
        
        # Generate summary report
        self.generate_summary_report(subset_df, pages)
        
        return self.similarity_results
    
    def generate_summary_report(self, subset_df, pages):
        """Generate comprehensive summary report"""
        print(f"\n📋 COMPREHENSIVE ANALYSIS SUMMARY - PAGES {pages}")
        print("=" * 60)
        
        print(f"📊 Dataset: {len(subset_df)} components")
        print(f"📄 Pages: {pages}")
        print(f"🎨 Colors: {subset_df['color_detected'].nunique()} types")
        print(f"🏷️ Component Types: {subset_df['component_type'].nunique()} types")
        
        print("\n🎯 EXACT MATCHES:")
        print(f"  Description Matches: {len(self.similarity_results.get('exact_desc_matches', []))}")
        print(f"  Product ID Matches: {len(self.similarity_results.get('exact_id_matches', []))}")
        
        print("\n🔍 PATTERN ANALYSIS:")
        print(f"  Terminology Categories: {len(self.similarity_results.get('terminology_categories', {}))}")
        print(f"  ID Families: {len(self.similarity_results.get('id_families', {}))}")
        
        print("\n🧠 SEMANTIC ANALYSIS:")
        print(f"  Semantic Matches (≥0.7): {len(self.similarity_results.get('semantic_matches', []))}")
        
        print("\n📍 SPATIAL ANALYSIS:")
        spatial_clusters = self.similarity_results.get('spatial_clusters', {})
        total_clusters = sum(len(clusters) for clusters in spatial_clusters.values())
        print(f"  Spatial Clusters: {total_clusters}")
        
        print("\n🎨 COLOR ANALYSIS:")
        print(f"  Color Chains: {len(self.similarity_results.get('color_chains', []))}")

def main():
    """Main execution function"""
    analyzer = ComprehensiveSimilarityAnalyzer()
    
    # Run limited analysis on Pages 2-3 as requested
    results = analyzer.run_limited_analysis(pages=[2, 3])
    
    # Save results to multiple files for better analysis
    save_detailed_results(results)

    return results

def save_detailed_results(results):
    """Save detailed results to multiple files"""

    # 1. Save exact matches to CSV
    if 'exact_desc_matches' in results:
        exact_desc_df = pd.DataFrame(results['exact_desc_matches'])
        exact_desc_df.to_csv('output/exact_description_matches.csv', index=False)
        print(f"💾 Exact description matches saved: {len(exact_desc_df)} matches")

    # 2. Save semantic matches to CSV
    if 'semantic_matches' in results:
        semantic_df = pd.DataFrame(results['semantic_matches'])
        semantic_df.to_csv('output/semantic_similarity_matches.csv', index=False)
        print(f"💾 Semantic matches saved: {len(semantic_df)} matches")

    # 3. Save terminology analysis
    if 'terminology_categories' in results:
        with open('output/terminology_analysis.txt', 'w', encoding='utf-8') as f:
            f.write("TERMINOLOGY ANALYSIS REPORT\n")
            f.write("=" * 50 + "\n\n")

            f.write("COMPONENT CATEGORIES:\n")
            for category, components in results['terminology_categories'].items():
                f.write(f"\n{category} ({len(components)} components):\n")
                for comp in components[:5]:  # Show first 5
                    f.write(f"  - {comp['product_id']}: {comp['component_text']}\n")
                if len(components) > 5:
                    f.write(f"  ... and {len(components)-5} more\n")

            f.write(f"\n\nID FAMILIES:\n")
            for family, components in results['id_families'].items():
                f.write(f"\n{family} Family ({len(components)} components):\n")
                for comp in components[:5]:  # Show first 5
                    f.write(f"  - {comp['product_id']}: {comp['component_text']}\n")
                if len(components) > 5:
                    f.write(f"  ... and {len(components)-5} more\n")

    # 4. Save spatial clusters analysis
    if 'spatial_clusters' in results:
        with open('output/spatial_clusters_analysis.txt', 'w', encoding='utf-8') as f:
            f.write("SPATIAL CLUSTERING ANALYSIS\n")
            f.write("=" * 40 + "\n\n")

            for page, clusters in results['spatial_clusters'].items():
                f.write(f"PAGE {page} - {len(clusters)} clusters:\n")
                for cluster_id, components in clusters.items():
                    f.write(f"\n  Cluster {cluster_id} ({len(components)} components):\n")
                    for comp in components:
                        f.write(f"    - {comp['product_id']}: {comp['component_text']} at ({comp['center_x']}, {comp['center_y']})\n")
                f.write("\n" + "-" * 50 + "\n")

    # 5. Save color analysis
    if 'color_chains' in results:
        with open('output/color_chain_analysis.txt', 'w', encoding='utf-8') as f:
            f.write("COLOR CHAIN ANALYSIS\n")
            f.write("=" * 30 + "\n\n")

            for chain in results['color_chains']:
                f.write(f"Page {chain['page']}: {chain['pattern']}\n")
                f.write(f"  Chain: {' -> '.join(chain['chain'])}\n\n")

    print("💾 All detailed results saved to output/ folder")


if __name__ == "__main__":
    main()
