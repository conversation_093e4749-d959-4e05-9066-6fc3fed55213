@echo off
echo 🚀 BOM Analysis Quick Start
echo ==========================
echo.

REM Check if environment exists
if not exist "bom_analysis_env" (
    echo ❌ Environment not set up yet!
    echo Please run setup_windows.bat first
    pause
    exit /b 1
)

echo ✅ Activating BOM Analysis environment...
call bom_analysis_env\Scripts\activate.bat

echo.
echo 🎯 What would you like to do?
echo.
echo 1. Run BOM similarity analysis
echo 2. Start Jupyter Lab
echo 3. Start Jupyter Notebook
echo 4. Open Python interactive shell
echo 5. Check installed packages
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🔄 Running BOM similarity analysis...
    python bom_similarity_analysis.py
) else if "%choice%"=="2" (
    echo.
    echo 🔄 Starting Jupyter Lab...
    jupyter lab
) else if "%choice%"=="3" (
    echo.
    echo 🔄 Starting Jupyter Notebook...
    jupyter notebook
) else if "%choice%"=="4" (
    echo.
    echo 🔄 Starting Python shell...
    python
) else if "%choice%"=="5" (
    echo.
    echo 📦 Installed packages:
    pip list
) else (
    echo Invalid choice!
)

echo.
pause
