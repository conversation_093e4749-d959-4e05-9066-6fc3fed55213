#!/usr/bin/env python3
"""
Test PDF + ColPali Extractor
Test the extraction system before using on real PDF
"""

import os
import sys

def test_imports():
    """Test all required imports"""
    print("🧪 Testing imports...")
    
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__}")
        
        import torchvision
        print(f"✅ TorchVision {torchvision.__version__}")
        
        import cv2
        print(f"✅ OpenCV {cv2.__version__}")
        
        import fitz  # PyMuPDF
        print(f"✅ PyMuPDF {fitz.version[0]}")
        
        from transformers import AutoProcessor, AutoModel
        print("✅ Transformers available")
        
        from PIL import Image
        print("✅ PIL available")
        
        import pandas as pd
        print("✅ Pandas available")
        
        import numpy as np
        print("✅ NumPy available")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_colpali_model():
    """Test ColPali model loading"""
    print("\n🤖 Testing ColPali model...")
    
    try:
        from transformers import AutoProcessor, AutoModel
        
        model_name = "vidore/colpali"
        print(f"📥 Loading {model_name}...")
        
        processor = AutoProcessor.from_pretrained(model_name, trust_remote_code=True)
        print("✅ Processor loaded")
        
        model = AutoModel.from_pretrained(model_name, trust_remote_code=True)
        print("✅ Model loaded")
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model = model.to(device)
        print(f"✅ Model moved to {device}")
        
        return True, processor, model, device
        
    except Exception as e:
        print(f"⚠️ ColPali failed: {e}")
        print("🔄 Trying CLIP fallback...")
        
        try:
            from transformers import CLIPProcessor, CLIPModel
            
            model_name = "openai/clip-vit-base-patch32"
            processor = CLIPProcessor.from_pretrained(model_name)
            model = CLIPModel.from_pretrained(model_name)
            
            device = "cuda" if torch.cuda.is_available() else "cpu"
            model = model.to(device)
            
            print(f"✅ CLIP fallback loaded on {device}")
            return True, processor, model, device
            
        except Exception as e2:
            print(f"❌ All models failed: {e2}")
            return False, None, None, "cpu"

def test_pdf_processing():
    """Test PDF processing capabilities"""
    print("\n📄 Testing PDF processing...")
    
    try:
        import fitz
        
        # Create a simple test PDF
        doc = fitz.open()  # New empty document
        page = doc.new_page()  # New page
        
        # Add some colored rectangles
        page.draw_rect(fitz.Rect(100, 100, 200, 150), color=(0, 0, 1), fill=(0, 0, 1))  # Blue
        page.draw_rect(fitz.Rect(250, 100, 350, 150), color=(1, 0, 1), fill=(1, 0, 1))  # Purple
        page.draw_rect(fitz.Rect(400, 100, 500, 150), color=(1, 0, 0), fill=(1, 0, 0))  # Red
        
        # Add text
        page.insert_text((110, 130), "Main Unit", fontsize=12, color=(1, 1, 1))
        page.insert_text((260, 130), "Display", fontsize=12, color=(1, 1, 1))
        page.insert_text((410, 130), "Memory", fontsize=12, color=(1, 1, 1))
        
        # Save test PDF
        test_pdf = "test_architecture.pdf"
        doc.save(test_pdf)
        doc.close()
        
        print(f"✅ Test PDF created: {test_pdf}")
        
        # Test conversion to image
        doc = fitz.open(test_pdf)
        page = doc.load_page(0)
        
        mat = fitz.Matrix(2, 2)  # 2x zoom
        pix = page.get_pixmap(matrix=mat)
        
        test_image = "test_page.png"
        pix.save(test_image)
        doc.close()
        
        print(f"✅ Test image created: {test_image}")
        
        return True, test_pdf, test_image
        
    except Exception as e:
        print(f"❌ PDF processing failed: {e}")
        return False, None, None

def test_color_detection():
    """Test color detection on test image"""
    print("\n🎨 Testing color detection...")
    
    try:
        import cv2
        import numpy as np
        from PIL import Image
        
        # Load test image
        test_image = "test_page.png"
        if not os.path.exists(test_image):
            print("⚠️ Test image not found")
            return False
        
        # Load with OpenCV
        cv_image = cv2.imread(test_image)
        hsv = cv2.cvtColor(cv_image, cv2.COLOR_BGR2HSV)
        
        # Define color ranges
        color_ranges = {
            'Blue': {'lower': np.array([100, 50, 50]), 'upper': np.array([130, 255, 255])},
            'Purple': {'lower': np.array([130, 50, 50]), 'upper': np.array([160, 255, 255])},
            'Red': {'lower': np.array([0, 50, 50]), 'upper': np.array([10, 255, 255])}
        }
        
        detected_colors = []
        
        for color_name, color_info in color_ranges.items():
            mask = cv2.inRange(hsv, color_info['lower'], color_info['upper'])
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 1000:  # Minimum area
                    x, y, w, h = cv2.boundingRect(contour)
                    detected_colors.append({
                        'color': color_name,
                        'area': area,
                        'bbox': {'x': x, 'y': y, 'width': w, 'height': h}
                    })
        
        print(f"✅ Detected {len(detected_colors)} colored regions:")
        for detection in detected_colors:
            print(f"   {detection['color']}: {detection['area']} pixels")
        
        return True
        
    except Exception as e:
        print(f"❌ Color detection failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 PDF + COLPALI EXTRACTOR TEST")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("❌ Import test failed - check dependencies")
        return
    
    # Test ColPali model
    model_success, processor, model, device = test_colpali_model()
    
    # Test PDF processing
    pdf_success, test_pdf, test_image = test_pdf_processing()
    
    # Test color detection
    if pdf_success:
        color_success = test_color_detection()
    else:
        color_success = False
    
    # Summary
    print(f"\n📊 Test Results:")
    print("=" * 30)
    print(f"✅ Imports: {'PASS' if True else 'FAIL'}")
    print(f"{'✅' if model_success else '⚠️'} ColPali: {'PASS' if model_success else 'FALLBACK'}")
    print(f"{'✅' if pdf_success else '❌'} PDF Processing: {'PASS' if pdf_success else 'FAIL'}")
    print(f"{'✅' if color_success else '❌'} Color Detection: {'PASS' if color_success else 'FAIL'}")
    
    if model_success and pdf_success and color_success:
        print(f"\n🎉 All tests passed! Ready for real PDF extraction.")
        print(f"📋 Next steps:")
        print(f"1. Convert your PowerPoint to PDF")
        print(f"2. Place it as: input/Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pdf")
        print(f"3. Run: python pdf_colpali_extractor.py")
    else:
        print(f"\n⚠️ Some tests failed. Check error messages above.")
    
    # Cleanup
    for file in ["test_architecture.pdf", "test_page.png"]:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"🧹 Cleaned up: {file}")
            except:
                pass

if __name__ == "__main__":
    main()
