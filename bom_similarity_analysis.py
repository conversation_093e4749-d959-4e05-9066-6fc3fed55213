import sys
import os
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import AgglomerativeClustering
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
from scipy.spatial.distance import squareform
import matplotlib.pyplot as plt
import seaborn as sns
import re
import traceback
from datetime import datetime
import warnings
import pickle
import hashlib
warnings.filterwarnings('ignore')

def clean_description(text):
    if pd.isna(text):
        return ""
    return str(text).lower().strip()

def get_file_hash(file_path):
    """Generate a hash for a file to detect changes"""
    try:
        with open(file_path, 'rb') as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        return file_hash
    except Exception:
        return None

def save_processed_data(data, cache_file):
    """Save processed data to cache"""
    try:
        with open(cache_file, 'wb') as f:
            pickle.dump(data, f)
        return True
    except Exception as e:
        print(f"    ⚠️  Could not save cache: {e}")
        return False

def load_processed_data(cache_file):
    """Load processed data from cache"""
    try:
        with open(cache_file, 'rb') as f:
            data = pickle.load(f)
        return data
    except Exception:
        return None

def check_cache_validity(input_folder, cache_folder):
    """Check if cached data is still valid by comparing file hashes"""
    cache_info_file = os.path.join(cache_folder, 'cache_info.pkl')

    if not os.path.exists(cache_info_file):
        return False, {}

    try:
        with open(cache_info_file, 'rb') as f:
            cached_info = pickle.load(f)
    except Exception:
        return False, {}

    # Check if all files still exist and have same hash
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    current_info = {}

    for file in excel_files:
        file_path = os.path.join(input_folder, file)
        current_info[file] = get_file_hash(file_path)

    # Compare with cached info
    if current_info == cached_info:
        return True, current_info
    else:
        return False, current_info

def encode_fe_line(fe_line_series):
    
    # Convert all to string first
    fe_strings = fe_line_series.astype(str).str.strip()

    # Create unique mapping
    unique_fe_lines = fe_strings.unique()

    # Create encoding dictionary
    fe_line_to_code = {}
    code_to_fe_line = {}
    parent_child_map = {}

    for i, fe_line in enumerate(unique_fe_lines):
        if fe_line == 'nan' or fe_line == '':
            continue

        # Assign numeric code
        code = i + 1
        fe_line_to_code[fe_line] = code
        code_to_fe_line[code] = fe_line

        # Analyze hierarchical structure
        # F-E-Line often represents parent-child relationships
        # Look for patterns like "1.1", "1.1.1", etc.
        if '.' in fe_line:
            parts = fe_line.split('.')
            if len(parts) > 1:
                parent_fe = '.'.join(parts[:-1])
                if parent_fe in fe_line_to_code:
                    parent_child_map[code] = fe_line_to_code[parent_fe]

    # Apply encoding
    encoded_series = fe_strings.map(fe_line_to_code).fillna(0).astype('int32')

    return encoded_series, fe_line_to_code, code_to_fe_line, parent_child_map

def load_bom_data(file_path):
    try:
        # Read the second sheet (index 1) which contains BOM data
        xls = pd.ExcelFile(file_path)
        if len(xls.sheet_names) < 2:
            print(f"Warning: {file_path} doesn't have a second sheet")
            return None

        df = pd.read_excel(file_path, sheet_name=1)

        # Keep only the first 4 columns
        if len(df.columns) >= 4:
            df = df.iloc[:, :4]
            # Rename columns for consistency
            df.columns = ['Level', 'Item ID', 'F-E-Line', 'Item Description']

            print(f"    📊 Original rows: {len(df)}")

            # STRICT REQUIREMENT: Remove rows with ANY empty values in ALL 4 columns
            # This ensures we only consider complete, valid BOM entries
            df_before = len(df)
            df = df.dropna(subset=['Level', 'Item ID', 'F-E-Line', 'Item Description'])
            df_after = len(df)

            # Additional cleaning: remove rows where any column is just whitespace
            df = df[df['Level'].astype(str).str.strip() != '']
            df = df[df['Item ID'].astype(str).str.strip() != '']
            df = df[df['F-E-Line'].astype(str).str.strip() != '']
            df = df[df['Item Description'].astype(str).str.strip() != '']

            df_after_cleaning = len(df)

            print(f"    🧹 After removing incomplete rows: {df_after} (removed {df_before - df_after})")
            print(f"    🧹 After removing empty/whitespace: {df_after_cleaning} (removed {df_after - df_after_cleaning})")

            # CORRECT REQUIREMENT: Filter to only Level 0, 1, 2, and 3
            df_before_level_filter = len(df)
            # Convert Level to string and extract numeric part for comparison
            df['Level_Numeric'] = df['Level'].astype(str).str.extract('(\d+)').astype(float)
            df = df[df['Level_Numeric'].isin([0.0, 1.0, 2.0, 3.0])]
            df_after_level_filter = len(df)

            print(f"    🎯 After filtering to Level 0, 1, 2 & 3: {df_after_level_filter} (removed {df_before_level_filter - df_after_level_filter})")

            if df_after_level_filter == 0:
                print(f"    ❌ No valid Level 0, 1, 2 or 3 rows found")
                return None

            # Clean and normalize descriptions for better matching
            df['Item Description'] = df['Item Description'].apply(clean_description)

            # Remove rows where description becomes empty after cleaning
            df = df[df['Item Description'].str.len() > 0]

            df_final = len(df)
            if df_final == 0:
                print(f"    ❌ No valid descriptions after cleaning")
                return None

            # 🚀 SMART F-E-LINE ENCODING: Handle hierarchical relationships
            print(f"    🔧 Encoding F-E-Line hierarchical relationships...")
            df['F-E-Line_Encoded'], fe_line_to_code, code_to_fe_line, parent_child_map = encode_fe_line(df['F-E-Line'])

            # Store encoding information for later use
            df.attrs['fe_line_encoding'] = {
                'fe_line_to_code': fe_line_to_code,
                'code_to_fe_line': code_to_fe_line,
                'parent_child_map': parent_child_map
            }

            print(f"    ✅ F-E-Line encoded: {len(fe_line_to_code)} unique values, {len(parent_child_map)} parent-child relationships")

            # 🚀 CRITICAL OPTIMIZATION: Remove exact duplicates to prevent memory overflow
            # This dramatically reduces memory usage and computation time
            df_before_dedup = len(df)

            # Create a unique key combining Item ID and Description for exact duplicate detection
            df['unique_key'] = df['Item ID'].astype(str) + "|||" + df['Item Description'].astype(str)

            # Group by unique key and aggregate other information
            df_grouped = df.groupby('unique_key').agg({
                'Level': lambda x: ', '.join(map(str, sorted(set(x)))),  # Keep all unique levels
                'Item ID': 'first',  # Take first (they're all the same)
                'F-E-Line': lambda x: ', '.join(map(str, sorted(set(x)))),  # Keep all unique F-E-Lines
                'Item Description': 'first',  # Take first (they're all the same)
                'Level_Numeric': lambda x: list(set(x))[0]  # Take first unique value
            }).reset_index()

            # Add occurrence count for tracking
            df_grouped['Occurrence_Count'] = df.groupby('unique_key').size().values

            # Keep the simplified structure for similarity analysis
            df_final_dedup = df_grouped[['Item ID', 'Item Description', 'Level', 'F-E-Line', 'Level_Numeric', 'Occurrence_Count']].copy()

            df_after_dedup = len(df_final_dedup)

            print(f"    🔄 After removing exact duplicates: {df_after_dedup} unique items (removed {df_before_dedup - df_after_dedup} duplicates)")
            print(f"    💾 Memory optimization: {((df_before_dedup - df_after_dedup) / df_before_dedup * 100):.1f}% reduction in data size")
            print(f"    ✅ Final unique Level 0,1,2,3 items for analysis: {len(df_final_dedup)}")

            return df_final_dedup
        else:
            print(f"Warning: {file_path} doesn't have at least 4 columns")
            return None
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def calculate_similarity(df1, df2, file1_name="File1", file2_name="File2"):
    """
    Calculate similarity between two BOM datasets focusing on the 3 columns outside Level.
    Analyzes Item ID, F-E-Line, and Item Description with emphasis on Item ID and Description.
    Only considers Level 1 and 2 items with complete data in all 4 columns.
    """
    # Skip if either dataframe is empty
    if len(df1) == 0 or len(df2) == 0:
        print(f"    ⚠️  Skipping comparison: one or both files have no valid Level 1&2 data")
        return [], 0, 0

    # Prepare data for multi-column similarity analysis
    # Focus on the 3 columns: Item ID, F-E-Line, Item Description
    item_ids1 = df1['Item ID'].astype(str).tolist()
    item_ids2 = df2['Item ID'].astype(str).tolist()

    fe_lines1 = df1['F-E-Line'].astype(str).tolist()
    fe_lines2 = df2['F-E-Line'].astype(str).tolist()

    descriptions1 = df1['Item Description'].tolist()
    descriptions2 = df2['Item Description'].tolist()

    # Enhanced TF-IDF for Item Descriptions
    desc_vectorizer = TfidfVectorizer(
        min_df=1,           # Include all terms
        max_df=0.95,        # Exclude very common terms
        ngram_range=(1, 2), # Include both single words and bigrams
        stop_words='english', # Remove common English stop words
        lowercase=True,
        strip_accents='unicode'
    )

    # TF-IDF for Item IDs (treat as text for partial matching)
    id_vectorizer = TfidfVectorizer(
        min_df=1,
        analyzer='char_wb',  # Character-based analysis for IDs
        ngram_range=(2, 4),  # Character n-grams for ID similarity
        lowercase=True
    )

    try:
        # Calculate description similarity
        desc_tfidf_matrix = desc_vectorizer.fit_transform(descriptions1 + descriptions2)
        desc_tfidf1 = desc_tfidf_matrix[:len(descriptions1)]
        desc_tfidf2 = desc_tfidf_matrix[len(descriptions1):]
        desc_similarity_matrix = cosine_similarity(desc_tfidf1, desc_tfidf2)

        # Calculate Item ID similarity
        id_tfidf_matrix = id_vectorizer.fit_transform(item_ids1 + item_ids2)
        id_tfidf1 = id_tfidf_matrix[:len(item_ids1)]
        id_tfidf2 = id_tfidf_matrix[len(item_ids1):]
        id_similarity_matrix = cosine_similarity(id_tfidf1, id_tfidf2)

        # Find matches with combined similarity scoring
        matches = []
        exact_matches = 0
        high_matches = 0
        medium_matches = 0

        for j in range (len(df2)):
            for i in range(len(df1)):
                # Get individual similarity scores
                desc_sim = desc_similarity_matrix[i, j]
                id_sim = id_similarity_matrix[i, j]

                # Check for exact Item ID match (bonus scoring)
                exact_id_match = item_ids1[i].lower().strip() == item_ids2[j].lower().strip()

                # Check for exact F-E-Line match
                exact_fe_match = fe_lines1[i].lower().strip() == fe_lines2[j].lower().strip()

                # Combined similarity score with weights:
                # - Item Description: 50% weight
                # - Item ID: 35% weight
                # - F-E-Line exact match: 15% bonus
                combined_score = (desc_sim * 0.5) + (id_sim * 0.35)

                # Add bonuses for exact matches
                if exact_id_match:
                    combined_score += 0.3  # Strong bonus for exact ID match
                if exact_fe_match:
                    combined_score += 0.15  # Bonus for exact F-E-Line match    


        for i in range(len(df1)):
            for j in range(len(df2)):
                # Get individual similarity scores
                desc_sim = desc_similarity_matrix[i, j]
                id_sim = id_similarity_matrix[i, j]

                # Check for exact Item ID match (bonus scoring)
                exact_id_match = item_ids1[i].lower().strip() == item_ids2[j].lower().strip()

                # Check for exact F-E-Line match
                exact_fe_match = fe_lines1[i].lower().strip() == fe_lines2[j].lower().strip()

                # Combined similarity score with weights:
                # - Item Description: 50% weight
                # - Item ID: 35% weight
                # - F-E-Line exact match: 15% bonus
                combined_score = (desc_sim * 0.5) + (id_sim * 0.35)

                # Add bonuses for exact matches
                if exact_id_match:
                    combined_score += 0.3  # Strong bonus for exact ID match
                if exact_fe_match:
                    combined_score += 0.15  # Bonus for exact F-E-Line match

                # Cap the score at 1.0
                combined_score = min(combined_score, 1.0)

                # Only consider meaningful similarities (threshold 0.6 for combined score)
                if combined_score > 0.6:
                    # Additional validation: check if descriptions are not too short
                    desc1 = descriptions1[i]
                    desc2 = descriptions2[j]

                    # Skip if descriptions are too short (less than 3 characters)
                    if len(desc1.strip()) < 3 or len(desc2.strip()) < 3:
                        continue

                    # Skip if descriptions are identical but too generic
                    if desc1 == desc2 and len(desc1.split()) < 2:
                        continue

                    match_info = {
                        'item1': df1.iloc[i]['Item ID'],
                        'desc1': df1.iloc[i]['Item Description'],
                        'level1': df1.iloc[i]['Level'],
                        'fe_line1': df1.iloc[i]['F-E-Line'],
                        'item2': df2.iloc[j]['Item ID'],
                        'desc2': df2.iloc[j]['Item Description'],
                        'level2': df2.iloc[j]['Level'],
                        'fe_line2': df2.iloc[j]['F-E-Line'],
                        'similarity': combined_score,
                        'desc_similarity': desc_sim,
                        'id_similarity': id_sim,
                        'exact_id_match': exact_id_match,
                        'exact_fe_match': exact_fe_match,
                        'file1': file1_name,
                        'file2': file2_name
                    }

                    matches.append(match_info)

                    # Categorize matches by combined similarity level
                    if combined_score > 0.9:  # Very high similarity
                        exact_matches += 1
                    elif combined_score > 0.75:  # High similarity
                        high_matches += 1
                    else:  # Medium similarity (0.6-0.75)
                        medium_matches += 1

        # Calculate similarity percentage based on Level 1&2 items
        total_items = max(len(df1), len(df2))
        similarity_percentage = (len(matches) / total_items) * 100 if total_items > 0 else 0

        print(f"    📊 Level 1&2 Similarity breakdown:")
        print(f"      🎯 Exact matches (>90%): {exact_matches}")
        print(f"      🔍 High matches (75-90%): {high_matches}")
        print(f"      📋 Medium matches (60-75%): {medium_matches}")

        return matches, exact_matches, similarity_percentage

    except Exception as e:
        print(f"    ❌ Error calculating similarity: {e}")
        traceback.print_exc()
        return [], 0, 0

def create_global_similarity_matrix(all_bom_data):
    """
    Create a global similarity matrix for all items across all BOM files.
    Returns similarity matrix, item metadata, and distance matrix for clustering.
    """
    print("\n🌍 Creating global similarity matrix...")

    # Combine all items from all files into one dataset
    all_items = []
    item_metadata = []

    for file_name, df in all_bom_data.items():
        for idx, row in df.iterrows():
            item_info = {
                'file': file_name,
                'item_id': str(row['Item ID']),
                'level': row['Level'],
                'fe_line': str(row['F-E-Line']),
                'description': str(row['Item Description']),
                'level_numeric': row['Level_Numeric'],
                'original_index': idx
            }
            all_items.append(item_info)
            item_metadata.append(item_info)

    print(f"    📊 Total items across all files: {len(all_items)}")

    if len(all_items) < 2:
        print("    ❌ Need at least 2 items for global analysis")
        return None, None, None
        
    # Check if dataset is too large
    n_items = len(all_items)
    estimated_memory_gb = (n_items ** 2 * 8) / (1024**3)  # 8 bytes per float64
    
    if estimated_memory_gb > 8:  # If estimated memory usage > 8GB
        print(f"    ⚠️ Dataset too large! Estimated memory: {estimated_memory_gb:.1f} GB")
        print(f"    🔄 Applying chunking to reduce memory usage...")
        
        # Use sklearn's working_memory parameter to limit memory usage
        from sklearn import set_config
        set_config(working_memory=2048)  # Limit to 2GB chunks
        
        # Sample the dataset if it's extremely large
        if n_items > 100000:
            sample_size = min(100000, int(n_items * 0.2))  # 20% or max 100k items
            print(f"    🔄 Sampling {sample_size} items from total {n_items} for analysis")
            
            import random
            random.seed(42)  # For reproducibility
            sampled_indices = random.sample(range(n_items), sample_size)
            
            # Create sampled versions
            sampled_items = [all_items[i] for i in sampled_indices]
            sampled_metadata = [item_metadata[i] for i in sampled_indices]
            
            all_items = sampled_items
            item_metadata = sampled_metadata
            n_items = len(all_items)
            print(f"    ✅ Reduced to {n_items} items")

    # Extract features for similarity calculation
    item_ids = [item['item_id'] for item in all_items]
    descriptions = [item['description'] for item in all_items]
    fe_lines = [item['fe_line'] for item in all_items]

    # Calculate similarity matrices
    print("    🔍 Calculating description similarities...")
    desc_vectorizer = TfidfVectorizer(
        min_df=1, max_df=0.95, ngram_range=(1, 2),
        stop_words='english', lowercase=True, strip_accents='unicode'
    )
    desc_tfidf_matrix = desc_vectorizer.fit_transform(descriptions)
    desc_similarity_matrix = cosine_similarity(desc_tfidf_matrix)

    print("    🔍 Calculating ID similarities...")
    id_vectorizer = TfidfVectorizer(
        min_df=1, analyzer='char_wb', ngram_range=(2, 4), lowercase=True
    )
    id_tfidf_matrix = id_vectorizer.fit_transform(item_ids)
    id_similarity_matrix = cosine_similarity(id_tfidf_matrix)

    # Create combined similarity matrix
    print("    🔍 Creating combined similarity matrix...")
    combined_similarity_matrix = np.zeros((n_items, n_items))

    for i in range(n_items):
        for j in range(n_items):
            if i == j:
                combined_similarity_matrix[i, j] = 1.0
                continue

            desc_sim = desc_similarity_matrix[i, j]
            id_sim = id_similarity_matrix[i, j]

            # Check exact matches
            exact_id_match = item_ids[i].lower().strip() == item_ids[j].lower().strip()
            exact_fe_match = fe_lines[i].lower().strip() == fe_lines[j].lower().strip()

            # Combined score with same weights as before
            combined_score = (desc_sim * 0.5) + (id_sim * 0.35)
            if exact_id_match:
                combined_score += 0.3
            if exact_fe_match:
                combined_score += 0.15
            combined_score = min(combined_score, 1.0)

            combined_similarity_matrix[i, j] = combined_score

    # Convert similarity to distance for clustering (distance = 1 - similarity)
    distance_matrix = 1 - combined_similarity_matrix

    print(f"    ✅ Global similarity matrix created: {n_items}x{n_items}")
    return combined_similarity_matrix, item_metadata, distance_matrix

def create_similarity_clusters(similarity_matrix, item_metadata, distance_matrix, threshold=0.7):
    """
    Create hierarchical clusters of similar items and generate dendrogram.
    """
    print(f"\n🌳 Creating similarity clusters (threshold: {threshold})...")

    n_items = len(item_metadata)
    if n_items < 2:
        return [], None

    # Perform hierarchical clustering
    print("    🔗 Performing hierarchical clustering...")

    # Convert distance matrix to condensed form for linkage
    condensed_distances = squareform(distance_matrix, checks=False)

    # Create linkage matrix using Ward method
    linkage_matrix = linkage(condensed_distances, method='ward')

    # Create clusters based on threshold
    cluster_labels = fcluster(linkage_matrix, 1-threshold, criterion='distance')

    # Group items by cluster
    clusters = {}
    for i, cluster_id in enumerate(cluster_labels):
        if cluster_id not in clusters:
            clusters[cluster_id] = []
        clusters[cluster_id].append({
            'index': i,
            'metadata': item_metadata[i],
            'cluster_id': cluster_id
        })

    # Filter clusters to only include those with multiple items
    significant_clusters = {k: v for k, v in clusters.items() if len(v) > 1}

    print(f"    📊 Found {len(significant_clusters)} clusters with multiple items")
    print(f"    📊 Total clustered items: {sum(len(cluster) for cluster in significant_clusters.values())}")

    # Create dendrogram
    print("    🌳 Creating dendrogram...")
    try:
        plt.figure(figsize=(15, 10))
        dendrogram_plot = dendrogram(
            linkage_matrix,
            labels=[f"{item['item_id'][:10]}..." for item in item_metadata],
            leaf_rotation=90,
            leaf_font_size=8
        )
        plt.title('BOM Items Similarity Dendrogram\n(Based on Item ID, Description, and F-E-Line)', fontsize=14)
        plt.xlabel('Items', fontsize=12)
        plt.ylabel('Distance (1 - Similarity)', fontsize=12)
        plt.tight_layout()

        # Save dendrogram
        output_folder = 'output'
        os.makedirs(output_folder, exist_ok=True)
        plt.savefig(os.path.join(output_folder, 'bom_similarity_dendrogram.png'), dpi=300, bbox_inches='tight')
        plt.close()
        print(f"    ✅ Dendrogram saved: bom_similarity_dendrogram.png")

    except Exception as e:
        print(f"    ⚠️  Could not create dendrogram: {e}")
        dendrogram_plot = None

    return significant_clusters, linkage_matrix

def create_global_similarity_dataframe(similarity_matrix, item_metadata, clusters, threshold=0.6):
    """
    Create a comprehensive DataFrame with all similar items and their resemblance percentages.
    """
    print(f"\n📊 Creating global similarity DataFrame (threshold: {threshold})...")

    similarity_records = []
    n_items = len(item_metadata)

    # Create records for all item pairs above threshold
    for i in range(n_items):
        for j in range(i + 1, n_items):  # Only upper triangle to avoid duplicates
            similarity_score = similarity_matrix[i, j]

            if similarity_score >= threshold:
                item1 = item_metadata[i]
                item2 = item_metadata[j]

                # Find cluster information
                cluster_id = None
                for cid, cluster_items in clusters.items():
                    cluster_indices = [item['index'] for item in cluster_items]
                    if i in cluster_indices and j in cluster_indices:
                        cluster_id = cid
                        break

                record = {
                    'Item_ID_1': item1['item_id'],
                    'File_1': item1['file'],
                    'Level_1': item1['level'],
                    'FE_Line_1': item1['fe_line'],
                    'Description_1': item1['description'],
                    'Item_ID_2': item2['item_id'],
                    'File_2': item2['file'],
                    'Level_2': item2['level'],
                    'FE_Line_2': item2['fe_line'],
                    'Description_2': item2['description'],
                    'Similarity_Score': round(similarity_score, 4),
                    'Resemblance_Percentage': round(similarity_score * 100, 2),
                    'Cluster_ID': cluster_id,
                    'Same_File': item1['file'] == item2['file'],
                    'Same_Level': item1['level'] == item2['level'],
                    'Exact_ID_Match': item1['item_id'].lower().strip() == item2['item_id'].lower().strip(),
                    'Exact_FE_Match': item1['fe_line'].lower().strip() == item2['fe_line'].lower().strip(),
                    'Cross_File_Match': item1['file'] != item2['file']
                }
                similarity_records.append(record)

    similarity_df = pd.DataFrame(similarity_records)

    if len(similarity_df) > 0:
        # Sort by similarity score descending
        similarity_df = similarity_df.sort_values('Similarity_Score', ascending=False)
        print(f"    ✅ Created similarity DataFrame with {len(similarity_df)} similar item pairs")
        print(f"    📊 Cross-file matches: {similarity_df['Cross_File_Match'].sum()}")
        print(f"    📊 Same-file matches: {(~similarity_df['Cross_File_Match']).sum()}")
    else:
        print(f"    ⚠️  No similar items found above threshold {threshold}")

    return similarity_df

def main():
    print("🚀 Starting Global BOM Similarity Analysis with Caching")
    print("=" * 70)
    print(f"⏰ Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Analysis Focus: Level 1, 2, 3 items with global clustering and dendrogram")

    input_folder = 'input'
    output_folder = 'output'
    cache_folder = 'cache'

    # Check if input folder exists
    if not os.path.exists(input_folder):
        print(f"❌ Error: Input folder '{input_folder}' not found!")
        print("Please create the 'input' folder and place your Excel files there.")
        return

    # Create folders if they don't exist
    os.makedirs(output_folder, exist_ok=True)
    os.makedirs(cache_folder, exist_ok=True)
    print(f"📁 Output folder: {os.path.abspath(output_folder)}")
    print(f"💾 Cache folder: {os.path.abspath(cache_folder)}")

    # Get all Excel files
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    print(f"📊 Found {len(excel_files)} Excel files to analyze")

    if len(excel_files) == 0:
        print("❌ No Excel files found in the input folder!")
        print("Please add .xlsx or .xls files to the 'input' folder.")
        return

    # Check cache validity
    print("\n🔍 Checking cache validity...")
    cache_valid, file_info = check_cache_validity(input_folder, cache_folder)
    cache_data_file = os.path.join(cache_folder, 'bom_data.pkl')

    if cache_valid and os.path.exists(cache_data_file):
        print("✅ Cache is valid! Loading preprocessed data...")
        bom_data = load_processed_data(cache_data_file)
        if bom_data is not None:
            print(f"📈 Loaded {len(bom_data)} files from cache")
            total_items = sum(len(df) for df in bom_data.values())
            print(f"📊 Total Level 1,2,3 items: {total_items}")
        else:
            print("⚠️  Cache corrupted, will reload from files")
            cache_valid = False

    if not cache_valid:
        print("� Loading and preprocessing BOM data from files...")
        bom_data = {}
        failed_files = []

        for i, file in enumerate(excel_files, 1):
            print(f"  [{i}/{len(excel_files)}] Loading: {file}")
            file_path = os.path.join(input_folder, file)

            try:
                df = load_bom_data(file_path)
                if df is not None:
                    bom_data[file] = df
                    print(f"    ✅ Loaded {len(df)} Level 1,2,3 items")
                else:
                    failed_files.append(file)
                    print(f"    ❌ Failed to load (check file format)")
            except Exception as e:
                failed_files.append(file)
                print(f"    ❌ Error: {str(e)}")

        print(f"\n📈 Successfully loaded {len(bom_data)} files")
        if failed_files:
            print(f"⚠️  Failed to load {len(failed_files)} files: {failed_files}")

        # Save to cache
        print("💾 Saving preprocessed data to cache...")
        if save_processed_data(bom_data, cache_data_file):
            # Save file info for cache validation
            cache_info_file = os.path.join(cache_folder, 'cache_info.pkl')
            save_processed_data(file_info, cache_info_file)
            print("✅ Data cached successfully!")
        else:
            print("⚠️  Could not save to cache, but continuing with analysis...")

    if len(bom_data) < 1:
        print("❌ Need at least 1 file with valid data to perform analysis!")
        return

    # GLOBAL ANALYSIS
    print(f"\n🌍 GLOBAL SIMILARITY ANALYSIS")
    print("=" * 50)

    # Create global similarity matrix
    similarity_matrix, item_metadata, distance_matrix = create_global_similarity_matrix(bom_data)

    if similarity_matrix is None:
        print("❌ Could not create global similarity matrix!")
        return

    # Create similarity clusters and dendrogram
    clusters, _ = create_similarity_clusters(similarity_matrix, item_metadata, distance_matrix, threshold=0.7)

    # Create comprehensive similarity DataFrame
    similarity_df = create_global_similarity_dataframe(similarity_matrix, item_metadata, clusters, threshold=0.6)

    # Generate comprehensive reports
    print(f"\n� Generating global analysis reports...")

    # Create cluster summary
    cluster_summary = []
    for cluster_id, cluster_items in clusters.items():
        cluster_info = {
            'Cluster_ID': cluster_id,
            'Item_Count': len(cluster_items),
            'Files_Involved': len(set(item['metadata']['file'] for item in cluster_items)),
            'Levels_Involved': list(set(item['metadata']['level'] for item in cluster_items)),
            'Sample_Items': [item['metadata']['item_id'] for item in cluster_items[:3]]  # First 3 items as sample
        }
        cluster_summary.append(cluster_info)

    cluster_summary_df = pd.DataFrame(cluster_summary)
    if len(cluster_summary_df) > 0:
        cluster_summary_df = cluster_summary_df.sort_values('Item_Count', ascending=False)

    # Generate enhanced summary report
    print(f"\n📝 Generating detailed reports...")

    with open(os.path.join(output_folder, 'similarity_summary.txt'), 'w', encoding='utf-8') as f:
        f.write("BOM Similarity Analysis Summary - Level 1&2 Multi-Column Analysis\n")
        f.write("=" * 70 + "\n")
        f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Analysis Method: Multi-Column TF-IDF + Cosine Similarity\n")
        f.write(f"Data Requirement: Complete data in all 4 columns + Level 1 or 2 only\n")
        f.write(f"Similarity Focus: Item ID (35%) + Item Description (50%) + F-E-Line bonus (15%)\n")
        f.write(f"Similarity Thresholds: Exact (>90%), High (75-90%), Medium (60-75%)\n\n")

        for result in comparison_results:
            f.write(f"File: {result['file']}\n")
            f.write(f"Valid Items (complete data): {result['total_items']}\n")
            f.write("-" * 40 + "\n")

            for comp in result['comparisons']:
                f.write(f"  Compared with: {comp['compared_with']}\n")
                f.write(f"  Total Matches: {comp['matches']}\n")
                f.write(f"  Exact Matches (>95%): {comp['exact_matches']}\n")
                f.write(f"  Overall Similarity: {comp['similarity_percentage']:.2f}%\n")

                if comp['match_details']:
                    f.write("  \n  Top matches by combined similarity:\n")

                    # Show top 10 matches by similarity with full details
                    top_matches = sorted(comp['match_details'], key=lambda x: x['similarity'], reverse=True)[:10]
                    for i, match in enumerate(top_matches, 1):
                        f.write(f"    {i:2d}. Combined Similarity: {match['similarity']:.3f}\n")
                        f.write(f"        Description Sim: {match['desc_similarity']:.3f} | ID Sim: {match['id_similarity']:.3f}\n")
                        f.write(f"        Exact ID Match: {match['exact_id_match']} | Exact F-E Match: {match['exact_fe_match']}\n")
                        f.write(f"        Item 1: {match['item1']} | Level: {match['level1']} | F-E-Line: {match['fe_line1']}\n")
                        f.write(f"        Desc 1: {match['desc1']}\n")
                        f.write(f"        Item 2: {match['item2']} | Level: {match['level2']} | F-E-Line: {match['fe_line2']}\n")
                        f.write(f"        Desc 2: {match['desc2']}\n")
                        f.write(f"        ----\n")
                else:
                    f.write("  No matches found above similarity threshold.\n")

                f.write("\n")

            f.write("\n")

        # Global summary
        f.write("Global Summary\n")
        f.write("==============\n")
        f.write(f"Total files analyzed: {len(bom_data)}\n")
        f.write(f"Total comparisons: {sum(len(r['comparisons']) for r in comparison_results)}\n")
        f.write(f"Total matches found: {len(global_matches)}\n")

        # Most common similar parts
        if global_matches:
            f.write("\nMost Common Similar Parts:\n")
            # Count occurrences of each item
            item_counts = {}
            for match in global_matches:
                item_counts[match['item1']] = item_counts.get(match['item1'], 0) + 1
                item_counts[match['item2']] = item_counts.get(match['item2'], 0) + 1

            # Show top 10 most common items
            top_items = sorted(item_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            for item, count in top_items:
                f.write(f"  {item}: appears in {count} matches\n")

    # Generate enhanced Excel reports

    # Summary report
    summary_report = pd.DataFrame([
        {
            'File1': result['file'],
            'File2': comp['compared_with'],
            'Valid_Items_File1': result['total_items'],
            'Valid_Items_File2': bom_data[comp['compared_with']].shape[0],
            'Total_Matches': comp['matches'],
            'Exact_Matches_95_Plus': comp['exact_matches'],
            'Similarity_Percentage': round(comp['similarity_percentage'], 2),
            'Analysis_Date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        for result in comparison_results
        for comp in result['comparisons']
    ])

    # Detailed matches report with enhanced metrics
    detailed_matches = []
    for result in comparison_results:
        for comp in result['comparisons']:
            for match in comp['match_details']:
                detailed_matches.append({
                    'File1': result['file'],
                    'File2': comp['compared_with'],
                    'Item_ID_1': match['item1'],
                    'Level_1': match['level1'],
                    'FE_Line_1': match['fe_line1'],
                    'Description_1': match['desc1'],
                    'Item_ID_2': match['item2'],
                    'Level_2': match['level2'],
                    'FE_Line_2': match['fe_line2'],
                    'Description_2': match['desc2'],
                    'Combined_Similarity_Score': round(match['similarity'], 4),
                    'Description_Similarity': round(match['desc_similarity'], 4),
                    'ID_Similarity': round(match['id_similarity'], 4),
                    'Exact_ID_Match': match['exact_id_match'],
                    'Exact_FE_Match': match['exact_fe_match'],
                    'Match_Category': 'Exact' if match['similarity'] > 0.9 else 'High' if match['similarity'] > 0.75 else 'Medium'
                })

    detailed_matches_df = pd.DataFrame(detailed_matches)

    # Save Excel reports
    try:
        with pd.ExcelWriter(os.path.join(output_folder, 'bom_similarity_analysis.xlsx'), engine='openpyxl') as writer:
            # Summary sheet
            summary_report.to_excel(writer, sheet_name='Summary', index=False)

            # Detailed matches sheet
            if not detailed_matches_df.empty:
                detailed_matches_df.to_excel(writer, sheet_name='Detailed_Matches', index=False)
            else:
                # Create empty sheet with headers if no matches
                empty_df = pd.DataFrame(columns=['File1', 'File2', 'Item_ID_1', 'Description_1', 'Item_ID_2', 'Description_2', 'Similarity_Score'])
                empty_df.to_excel(writer, sheet_name='Detailed_Matches', index=False)

        print(f"\n📊 Excel reports saved: bom_similarity_analysis.xlsx")
        print(f"    📋 Summary sheet: Overview of all Level 1&2 comparisons")
        print(f"    📋 Detailed_Matches sheet: All individual matches with multi-column analysis")

    except Exception as e:
        print(f"\n❌ Error saving Excel report: {str(e)}")
        traceback.print_exc()

    print(f"\n🎉 Level 1&2 Multi-Column BOM Analysis Complete!")
    print("=" * 60)
    print(f"⏰ End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Reports saved to: {os.path.abspath(output_folder)}")
    print(f"📊 Total files analyzed: {len(bom_data)}")
    print(f"🔍 Total Level 1&2 comparisons performed: {total_comparisons}")
    print(f"🎯 Total matches found: {len(global_matches)}")
    print("\n📋 Generated files:")
    print("  • similarity_summary.txt - Human-readable Level 1&2 analysis summary")
    print("  • bom_similarity_analysis.xlsx - Detailed Excel report with multi-column metrics")

    if len(global_matches) > 0:
        print(f"\n🏆 Top Level 1&2 similar items found (by combined score):")
        # Show top 5 matches by similarity
        top_global_matches = sorted(global_matches, key=lambda x: x['similarity'], reverse=True)[:5]
        for i, match in enumerate(top_global_matches, 1):
            id_match_indicator = " [ID✓]" if match['exact_id_match'] else ""
            fe_match_indicator = " [FE✓]" if match['exact_fe_match'] else ""
            print(f"  {i}. {match['item1']} ↔ {match['item2']} ({match['similarity']:.3f}){id_match_indicator}{fe_match_indicator}")
    else:
        print("\n⚠️  No similar Level 1&2 items found with current similarity threshold (0.6)")
        print("   Consider lowering the threshold in the code if needed.")

if __name__ == "__main__":
    main()

