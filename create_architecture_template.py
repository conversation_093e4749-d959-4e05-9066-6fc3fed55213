#!/usr/bin/env python3
"""
Create Architecture Mapping Template
Creates Excel template for manual component architecture mapping
"""

import pandas as pd
import os

def create_architecture_template():
    """Create Excel template for manual architecture mapping"""
    print("📋 Creating Architecture Mapping Template...")
    
    # Sample data to guide user
    sample_data = [
        {
            'Component_Name': 'RF Amplifier Module',
            'Item_ID_Pattern': 'RF-AMP-*',
            'Color_Code': 'Blue',
            'Component_Type': 'Mandatory',
            'Slide_Number': 5,
            'Architecture_Level': 1,
            'Shipping_Impact': 1,
            'Creation_Impact': 1,
            'Context': 'Main_Assembly',
            'Notes': 'Critical RF component - always required'
        },
        {
            'Component_Name': 'Display Module 15inch',
            'Item_ID_Pattern': 'DISP-15-*',
            'Color_Code': 'Purple',
            'Component_Type': 'Mandatory_Selectable',
            'Slide_Number': 3,
            'Architecture_Level': 2,
            'Shipping_Impact': 1,
            'Creation_Impact': 0,
            'Context': 'Display_Options',
            'Notes': 'User can select 15" or 17" display'
        },
        {
            'Component_Name': 'Extended Memory Kit',
            'Item_ID_Pattern': 'MEM-EXT-*',
            'Color_Code': 'Red',
            'Component_Type': 'Optional',
            'Slide_Number': 7,
            'Architecture_Level': 3,
            'Shipping_Impact': 0,
            'Creation_Impact': 0,
            'Context': 'Accessories',
            'Notes': 'Optional upgrade - customer choice'
        },
        {
            'Component_Name': 'Software License Pro',
            'Item_ID_Pattern': 'SW-LIC-PRO',
            'Color_Code': 'Purple',
            'Component_Type': 'Mandatory_Selectable',
            'Slide_Number': 9,
            'Architecture_Level': 4,
            'Shipping_Impact': 0,
            'Creation_Impact': 1,
            'Context': 'Software',
            'Notes': 'Pro or Standard license required'
        },
        {
            'Component_Name': 'Cooling Fan Assembly',
            'Item_ID_Pattern': 'FAN-COOL-*',
            'Color_Code': 'Blue',
            'Component_Type': 'Mandatory',
            'Slide_Number': 4,
            'Architecture_Level': 2,
            'Shipping_Impact': 1,
            'Creation_Impact': 1,
            'Context': 'Thermal_Management',
            'Notes': 'Required for thermal management'
        }
    ]
    
    # Create DataFrame
    template_df = pd.DataFrame(sample_data)
    
    # Add empty rows for user input
    empty_rows = []
    for i in range(20):  # 20 empty rows
        empty_row = {col: '' for col in template_df.columns}
        empty_rows.append(empty_row)
    
    # Combine sample and empty rows
    full_template = pd.concat([template_df, pd.DataFrame(empty_rows)], ignore_index=True)
    
    return full_template

def create_instructions_sheet():
    """Create instructions for using the template"""
    instructions = [
        {
            'Field': 'Component_Name',
            'Description': 'Name of component as shown in PowerPoint',
            'Example': 'RF Amplifier Module',
            'Required': 'Yes'
        },
        {
            'Field': 'Item_ID_Pattern',
            'Description': 'Pattern to match Item_IDs in BOM (use * for wildcards)',
            'Example': 'RF-AMP-*, *AMPLIFIER*',
            'Required': 'Yes'
        },
        {
            'Field': 'Color_Code',
            'Description': 'Color in PowerPoint slide',
            'Example': 'Blue, Purple, Red',
            'Required': 'Yes'
        },
        {
            'Field': 'Component_Type',
            'Description': 'Classification based on color',
            'Example': 'Mandatory, Mandatory_Selectable, Optional',
            'Required': 'Yes'
        },
        {
            'Field': 'Slide_Number',
            'Description': 'PowerPoint slide number where component appears',
            'Example': '5',
            'Required': 'No'
        },
        {
            'Field': 'Architecture_Level',
            'Description': 'Hierarchy level (1=Core, 2=Major, 3=Minor, 4=Accessory)',
            'Example': '1, 2, 3, 4',
            'Required': 'Yes'
        },
        {
            'Field': 'Shipping_Impact',
            'Description': 'Does component affect shipping? (1=Yes, 0=No)',
            'Example': '1, 0',
            'Required': 'Yes'
        },
        {
            'Field': 'Creation_Impact',
            'Description': 'Does component affect creation/manufacturing? (1=Yes, 0=No)',
            'Example': '1, 0',
            'Required': 'Yes'
        },
        {
            'Field': 'Context',
            'Description': 'Functional context of component',
            'Example': 'Main_Assembly, Accessories, Software, Display_Options',
            'Required': 'No'
        },
        {
            'Field': 'Notes',
            'Description': 'Additional notes or comments',
            'Example': 'Critical component - always required',
            'Required': 'No'
        }
    ]
    
    return pd.DataFrame(instructions)

def save_template(output_folder):
    """Save the complete template"""
    print("💾 Saving Architecture Template...")
    
    # Create template data
    template_df = create_architecture_template()
    instructions_df = create_instructions_sheet()
    
    # Save to Excel with multiple sheets
    template_file = os.path.join(output_folder, 'architecture_mapping_template.xlsx')
    
    with pd.ExcelWriter(template_file, engine='openpyxl') as writer:
        # Main template sheet
        template_df.to_excel(writer, sheet_name='Architecture_Mapping', index=False)
        
        # Instructions sheet
        instructions_df.to_excel(writer, sheet_name='Instructions', index=False)
        
        # Color coding reference
        color_ref = pd.DataFrame([
            {'Color': 'Blue', 'Component_Type': 'Mandatory', 'Description': 'Always required - core functionality'},
            {'Color': 'Purple', 'Component_Type': 'Mandatory_Selectable', 'Description': 'Required but user can choose variant'},
            {'Color': 'Red', 'Component_Type': 'Optional', 'Description': 'Customer choice - not required for basic function'}
        ])
        color_ref.to_excel(writer, sheet_name='Color_Reference', index=False)
    
    print(f"✅ Template saved: {template_file}")
    return template_file

def main():
    """Create architecture mapping template"""
    print("📋 ARCHITECTURE MAPPING TEMPLATE CREATOR")
    print("=" * 50)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    template_file = save_template(output_folder)
    
    print(f"\n🎉 Template Creation Complete!")
    print("=" * 50)
    print(f"📁 File created: {os.path.abspath(template_file)}")
    print("\n📋 Next Steps:")
    print("1. Open the Excel file")
    print("2. Review the sample data in rows 1-5")
    print("3. Fill in your component mappings in empty rows")
    print("4. Use the Instructions sheet for guidance")
    print("5. Save and use for BOM enrichment")

if __name__ == "__main__":
    main()
