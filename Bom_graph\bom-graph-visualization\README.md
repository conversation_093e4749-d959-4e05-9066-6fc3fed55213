# BOM Graph Visualization Project

This project visualizes a Bill of Materials (BOM) structure using a graph representation. It encodes item descriptions using the SBERT model and displays the relationships between items based on their F-E-Line, Level, Item Number, and Item Description.

## Project Structure

```
bom-graph-visualization
├── src
│   ├── app.py                # Main entry point of the application
│   ├── graph_visualization.py # Functions for creating and rendering the BOM graph
│   ├── sbert_encoder.py      # SBERT encoding for item descriptions
│   ├── data_loader.py        # Data loading from CSV or Excel
│   └── types
│       └── __init__.py      # Custom types and data structures
├── requirements.txt          # Project dependencies
└── README.md                 # Project documentation
```

## Setup Instructions

1. Clone the repository:
   ```
   git clone <repository-url>
   cd bom-graph-visualization
   ```

2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage

To run the application, execute the following command:
```
python src/app.py
```

This will load the BOM data, encode the item descriptions, and visualize the BOM structure as a graph.

## Functionality

- **Data Loading**: The project can load BOM data from various sources, including CSV and Excel files.
- **Graph Visualization**: It creates a visual representation of the BOM structure using NetworkX and Matplotlib.
- **SBERT Encoding**: Item descriptions are encoded using the SBERT model for better similarity representation.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.