import torch
import cv2
import numpy as np
from simple_detector import SimpleArchitectureDetector

class RegionDetector:
    def __init__(self, model_path=None, use_simple_detector=True):
        """
        Initialize detector with pre-trained or custom model
        """
        self.use_simple_detector = use_simple_detector

        if use_simple_detector:
            # Use simple OpenCV-based detector for architectural elements
            self.simple_detector = SimpleArchitectureDetector()
            self.model = None
        else:
            if model_path and model_path.endswith('.pt'):
                # Load custom trained model
                self.model = torch.hub.load('ultralytics/yolov5', 'custom', path=model_path, trust_repo=True)
            else:
                # Use pre-trained YOLOv5 nano for general object detection
                self.model = torch.hub.load('ultralytics/yolov5', 'yolov5n', trust_repo=True)

            # Set to CPU mode for compatibility
            self.model.cpu()
            self.simple_detector = None
    
    def detect_regions(self, img):
        """
        Detect regions using either simple detector or YOLO
        """
        if self.use_simple_detector:
            # Use simple OpenCV-based detection
            enhanced_img = self.simple_detector.enhance_image_for_detection(img)
            detections = self.simple_detector.detect_regions(enhanced_img)
        else:
            # Use YOLO detection
            results = self.model(img)
            detections = []

            for *xyxy, conf, cls in results.xyxy[0]:
                if conf > 0.3:  # Confidence threshold
                    label = self.model.names[int(cls)]
                    x1, y1, x2, y2 = map(int, xyxy)
                    detections.append({
                        'label': self.map_label_to_type(label),
                        'xmin': x1,
                        'ymin': y1,
                        'xmax': x2,
                        'ymax': y2,
                        'confidence': float(conf)
                    })

        return detections
    
    def map_label_to_type(self, yolo_label):
        """
        Map YOLO labels to our box/satellite types
        """
        # Map common YOLO labels to our architecture types
        box_labels = ['person', 'car', 'truck', 'bus', 'train', 'boat']
        satellite_labels = ['bicycle', 'motorcycle', 'airplane', 'traffic light', 'stop sign']
        
        if yolo_label in box_labels:
            return 'box'
        elif yolo_label in satellite_labels:
            return 'satellite'
        else:
            # Default classification based on size could be added here
            return 'box'  # Default to box for unknown objects
