# Contents of /bom-graph-visualization/bom-graph-visualization/src/main.py

import pandas as pd
from data_loader import load_data
from graph_utils import create_graph, add_edges
from visualization import visualize_graph

def main():
    # Load BOM data
    bom_data = load_data("path/to/your/bom_data.xlsx")  # Update with actual path

    # Create graph from BOM data
    graph = create_graph(bom_data)

    # Add edges based on relationships in the BOM data
    add_edges(graph, bom_data)

    # Visualize the graph
    visualize_graph(graph)

if __name__ == "__main__":
    main()