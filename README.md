# 🔧 BOM Cluster Analysis

A comprehensive tool for analyzing Bill of Materials (BOM) similarity and clustering using advanced NLP techniques.

## 🚀 Features

- **BOM Similarity Analysis**: Compare multiple BOM files using TF-IDF and cosine similarity
- **Advanced Text Processing**: Leverage sentence transformers and transformer models
- **Graph Visualization**: Visualize BOM relationships and clusters
- **Clustering Pipeline**: Advanced clustering using UMAP, HDBSCAN, and other ML techniques
- **Interactive Reports**: Generate detailed Excel and text reports
- **Jupyter Integration**: Interactive analysis notebooks

## 📋 Requirements

- Python 3.8 or higher
- Windows, macOS, or Linux

## 🛠️ Quick Setup

### Option 1: Automated Setup (Recommended)

Run the setup script to create a complete environment:

```bash
python setup_environment.py
```

Then activate the environment:

**Windows:**
```cmd
activate_env.bat
```

**macOS/Linux:**
```bash
source activate_env.sh
```

### Option 2: Manual Setup

1. **Create virtual environment:**
   ```bash
   python -m venv bom_analysis_env
   ```

2. **Activate environment:**
   
   **Windows:**
   ```cmd
   bom_analysis_env\Scripts\activate
   ```
   
   **macOS/Linux:**
   ```bash
   source bom_analysis_env/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

### Option 3: Using pip with pyproject.toml

```bash
pip install -e .
# Or with optional dependencies
pip install -e ".[all]"
```

## 📁 Project Structure

```
BOM_Cluster/
├── 📄 bom_similarity_analysis.py    # Main analysis script
├── 📂 input/                        # Place your Excel BOM files here
├── 📂 output/                       # Analysis results
├── 📂 scripts/                      # Additional analysis scripts
│   └── BOM_Clustering_Pipeline.ipynb
├── 📂 Graph_BOM/                    # Graph visualization tools
├── 📂 all-MiniLM-L6-v2/            # Sentence transformer model
├── 📂 flan-t5-base/                # T5 model for advanced NLP
├── 📄 requirements.txt              # Python dependencies
├── 📄 pyproject.toml               # Modern Python project config
└── 📄 README.md                    # This file
```

## 🎯 Usage

### Basic BOM Analysis

1. **Place your Excel files** in the `input/` folder
2. **Run the analysis:**
   ```bash
   python bom_similarity_analysis.py
   ```
3. **Check results** in the `output/` folder

### Jupyter Analysis

1. **Start Jupyter Lab:**
   ```bash
   jupyter lab
   ```
2. **Open** `scripts/BOM_Clustering_Pipeline.ipynb`
3. **Select kernel:** "BOM Analysis" (if you used the setup script)

### Advanced Usage

```python
from bom_similarity_analysis import load_bom_data, calculate_similarity

# Load BOM data
df1 = load_bom_data('input/bom1.xlsx')
df2 = load_bom_data('input/bom2.xlsx')

# Calculate similarity
matches, exact_matches, similarity_percentage = calculate_similarity(df1, df2)

print(f"Found {len(matches)} matches with {similarity_percentage:.2f}% similarity")
```

## 📊 Output Files

The analysis generates several output files:

- **`similarity_summary.txt`**: Human-readable summary report
- **`similarity_report.xlsx`**: Detailed Excel report with all comparisons
- **Individual comparison files**: Detailed match information

## 🔧 Configuration

### Excel File Format

Your Excel files should have:
- **Sheet 2** (index 1) containing BOM data
- **Columns**: Level, Item ID, F-E-Line, Item Description
- **Headers** in the first row

### Similarity Thresholds

You can adjust similarity thresholds in `bom_similarity_analysis.py`:

```python
if sim_score > 0.8:  # General similarity threshold
    # ... 
if sim_score > 0.95:  # "Exact" match threshold
    # ...
```

## 🧪 Development

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black .
flake8 .
```

### Type Checking

```bash
mypy .
```

## 📦 Dependencies

### Core Libraries
- **pandas**: Data manipulation and analysis
- **numpy**: Numerical computing
- **scikit-learn**: Machine learning algorithms
- **sentence-transformers**: Advanced text embeddings

### Visualization
- **matplotlib**: Basic plotting
- **seaborn**: Statistical visualization
- **plotly**: Interactive plots
- **networkx**: Graph analysis

### Optional Enhancements
- **umap-learn**: Dimensionality reduction
- **hdbscan**: Density-based clustering
- **spacy**: Advanced NLP
- **streamlit**: Web interfaces

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and formatting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

**"No module named 'sentence_transformers'"**
- Make sure you've activated the virtual environment
- Run `pip install sentence-transformers`

**"Excel file not found"**
- Ensure your Excel files are in the `input/` folder
- Check file permissions

**"Memory error during analysis"**
- Try processing fewer files at once
- Consider using a machine with more RAM

### Getting Help

1. Check the [Issues](https://github.com/your-org/bom-cluster-analysis/issues) page
2. Create a new issue with:
   - Error message
   - Steps to reproduce
   - Your environment details

## 🎉 Acknowledgments

- Built with [sentence-transformers](https://www.sbert.net/)
- Uses [scikit-learn](https://scikit-learn.org/) for machine learning
- Powered by [pandas](https://pandas.pydata.org/) for data processing
