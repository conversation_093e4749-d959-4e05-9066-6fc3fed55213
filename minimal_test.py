#!/usr/bin/env python3
"""
Minimal test to check if we can run the analysis
"""

print("🧪 Minimal BOM Analysis Test")
print("=" * 30)

# Test 1: Check packages
print("1. Testing package imports...")
try:
    import pandas as pd
    print("✅ pandas imported")
except Exception as e:
    print(f"❌ pandas error: {e}")
    exit(1)

try:
    import numpy as np
    print("✅ numpy imported")
except Exception as e:
    print(f"❌ numpy error: {e}")
    exit(1)

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    print("✅ sklearn imported")
except Exception as e:
    print(f"❌ sklearn error: {e}")
    exit(1)

# Test 2: Check input folder
print("\n2. Checking input folder...")
import os
if os.path.exists('input'):
    excel_files = [f for f in os.listdir('input') if f.endswith(('.xlsx', '.xls'))]
    print(f"✅ Found {len(excel_files)} Excel files")
    if excel_files:
        print(f"   First file: {excel_files[0]}")
else:
    print("❌ Input folder not found")
    exit(1)

# Test 3: Try to read one Excel file
print("\n3. Testing Excel file reading...")
try:
    test_file = excel_files[0]
    file_path = os.path.join('input', test_file)
    
    xls = pd.ExcelFile(file_path)
    print(f"✅ Excel file opened: {len(xls.sheet_names)} sheets")
    
    if len(xls.sheet_names) >= 2:
        df = pd.read_excel(file_path, sheet_name=1)
        print(f"✅ Second sheet read: {df.shape[0]} rows, {df.shape[1]} columns")
        
        if df.shape[1] >= 4:
            df_sample = df.iloc[:3, :4]
            print("✅ First 4 columns sample:")
            print(df_sample.to_string())
        else:
            print(f"⚠️  Only {df.shape[1]} columns available")
    else:
        print("❌ No second sheet found")
        
except Exception as e:
    print(f"❌ Excel reading error: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 Test complete!")
print("If all tests passed, the main analysis should work.")
