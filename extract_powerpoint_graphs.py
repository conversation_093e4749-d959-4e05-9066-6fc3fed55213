#!/usr/bin/env python3
"""
PowerPoint Graph Extractor
Extracts product architecture graphs with color-coded components
Focus on cases with Product IDs and their specific colors
"""

import os
import pandas as pd
from pptx import Presentation
from pptx.dml.color import RGBColor
import csv
import re
from collections import defaultdict

def extract_color_rgb(shape):
    """Extract RGB color from shape with multiple fallback methods"""
    
    try:
        # Method 1: Fill color
        if hasattr(shape, 'fill') and shape.fill.type == 1:  # Solid fill
            if hasattr(shape.fill, 'fore_color') and hasattr(shape.fill.fore_color, 'rgb'):
                rgb = shape.fill.fore_color.rgb
                return (rgb.r, rgb.g, rgb.b)
        
        # Method 2: Line color
        if hasattr(shape, 'line') and hasattr(shape.line, 'color'):
            if hasattr(shape.line.color, 'rgb'):
                rgb = shape.line.color.rgb
                return (rgb.r, rgb.g, rgb.b)
        
        # Method 3: Text color (first run)
        if hasattr(shape, 'text_frame') and shape.text_frame.paragraphs:
            for paragraph in shape.text_frame.paragraphs:
                for run in paragraph.runs:
                    if hasattr(run.font, 'color') and hasattr(run.font.color, 'rgb'):
                        rgb = run.font.color.rgb
                        return (rgb.r, rgb.g, rgb.b)
        
        return None
        
    except Exception as e:
        return None

def classify_color(rgb):
    """Classify RGB color into business categories"""
    if not rgb:
        return "Unknown", "No Color"
    
    r, g, b = rgb
    
    # Blue variants (Mandatory)
    if b > 150 and r < 100 and g < 150:
        return "Blue", "Mandatory"
    
    # Purple/Magenta variants (Mandatory Selectable)
    if r > 100 and b > 100 and g < 150:
        return "Purple", "Mandatory_Selectable"
    
    # Red variants (Optional)
    if r > 150 and g < 100 and b < 100:
        return "Red", "Optional"
    
    # Light Blue variants
    if b > 100 and r < 200 and g < 200 and (b - r > 50 or b - g > 50):
        return "Light_Blue", "Mandatory"
    
    # Orange/Yellow (might be special category)
    if r > 200 and g > 150 and b < 100:
        return "Orange", "Special"
    
    # Green variants
    if g > 150 and r < 100 and b < 100:
        return "Green", "Special"
    
    # Gray variants (might be inactive/placeholder)
    if abs(r - g) < 30 and abs(g - b) < 30 and abs(r - b) < 30:
        if r > 150:
            return "Light_Gray", "Inactive"
        else:
            return "Dark_Gray", "Inactive"
    
    # Default for unclassified colors
    return f"Custom_RGB({r},{g},{b})", "Unclassified"

def extract_product_id(text):
    """Extract product ID from text using various patterns"""
    if not text:
        return None
    
    text = str(text).strip()
    
    # Pattern 1: Pure numbers (like 4516256)
    number_match = re.search(r'\b\d{6,}\b', text)
    if number_match:
        return number_match.group()
    
    # Pattern 2: Alphanumeric codes (like ABC123, XYZ-456)
    alphanum_match = re.search(r'\b[A-Z]{2,}[-_]?\d{3,}\b', text.upper())
    if alphanum_match:
        return alphanum_match.group()
    
    # Pattern 3: Product codes with letters and numbers
    product_match = re.search(r'\b[A-Z0-9]{4,}[-_][A-Z0-9]{2,}\b', text.upper())
    if product_match:
        return product_match.group()
    
    # Pattern 4: Any sequence of 4+ alphanumeric characters
    general_match = re.search(r'\b[A-Z0-9]{4,}\b', text.upper())
    if general_match:
        return general_match.group()
    
    return None

def is_likely_product_component(text):
    """Determine if text likely represents a product component"""
    if not text:
        return False
    
    text = str(text).strip().lower()
    
    # Skip very short text
    if len(text) < 3:
        return False
    
    # Skip common UI elements
    ui_elements = ['slide', 'title', 'header', 'footer', 'page', 'click', 'next', 'previous']
    if any(element in text for element in ui_elements):
        return False
    
    # Look for product-like indicators
    product_indicators = [
        r'\d{4,}',  # 4+ digit numbers
        r'[a-z]{2,}\s*\d+',  # letters followed by numbers
        r'\b(model|type|version|variant|option)\b',  # product terms
        r'\b(module|unit|assembly|component|part)\b',  # component terms
    ]
    
    for pattern in product_indicators:
        if re.search(pattern, text):
            return True
    
    return False

def extract_slide_components(slide, slide_number):
    """Extract all components from a single slide"""
    print(f"  🔍 Analyzing slide {slide_number}...")
    
    components = []
    shape_count = 0
    
    for shape in slide.shapes:
        shape_count += 1
        
        # Extract text
        text_content = ""
        if hasattr(shape, 'text') and shape.text:
            text_content = shape.text.strip()
        
        # Extract color
        rgb_color = extract_color_rgb(shape)
        color_name, component_type = classify_color(rgb_color)
        
        # Skip if no meaningful content
        if not text_content and color_name == "Unknown":
            continue
        
        # Extract product ID
        product_id = extract_product_id(text_content)
        
        # Determine if this is likely a product component
        is_component = is_likely_product_component(text_content) or product_id is not None
        
        # Create component entry
        component = {
            'Slide_Number': slide_number,
            'Shape_Index': shape_count,
            'Text_Content': text_content,
            'Product_ID': product_id,
            'RGB_Color': f"({rgb_color[0]},{rgb_color[1]},{rgb_color[2]})" if rgb_color else "None",
            'Color_Name': color_name,
            'Component_Type': component_type,
            'Is_Product_Component': is_component,
            'Shape_Type': str(shape.shape_type) if hasattr(shape, 'shape_type') else "Unknown"
        }
        
        components.append(component)
    
    # Filter to likely product components
    product_components = [c for c in components if c['Is_Product_Component']]
    
    print(f"    📦 Found {len(components)} total shapes, {len(product_components)} likely product components")
    
    return product_components

def analyze_powerpoint_graphs(pptx_path):
    """Main function to extract graph data from PowerPoint"""
    print("📊 Extracting PowerPoint Graph Data...")
    print(f"📁 File: {os.path.basename(pptx_path)}")
    
    if not os.path.exists(pptx_path):
        print(f"❌ File not found: {pptx_path}")
        return []
    
    try:
        prs = Presentation(pptx_path)
        print(f"📋 Total slides: {len(prs.slides)}")
        
        all_components = []
        
        for slide_idx, slide in enumerate(prs.slides, 1):
            slide_components = extract_slide_components(slide, slide_idx)
            all_components.extend(slide_components)
        
        print(f"✅ Extraction complete: {len(all_components)} components found")
        return all_components
        
    except Exception as e:
        print(f"❌ Error analyzing PowerPoint: {e}")
        return []

def create_summary_statistics(components):
    """Create summary statistics of extracted data"""
    print("\n📊 Creating summary statistics...")
    
    # Color distribution
    color_counts = defaultdict(int)
    type_counts = defaultdict(int)
    slide_counts = defaultdict(int)
    
    for comp in components:
        color_counts[comp['Color_Name']] += 1
        type_counts[comp['Component_Type']] += 1
        slide_counts[comp['Slide_Number']] += 1
    
    # Product ID statistics
    components_with_id = [c for c in components if c['Product_ID']]
    
    summary = {
        'total_components': len(components),
        'components_with_product_id': len(components_with_id),
        'unique_slides': len(slide_counts),
        'color_distribution': dict(color_counts),
        'type_distribution': dict(type_counts),
        'slide_distribution': dict(slide_counts)
    }
    
    print(f"📈 Summary Statistics:")
    print(f"   Total components: {summary['total_components']}")
    print(f"   With Product ID: {summary['components_with_product_id']}")
    print(f"   Slides analyzed: {summary['unique_slides']}")
    
    print(f"\n🎨 Color Distribution:")
    for color, count in sorted(color_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"   {color}: {count}")
    
    print(f"\n🏷️ Component Type Distribution:")
    for comp_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"   {comp_type}: {count}")
    
    return summary

def save_extraction_results(components, summary, output_folder):
    """Save extraction results to CSV and Excel"""
    print("\n💾 Saving extraction results...")
    
    # Create DataFrame
    df = pd.DataFrame(components)
    
    # Save main CSV (simple format as requested)
    csv_file = os.path.join(output_folder, 'powerpoint_components_colors.csv')
    df.to_csv(csv_file, index=False)
    
    # Save detailed Excel with multiple sheets
    excel_file = os.path.join(output_folder, 'powerpoint_graph_extraction.xlsx')
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # Main data
        df.to_excel(writer, sheet_name='All_Components', index=False)
        
        # Product components only
        product_df = df[df['Is_Product_Component'] == True]
        product_df.to_excel(writer, sheet_name='Product_Components', index=False)
        
        # Components with Product IDs
        id_df = df[df['Product_ID'].notna()]
        id_df.to_excel(writer, sheet_name='With_Product_ID', index=False)
        
        # Summary statistics
        summary_df = pd.DataFrame([summary])
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        # Color mapping reference
        color_ref = pd.DataFrame([
            {'Color_Name': 'Blue', 'Component_Type': 'Mandatory', 'Description': 'Required component'},
            {'Color_Name': 'Purple', 'Component_Type': 'Mandatory_Selectable', 'Description': 'Required but selectable variant'},
            {'Color_Name': 'Red', 'Component_Type': 'Optional', 'Description': 'Optional component'},
            {'Color_Name': 'Light_Blue', 'Component_Type': 'Mandatory', 'Description': 'Required component (light variant)'},
            {'Color_Name': 'Orange', 'Component_Type': 'Special', 'Description': 'Special category'},
            {'Color_Name': 'Green', 'Component_Type': 'Special', 'Description': 'Special category'},
            {'Color_Name': 'Light_Gray', 'Component_Type': 'Inactive', 'Description': 'Inactive/placeholder'},
            {'Color_Name': 'Dark_Gray', 'Component_Type': 'Inactive', 'Description': 'Inactive/placeholder'}
        ])
        color_ref.to_excel(writer, sheet_name='Color_Reference', index=False)
    
    print(f"✅ Results saved:")
    print(f"   📄 CSV: {os.path.basename(csv_file)}")
    print(f"   📊 Excel: {os.path.basename(excel_file)}")
    
    return csv_file, excel_file

def main():
    """Main extraction function"""
    print("📊 POWERPOINT GRAPH EXTRACTOR")
    print("=" * 50)
    
    # File paths
    pptx_path = r"input\Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pptx"
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Extract components
    components = analyze_powerpoint_graphs(pptx_path)
    
    if not components:
        print("❌ No components extracted")
        return
    
    # Create summary
    summary = create_summary_statistics(components)
    
    # Save results
    csv_file, excel_file = save_extraction_results(components, summary, output_folder)
    
    print(f"\n🎉 PowerPoint Graph Extraction Complete!")
    print("=" * 50)
    print(f"📊 {len(components)} components extracted")
    print(f"📁 Results saved in: {os.path.abspath(output_folder)}")
    
    # Show key findings
    product_components = [c for c in components if c['Is_Product_Component']]
    mandatory_count = len([c for c in components if c['Component_Type'] == 'Mandatory'])
    selectable_count = len([c for c in components if c['Component_Type'] == 'Mandatory_Selectable'])
    optional_count = len([c for c in components if c['Component_Type'] == 'Optional'])
    
    print(f"\n🎯 Key Findings:")
    print(f"   Product components: {len(product_components)}")
    print(f"   Mandatory (Blue): {mandatory_count}")
    print(f"   Mandatory Selectable (Purple): {selectable_count}")
    print(f"   Optional (Red): {optional_count}")

if __name__ == "__main__":
    main()
