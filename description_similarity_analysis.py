#!/usr/bin/env python3
"""
Description Similarity Analysis
Finds items with similar descriptions across different documents using text similarity.
Includes similarity percentage and coordinates for each match.
"""

import os
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re
from collections import defaultdict
import random

def clean_description_for_similarity(text):
    """Clean and normalize description text for similarity analysis"""
    if pd.isna(text):
        return ""
    
    text = str(text).lower()
    # Remove special characters but keep spaces
    text = re.sub(r'[^\w\s]', ' ', text)
    # Remove extra whitespace
    text = ' '.join(text.split())
    return text.strip()

def load_bom_data_for_similarity():
    """Load BOM data optimized for description similarity analysis"""
    print("🔍 Loading BOM data for description similarity analysis...")
    
    input_folder = 'input'
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    
    all_items = []
    
    for file_idx, file in enumerate(excel_files, 1):
        print(f"  [{file_idx}/{len(excel_files)}] Processing: {file}")
        file_path = os.path.join(input_folder, file)
        
        try:
            # Read the second sheet
            xls = pd.ExcelFile(file_path)
            if len(xls.sheet_names) < 2:
                print(f"    ⚠️  No second sheet in {file}")
                continue
                
            df = pd.read_excel(file_path, sheet_name=1)
            
            if len(df.columns) < 4:
                print(f"    ⚠️  Not enough columns in {file}")
                continue
            
            # Use corrected column mapping
            df = df.iloc[:, :4]
            df.columns = ['Level', 'Sequence', 'Item_ID', 'Item_Description']
            
            # Clean data
            df = df.dropna(subset=['Level', 'Sequence', 'Item_ID', 'Item_Description'])
            df = df[df['Level'].astype(str).str.strip() != '']
            df = df[df['Item_ID'].astype(str).str.strip() != '']
            df = df[df['Item_Description'].astype(str).str.strip() != '']
            
            # Filter to Level 1, 2, 3
            df['Level_Number'] = df['Level'].astype(str).str.extract(r'^(\d+)').astype(float)
            df = df[df['Level_Number'].isin([1.0, 2.0, 3.0])]
            
            # Remove numeric-only Item IDs
            df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
            
            # Clean descriptions for similarity
            df['Description_Clean'] = df['Item_Description'].apply(clean_description_for_similarity)
            
            # Filter out very short descriptions (less meaningful)
            df = df[df['Description_Clean'].str.len() >= 10]
            
            # Remove common generic descriptions that would create noise
            generic_patterns = [
                r'^no description',
                r'^description not',
                r'^see drawing',
                r'^refer to',
                r'^as required',
                r'^tbd$',
                r'^tba$'
            ]
            
            for pattern in generic_patterns:
                df = df[~df['Description_Clean'].str.match(pattern)]
            
            print(f"    ✅ Processed {len(df)} valid items with meaningful descriptions")
            
            # Add coordinate information
            for idx, row in df.iterrows():
                item_info = {
                    'file_name': file,
                    'file_index': file_idx,
                    'original_row': idx + 2,  # +2 for Excel header
                    'sheet_name': xls.sheet_names[1],
                    'level': str(row['Level']),
                    'level_number': row['Level_Number'],
                    'sequence': str(row['Sequence']),
                    'item_id': str(row['Item_ID']).strip(),
                    'item_description': str(row['Item_Description']).strip(),
                    'description_clean': row['Description_Clean'],
                    'description_length': len(row['Description_Clean'])
                }
                all_items.append(item_info)
                
        except Exception as e:
            print(f"    ❌ Error processing {file}: {e}")
            continue
    
    print(f"✅ Total items loaded: {len(all_items)} from {len(excel_files)} files")
    return all_items

def find_similar_descriptions(all_items, similarity_threshold=0.5, max_items=10000):
    """Find items with similar descriptions using TF-IDF and cosine similarity"""
    print(f"\n🔍 Finding similar descriptions (threshold: {similarity_threshold*100}%)...")
    
    # Sample items if dataset is too large
    if len(all_items) > max_items:
        print(f"📊 Sampling {max_items} items from {len(all_items)} for analysis...")
        # Stratified sampling by file to ensure representation
        items_by_file = defaultdict(list)
        for item in all_items:
            items_by_file[item['file_name']].append(item)
        
        sampled_items = []
        items_per_file = max_items // len(items_by_file)
        
        for file_name, file_items in items_by_file.items():
            if len(file_items) <= items_per_file:
                sampled_items.extend(file_items)
            else:
                sampled_items.extend(random.sample(file_items, items_per_file))
        
        all_items = sampled_items[:max_items]
        print(f"✅ Using {len(all_items)} sampled items")
    
    # Prepare descriptions for TF-IDF
    descriptions = [item['description_clean'] for item in all_items]
    
    print("🔍 Creating TF-IDF vectors for description similarity...")
    
    # Create TF-IDF vectorizer optimized for part descriptions
    vectorizer = TfidfVectorizer(
        min_df=2,           # Ignore terms that appear in less than 2 documents
        max_df=0.8,         # Ignore terms that appear in more than 80% of documents
        ngram_range=(1, 3), # Use 1-3 word combinations
        stop_words='english',
        lowercase=True,
        strip_accents='unicode',
        max_features=5000   # Limit features for performance
    )
    
    try:
        tfidf_matrix = vectorizer.fit_transform(descriptions)
        print(f"✅ TF-IDF matrix created: {tfidf_matrix.shape}")
        
        print("🔍 Calculating cosine similarity matrix...")
        similarity_matrix = cosine_similarity(tfidf_matrix)
        
        print(f"🔍 Finding similar pairs above {similarity_threshold*100}% threshold...")
        similar_pairs = []
        
        n_items = len(all_items)
        for i in range(n_items):
            for j in range(i + 1, n_items):
                similarity_score = similarity_matrix[i, j]
                
                if similarity_score >= similarity_threshold:
                    item1 = all_items[i]
                    item2 = all_items[j]
                    
                    # Only include cross-file matches (different documents)
                    if item1['file_name'] != item2['file_name']:
                        pair = {
                            'Item_ID_1': item1['item_id'],
                            'Item_ID_2': item2['item_id'],
                            'Description_1': item1['item_description'],
                            'Description_2': item2['item_description'],
                            'Description_Clean_1': item1['description_clean'],
                            'Description_Clean_2': item2['description_clean'],
                            'Similarity_Score': round(similarity_score, 4),
                            'Similarity_Percentage': round(similarity_score * 100, 2),
                            'File_1': item1['file_name'],
                            'File_2': item2['file_name'],
                            'Sheet_1': item1['sheet_name'],
                            'Sheet_2': item2['sheet_name'],
                            'Row_1': item1['original_row'],
                            'Row_2': item2['original_row'],
                            'Level_1': item1['level'],
                            'Level_2': item2['level'],
                            'Level_Number_1': item1['level_number'],
                            'Level_Number_2': item2['level_number'],
                            'Sequence_1': item1['sequence'],
                            'Sequence_2': item2['sequence'],
                            'Description_Length_1': item1['description_length'],
                            'Description_Length_2': item2['description_length'],
                            'Cross_File_Match': True,
                            'Same_Item_ID': item1['item_id'].lower() == item2['item_id'].lower()
                        }
                        similar_pairs.append(pair)
        
        # Sort by similarity score (highest first)
        similar_pairs.sort(key=lambda x: x['Similarity_Score'], reverse=True)
        
        print(f"✅ Found {len(similar_pairs)} similar description pairs above {similarity_threshold*100}% threshold")
        
        return similar_pairs
        
    except Exception as e:
        print(f"❌ Error during similarity calculation: {e}")
        return []

def save_similarity_results(similar_pairs, similarity_threshold):
    """Save similarity results to Excel with detailed analysis"""
    print(f"\n💾 Saving description similarity results...")
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    if not similar_pairs:
        print("❌ No similar pairs to save")
        return
    
    # Create DataFrame
    similarity_df = pd.DataFrame(similar_pairs)
    
    # Create Excel file with multiple sheets
    excel_file = os.path.join(output_folder, f'description_similarity_{int(similarity_threshold*100)}percent.xlsx')
    
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # Main similarity results
        similarity_df.to_excel(writer, sheet_name='Description_Similarities', index=False)
        
        # Summary statistics
        summary_data = {
            'Metric': [
                'Total Similar Pairs Found',
                'Similarity Threshold Used',
                'Average Similarity Score',
                'Highest Similarity Score',
                'Cross-File Matches',
                'Same Item ID Matches',
                'Different Item ID Matches',
                'Unique Files Involved',
                'Level 1 Matches',
                'Level 2 Matches',
                'Level 3 Matches'
            ],
            'Value': [
                len(similarity_df),
                f"{similarity_threshold*100}%",
                f"{similarity_df['Similarity_Score'].mean():.3f}",
                f"{similarity_df['Similarity_Score'].max():.3f}",
                similarity_df['Cross_File_Match'].sum(),
                similarity_df['Same_Item_ID'].sum(),
                len(similarity_df) - similarity_df['Same_Item_ID'].sum(),
                len(set(similarity_df['File_1'].tolist() + similarity_df['File_2'].tolist())),
                len(similarity_df[(similarity_df['Level_Number_1'] == 1) | (similarity_df['Level_Number_2'] == 1)]),
                len(similarity_df[(similarity_df['Level_Number_1'] == 2) | (similarity_df['Level_Number_2'] == 2)]),
                len(similarity_df[(similarity_df['Level_Number_1'] == 3) | (similarity_df['Level_Number_2'] == 3)])
            ]
        }
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary_Statistics', index=False)
        
        # Top similarity ranges
        ranges = [
            (0.9, 1.0, 'Very High (90-100%)'),
            (0.8, 0.9, 'High (80-90%)'),
            (0.7, 0.8, 'Medium-High (70-80%)'),
            (0.6, 0.7, 'Medium (60-70%)'),
            (0.5, 0.6, 'Medium-Low (50-60%)')
        ]
        
        range_data = []
        for min_score, max_score, label in ranges:
            count = len(similarity_df[(similarity_df['Similarity_Score'] >= min_score) & 
                                     (similarity_df['Similarity_Score'] < max_score)])
            percentage = (count / len(similarity_df) * 100) if len(similarity_df) > 0 else 0
            range_data.append({
                'Similarity_Range': label,
                'Count': count,
                'Percentage': f"{percentage:.1f}%"
            })
        
        range_df = pd.DataFrame(range_data)
        range_df.to_excel(writer, sheet_name='Similarity_Ranges', index=False)
        
        # Most similar items (top 50)
        top_similar = similarity_df.head(50)
        top_similar.to_excel(writer, sheet_name='Top_50_Most_Similar', index=False)
    
    print(f"✅ Excel report saved: {excel_file}")
    
    # Create text summary
    text_file = os.path.join(output_folder, f'description_similarity_summary_{int(similarity_threshold*100)}percent.txt')
    
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write("DESCRIPTION SIMILARITY ANALYSIS SUMMARY\n")
        f.write("=" * 60 + "\n")
        f.write(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Similarity Threshold: {similarity_threshold*100}%\n")
        f.write(f"Total Similar Pairs Found: {len(similarity_df):,}\n\n")
        
        f.write("TOP 20 MOST SIMILAR DESCRIPTIONS\n")
        f.write("=" * 40 + "\n")
        
        for i, (_, pair) in enumerate(similarity_df.head(20).iterrows(), 1):
            f.write(f"\n{i:2d}. Similarity: {pair['Similarity_Percentage']:.1f}%\n")
            f.write(f"    Item 1: {pair['Item_ID_1']}\n")
            f.write(f"    Desc 1: {pair['Description_1'][:80]}...\n")
            f.write(f"    File 1: {pair['File_1']} (Row {pair['Row_1']}, Level {pair['Level_1']})\n")
            f.write(f"    \n")
            f.write(f"    Item 2: {pair['Item_ID_2']}\n")
            f.write(f"    Desc 2: {pair['Description_2'][:80]}...\n")
            f.write(f"    File 2: {pair['File_2']} (Row {pair['Row_2']}, Level {pair['Level_2']})\n")
            f.write(f"    Same Item ID: {pair['Same_Item_ID']}\n")
        
        f.write(f"\n\nSIMILARITY DISTRIBUTION\n")
        f.write("=" * 25 + "\n")
        for _, row in range_df.iterrows():
            f.write(f"{row['Similarity_Range']}: {row['Count']} pairs ({row['Percentage']})\n")
    
    print(f"✅ Text summary saved: {text_file}")

def main():
    print("🚀 Starting Description Similarity Analysis")
    print("=" * 60)
    print("🎯 Finding items with similar descriptions across different documents")
    print("📊 Using 50% similarity threshold with coordinate tracking")
    
    # Load BOM data
    all_items = load_bom_data_for_similarity()
    
    if not all_items:
        print("❌ No valid items found!")
        return
    
    # Find similar descriptions
    similarity_threshold = 0.5  # 50% threshold
    similar_pairs = find_similar_descriptions(all_items, similarity_threshold)
    
    # Save results
    save_similarity_results(similar_pairs, similarity_threshold)
    
    print(f"\n🎉 Description Similarity Analysis Complete!")
    print("=" * 60)
    print(f"📊 Results Summary:")
    print(f"   • {len(similar_pairs):,} similar description pairs found")
    print(f"   • {similarity_threshold*100}% minimum similarity threshold")
    print(f"   • Cross-file matches only (different documents)")
    print(f"   • Complete coordinate tracking included")
    
    if similar_pairs:
        avg_similarity = sum(pair['Similarity_Score'] for pair in similar_pairs) / len(similar_pairs)
        max_similarity = max(pair['Similarity_Score'] for pair in similar_pairs)
        print(f"   • Average similarity: {avg_similarity:.1%}")
        print(f"   • Highest similarity: {max_similarity:.1%}")
    
    print(f"\n📁 Files created:")
    print(f"   • description_similarity_50percent.xlsx - Detailed similarity analysis")
    print(f"   • description_similarity_summary_50percent.txt - Human-readable summary")

if __name__ == "__main__":
    main()
