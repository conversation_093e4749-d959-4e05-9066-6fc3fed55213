@echo off
echo 🐍 Using Existing Python Installation
echo ====================================
echo.

REM Try the existing virtual environment we found earlier
set EXISTING_PYTHON="C:\Users\<USER>\OneDrive - GE HealthCare\virtualenv\Scripts\python.exe"

echo 🔍 Checking existing Python at: %EXISTING_PYTHON%
if exist %EXISTING_PYTHON% (
    echo ✅ Found existing Python installation!
    
    echo 📋 Testing Python version:
    %EXISTING_PYTHON% --version
    
    if %errorlevel% equ 0 (
        echo ✅ Python is working!
        echo.
        echo 🔄 Creating new BOM analysis environment using this Python...
        
        REM Create new virtual environment using the existing Python
        %EXISTING_PYTHON% -m venv bom_analysis_env
        
        if %errorlevel% equ 0 (
            echo ✅ Virtual environment created successfully!
            echo.
            echo 🔄 Activating environment and installing packages...
            
            call bom_analysis_env\Scripts\activate.bat
            
            echo 🔄 Upgrading pip...
            python -m pip install --upgrade pip
            
            echo 🔄 Installing core packages...
            pip install pandas numpy scikit-learn openpyxl matplotlib
            
            echo 🔄 Installing NLP packages...
            pip install sentence-transformers transformers torch
            
            echo 🔄 Installing visualization packages...
            pip install networkx seaborn plotly jupyter
            
            echo 🔄 Installing additional packages...
            pip install tqdm joblib ipywidgets
            
            echo.
            echo 🎉 Setup completed successfully!
            echo.
            echo 📋 To use your environment:
            echo 1. Run: bom_analysis_env\Scripts\activate.bat
            echo 2. Then: python bom_similarity_analysis.py
            echo 3. Or: jupyter lab
            echo.
            echo 🧪 Testing installation...
            python test_installation.py
            
        ) else (
            echo ❌ Failed to create virtual environment
        )
    ) else (
        echo ❌ Python is not working properly
    )
) else (
    echo ❌ Existing Python not found at expected location
    echo.
    echo 💡 Let's try other approaches:
    echo.
    
    REM Try py launcher
    echo 🔍 Trying py launcher...
    py --version 2>nul
    if %errorlevel% equ 0 (
        echo ✅ py launcher works! Using it to create environment...
        py -m venv bom_analysis_env
        call bom_analysis_env\Scripts\activate.bat
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    ) else (
        echo ❌ py launcher not available
        echo.
        echo 📥 Please install Python from https://python.org/downloads/
        echo Make sure to check "Add Python to PATH" during installation
    )
)

echo.
pause
