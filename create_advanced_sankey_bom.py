import json
import pandas as pd
from collections import defaultdict

def load_bom_data():
    """Load the tree statistics for flow analysis"""

    # Load tree statistics
    with open('output/voyager_bom_tree_stats.json', 'r', encoding='utf-8') as f:
        stats = json.load(f)

    # Create a simplified dataframe from stats
    level_data = []
    for level_str, count in stats['level_distribution'].items():
        level = int(level_str)
        level_data.append({
            'Level': level,
            'Count': count,
            'F-E-Line': 'simulated'  # Simplified for visualization
        })

    df = pd.DataFrame(level_data)
    return df, stats

def analyze_level_flows(df):
    """Analyze flows between levels"""
    
    flows = defaultdict(int)
    level_items = defaultdict(list)
    
    # Group by F-E-Line to track hierarchical flows
    for f_e_line in df['F-E-Line'].unique():
        f_e_data = df[df['F-E-Line'] == f_e_line].sort_index()
        
        prev_level = None
        for _, row in f_e_data.iterrows():
            current_level = row['Level']
            level_items[current_level].append({
                'item_id': row['Item Number'],
                'description': row.get('Item Description', ''),
                'f_e_line': f_e_line
            })
            
            if prev_level is not None:
                # Create flow from previous level to current level
                flow_key = f"Level_{prev_level}_to_Level_{current_level}"
                flows[flow_key] += 1
            
            prev_level = current_level
    
    return flows, level_items

def create_advanced_sankey_html():
    """Create advanced Sankey visualization with D3.js"""

    # Load data
    df, stats = load_bom_data()

    # Calculate level statistics
    level_stats = {row['Level']: row['Count'] for _, row in df.iterrows()}
    max_level = min(10, df['Level'].max())  # Limit to 10 levels for visualization
    
    # Prepare nodes and links for D3 Sankey
    nodes = []
    links = []
    
    # Create nodes for each level
    for level in range(0, max_level + 1):
        count = level_stats.get(level, 0)
        if count > 0:
            nodes.append({
                'id': f'level_{level}',
                'name': f'Level {level}',
                'count': count,
                'level': level
            })
    
    # Create links between consecutive levels
    for i in range(len(nodes) - 1):
        source_level = nodes[i]['level']
        target_level = nodes[i + 1]['level']
        
        # Calculate flow value (simplified)
        flow_value = min(nodes[i]['count'], nodes[i + 1]['count']) // 2
        if flow_value > 0:
            links.append({
                'source': i,
                'target': i + 1,
                'value': flow_value
            })
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voyager BOM - Advanced Sankey Flow Diagram</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://unpkg.com/d3-sankey@0.12.3/dist/d3-sankey.min.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 3em;
            font-weight: 300;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .header p {{
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 1.2em;
        }}
        
        .controls {{
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            display: flex;
            justify-content: center;
            gap: 20px;
        }}
        
        .control-button {{
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }}
        
        .control-button:hover {{
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }}
        
        .visualization {{
            padding: 40px;
            min-height: 800px;
            background: linear-gradient(45deg, #f8f9fa 0%, #ffffff 100%);
        }}
        
        .sankey-container {{
            width: 100%;
            height: 700px;
            border: 2px solid #dee2e6;
            border-radius: 15px;
            background: white;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .node rect {{
            cursor: pointer;
            stroke: #000;
            stroke-width: 1px;
            transition: all 0.3s ease;
        }}
        
        .node rect:hover {{
            stroke-width: 3px;
            filter: brightness(1.1);
        }}
        
        .node text {{
            pointer-events: none;
            text-shadow: 0 1px 0 #fff;
            font-weight: bold;
            font-size: 14px;
        }}
        
        .link {{
            fill: none;
            stroke-opacity: 0.6;
            transition: all 0.3s ease;
        }}
        
        .link:hover {{
            stroke-opacity: 0.9;
            stroke-width: 3px;
        }}
        
        .tooltip {{
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 1000;
            max-width: 300px;
        }}
        
        .stats-panel {{
            margin-top: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }}
        
        .stat-card {{
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
        }}
        
        .stat-title {{
            font-size: 1.1em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }}
        
        .stat-value {{
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
        }}
        
        .stat-description {{
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 VOYAGER BOM Flow Analysis</h1>
            <p>Advanced Sankey Diagram - Hierarchical Component Flow</p>
        </div>
        
        <div class="controls">
            <button class="control-button" onclick="animateFlow()">🎬 Animate Flow</button>
            <button class="control-button" onclick="resetView()">🔄 Reset View</button>
            <button class="control-button" onclick="exportData()">💾 Export Data</button>
        </div>
        
        <div class="visualization">
            <div id="sankey" class="sankey-container"></div>
            
            <div class="stats-panel">
                <div class="stat-card">
                    <div class="stat-title">📊 Total Components</div>
                    <div class="stat-value">{sum(level_stats.values()):,}</div>
                    <div class="stat-description">Across all hierarchical levels</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🏗️ Maximum Depth</div>
                    <div class="stat-value">{max_level}</div>
                    <div class="stat-description">Deepest level in hierarchy</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🌿 F-E-Lines</div>
                    <div class="stat-value">68</div>
                    <div class="stat-description">Unique product lines</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🔗 Flow Connections</div>
                    <div class="stat-value">{len(links)}</div>
                    <div class="stat-description">Inter-level relationships</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="tooltip" id="tooltip"></div>
    
    <script>
        // Data for D3 Sankey
        const data = {{
            nodes: {json.dumps(nodes)},
            links: {json.dumps(links)}
        }};
        
        // Set up dimensions
        const margin = {{top: 20, right: 20, bottom: 20, left: 20}};
        const width = 1400 - margin.left - margin.right;
        const height = 650 - margin.top - margin.bottom;
        
        // Create SVG
        const svg = d3.select("#sankey")
            .append("svg")
            .attr("width", width + margin.left + margin.right)
            .attr("height", height + margin.top + margin.bottom);
            
        const g = svg.append("g")
            .attr("transform", `translate(${{margin.left}},${{margin.top}})`);
        
        // Create Sankey generator
        const sankey = d3.sankey()
            .nodeWidth(80)
            .nodePadding(40)
            .extent([[1, 1], [width - 1, height - 6]]);
        
        // Generate the Sankey diagram
        const {{nodes, links}} = sankey(data);
        
        // Color scale for levels
        const colorScale = d3.scaleOrdinal()
            .domain(d3.range(0, {max_level + 1}))
            .range(['#e74c3c', '#9b59b6', '#27ae60', '#f39c12', '#3498db', '#1abc9c', '#34495e', '#95a5a6', '#e67e22', '#2ecc71', '#8e44ad']);
        
        // Add links
        const link = g.append("g")
            .selectAll(".link")
            .data(links)
            .enter().append("path")
            .attr("class", "link")
            .attr("d", d3.sankeyLinkHorizontal())
            .attr("stroke", d => colorScale(d.source.level))
            .attr("stroke-width", d => Math.max(1, d.width))
            .style("mix-blend-mode", "multiply");
        
        // Add nodes
        const node = g.append("g")
            .selectAll(".node")
            .data(nodes)
            .enter().append("g")
            .attr("class", "node")
            .attr("transform", d => `translate(${{d.x0}},${{d.y0}})`);
        
        node.append("rect")
            .attr("height", d => d.y1 - d.y0)
            .attr("width", sankey.nodeWidth())
            .attr("fill", d => colorScale(d.level))
            .attr("stroke", "#000");
        
        node.append("text")
            .attr("x", -6)
            .attr("y", d => (d.y1 - d.y0) / 2)
            .attr("dy", "0.35em")
            .attr("text-anchor", "end")
            .text(d => d.name)
            .filter(d => d.x0 < width / 2)
            .attr("x", sankey.nodeWidth() + 6)
            .attr("text-anchor", "start");
        
        // Add count labels
        node.append("text")
            .attr("x", sankey.nodeWidth() / 2)
            .attr("y", d => (d.y1 - d.y0) / 2 + 20)
            .attr("dy", "0.35em")
            .attr("text-anchor", "middle")
            .style("font-size", "12px")
            .style("font-weight", "bold")
            .style("fill", "white")
            .text(d => d.count.toLocaleString());
        
        // Tooltip
        const tooltip = d3.select("#tooltip");
        
        // Add interactivity
        node.on("mouseover", function(event, d) {{
            tooltip.style("opacity", 1)
                .html(`
                    <strong>${{d.name}}</strong><br>
                    Components: ${{d.count.toLocaleString()}}<br>
                    Level: ${{d.level}}<br>
                    Percentage: ${{((d.count / {sum(level_stats.values())}) * 100).toFixed(1)}}%
                `)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 10) + "px");
        }})
        .on("mouseout", function() {{
            tooltip.style("opacity", 0);
        }});
        
        link.on("mouseover", function(event, d) {{
            tooltip.style("opacity", 1)
                .html(`
                    <strong>Flow Connection</strong><br>
                    From: ${{d.source.name}}<br>
                    To: ${{d.target.name}}<br>
                    Flow Value: ${{d.value.toLocaleString()}}
                `)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 10) + "px");
        }})
        .on("mouseout", function() {{
            tooltip.style("opacity", 0);
        }});
        
        // Animation functions
        function animateFlow() {{
            link.style("stroke-dasharray", "5,5")
                .style("stroke-dashoffset", 0)
                .transition()
                .duration(2000)
                .style("stroke-dashoffset", -10)
                .on("end", function() {{
                    d3.select(this).style("stroke-dasharray", null);
                }});
        }}
        
        function resetView() {{
            link.style("stroke-dasharray", null);
            node.select("rect").style("filter", null);
        }}
        
        function exportData() {{
            const exportData = {{
                nodes: data.nodes,
                links: data.links,
                statistics: {{
                    totalComponents: {sum(level_stats.values())},
                    maxLevel: {max_level},
                    uniqueFELines: 68
                }}
            }};
            
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {{type: 'application/json'}});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'voyager_bom_sankey_data.json';
            link.click();
        }}
        
        // Initial animation
        setTimeout(animateFlow, 1000);
    </script>
</body>
</html>
"""
    
    return html_content

def main():
    """Main function"""
    print("🌊 CREATING ADVANCED SANKEY VISUALIZATION")
    print("=" * 50)
    
    html_content = create_advanced_sankey_html()
    
    with open('output/voyager_bom_advanced_sankey.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ Advanced Sankey visualization created!")
    print("📁 File: output/voyager_bom_advanced_sankey.html")

if __name__ == "__main__":
    main()
