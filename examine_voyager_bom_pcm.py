#!/usr/bin/env python3
"""
Examine Voyager BOM and PCM files for reconstruction
"""

import pandas as pd
import os

def examine_voyager_bom():
    """Examine the Voyager BOM file (5408509)"""
    
    file_path = "input/5408509_41c3917a-ff9f-427d-9862-2379b6e85a35_1750170742103.xlsx"
    
    print("📊 EXAMINING VOYAGER BOM FILE")
    print("=" * 50)
    
    try:
        # Load Excel file and examine structure
        xls = pd.ExcelFile(file_path)
        print(f"📋 Available sheets: {xls.sheet_names}")
        
        # Examine second sheet (index 1)
        if len(xls.sheet_names) >= 2:
            print(f"\n📄 Examining second sheet: {xls.sheet_names[1]}")
            df = pd.read_excel(file_path, sheet_name=1)
            
            print(f"📊 Shape: {df.shape}")
            print(f"📋 Columns: {list(df.columns)}")
            
            # Show first few rows
            print(f"\n📋 First 10 rows:")
            print(df.head(10).to_string())
            
            # Show sample of first 4 columns
            if len(df.columns) >= 4:
                print(f"\n📋 First 4 columns sample (rows 0-20):")
                sample_df = df.iloc[:20, :4]
                print(sample_df.to_string())
            
            # Check for levels and descriptions
            print(f"\n🔍 ANALYZING CONTENT:")
            
            # Look for level patterns
            first_col = df.iloc[:, 0]
            print(f"First column unique values (first 20): {first_col.head(20).unique()}")
            
            # Look for item descriptions
            if len(df.columns) >= 4:
                desc_col = df.iloc[:, 3]
                print(f"\nDescription column sample:")
                print(desc_col.head(10).tolist())
            
            return df
        else:
            print("❌ No second sheet found")
            return None
            
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        import traceback
        traceback.print_exc()
        return None

def examine_voyager_pcm():
    """Examine the Voyager PCM files"""
    
    print("\n📊 EXAMINING VOYAGER PCM FILES")
    print("=" * 50)
    
    # Check for PDF and PowerPoint PCM files
    pcm_files = [
        "input/Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pdf",
        "input/Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pptx"
    ]
    
    for file_path in pcm_files:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
            file_size = os.path.getsize(file_path) / (1024*1024)  # MB
            print(f"   Size: {file_size:.2f} MB")
        else:
            print(f"❌ Not found: {file_path}")
    
    # Check if we have any extracted PCM data
    pcm_extracted_files = [
        "output/comprehensive_colpali_extraction.csv",
        "output/colpali_bom_integration.csv"
    ]
    
    print(f"\n📋 CHECKING EXTRACTED PCM DATA:")
    for file_path in pcm_extracted_files:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
            df = pd.read_csv(file_path)
            print(f"   Shape: {df.shape}")
            print(f"   Columns: {list(df.columns)}")
            
            if 'color_detected' in df.columns:
                color_dist = df['color_detected'].value_counts()
                print(f"   Color distribution: {dict(color_dist)}")
        else:
            print(f"❌ Not found: {file_path}")

def load_colpali_data():
    """Load the ColPali extracted data"""
    
    colpali_file = "output/comprehensive_colpali_extraction.csv"
    
    if os.path.exists(colpali_file):
        print(f"\n📊 LOADING COLPALI DATA")
        print("=" * 40)
        
        df = pd.read_csv(colpali_file)
        print(f"📊 Shape: {df.shape}")
        print(f"📋 Columns: {list(df.columns)}")
        
        # Show sample data
        print(f"\n📋 Sample data:")
        print(df.head(10).to_string())
        
        # Color distribution
        if 'color_detected' in df.columns:
            print(f"\n🎨 Color Distribution:")
            color_dist = df['color_detected'].value_counts()
            for color, count in color_dist.items():
                percentage = (count / len(df)) * 100
                print(f"  {color}: {count} ({percentage:.1f}%)")
        
        # Page distribution
        if 'page_number' in df.columns:
            print(f"\n📄 Page Distribution:")
            page_dist = df['page_number'].value_counts().sort_index()
            for page, count in page_dist.items():
                print(f"  Page {page}: {count} components")
        
        return df
    else:
        print(f"❌ ColPali data not found: {colpali_file}")
        return None

def main():
    """Main examination function"""
    
    print("🔍 VOYAGER BOM & PCM EXAMINATION")
    print("=" * 60)
    
    # 1. Examine BOM file
    bom_df = examine_voyager_bom()
    
    # 2. Examine PCM files
    examine_voyager_pcm()
    
    # 3. Load ColPali data if available
    colpali_df = load_colpali_data()
    
    print(f"\n📊 SUMMARY:")
    print(f"BOM Data: {'✅ Loaded' if bom_df is not None else '❌ Failed'}")
    print(f"ColPali Data: {'✅ Loaded' if colpali_df is not None else '❌ Failed'}")
    
    return bom_df, colpali_df

if __name__ == "__main__":
    bom_df, colpali_df = main()
