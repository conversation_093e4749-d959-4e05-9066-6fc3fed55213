# SentenceTransformer

## SentenceTransformer
```{eval-rst}
.. autoclass:: sentence_transformers.SentenceTransformer
   :members:
   :inherited-members: fit, old_fit
   :exclude-members: save, save_to_hub, add_module, append, apply, buffers, children, extra_repr, forward, get_buffer, get_extra_state, get_parameter, get_submodule, ipu, load_state_dict, modules, named_buffers, named_children, named_modules, named_parameters, parameters, register_backward_hook, register_buffer, register_forward_hook, register_forward_pre_hook, register_full_backward_hook, register_full_backward_pre_hook, register_load_state_dict_post_hook, register_module, register_parameter, register_state_dict_pre_hook, requires_grad_, set_extra_state, share_memory, state_dict, to_empty, type, xpu, zero_grad
```

## SentenceTransformerModelCardData
```{eval-rst}
.. autoclass:: sentence_transformers.model_card.SentenceTransformerModelCardData
```

## SimilarityFunction
```{eval-rst}
.. autoclass:: sentence_transformers.SimilarityFunction
   :members:
```