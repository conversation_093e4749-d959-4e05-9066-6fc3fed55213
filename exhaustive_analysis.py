#!/usr/bin/env python3
"""
Exhaustive Analysis of ColPali Extraction Results
"""

import pandas as pd
import numpy as np

def main():
    # Load the comprehensive extraction data
    df = pd.read_csv('output/comprehensive_colpali_extraction.csv')
    
    print('📊 EXHAUSTIVE RETRIEVAL ANALYSIS')
    print('=' * 50)
    
    print(f'Total Components Retrieved: {len(df)}')
    print(f'Pages Processed: {df["page_number"].nunique()}')
    print(f'Page Range: {df["page_number"].min()} to {df["page_number"].max()}')
    
    print('\n📄 COMPONENTS PER PAGE:')
    page_counts = df['page_number'].value_counts().sort_index()
    for page, count in page_counts.items():
        print(f'  Page {page}: {count} components')
    
    print('\n🎨 COLOR DISTRIBUTION:')
    color_counts = df['color_detected'].value_counts()
    for color, count in color_counts.items():
        print(f'  {color}: {count} components')
    
    print('\n🏷️ COMPONENT TYPE DISTRIBUTION:')
    type_counts = df['component_type'].value_counts()
    for comp_type, count in type_counts.items():
        print(f'  {comp_type}: {count} components')
    
    print('\n📍 SPATIAL DISTRIBUTION ANALYSIS:')
    print(f'X Position Range: {df["x_position"].min()} to {df["x_position"].max()}')
    print(f'Y Position Range: {df["y_position"].min()} to {df["y_position"].max()}')
    print(f'Area Range: {df["area"].min()} to {df["area"].max()}')
    
    print('\n🎯 CONFIDENCE SCORE ANALYSIS:')
    print(f'Average Confidence: {df["confidence_score"].mean():.3f}')
    print(f'Min Confidence: {df["confidence_score"].min():.3f}')
    print(f'Max Confidence: {df["confidence_score"].max():.3f}')
    
    print('\n📋 COMPONENT TEXT PATTERNS:')
    # Analyze component text patterns
    component_prefixes = df['component_text'].str.extract(r'^([A-Z]+)')[0].value_counts().head(10)
    print('Top Component Text Prefixes:')
    for prefix, count in component_prefixes.items():
        if pd.notna(prefix):
            print(f'  {prefix}: {count} components')
    
    print('\n🆔 PRODUCT ID PATTERNS:')
    # Analyze product ID patterns  
    id_prefixes = df['product_id'].str.extract(r'^([A-Z]+)')[0].value_counts().head(10)
    print('Top Product ID Prefixes:')
    for prefix, count in id_prefixes.items():
        if pd.notna(prefix):
            print(f'  {prefix}: {count} components')

if __name__ == "__main__":
    main()
