#!/usr/bin/env python3
"""
Analyse PCM Page par Page avec données ColPali existantes
De gauche à droite, ligne par ligne, regrouper éléments avec code et description
"""

import pandas as pd
import numpy as np
import re
from collections import defaultdict

class PCMPageAnalyzerColPali:
    def __init__(self):
        """Initialize avec données ColPali"""
        self.colpali_data = None
        self.page_elements = {}
        
    def load_colpali_data(self):
        """Charger les données ColPali extraites"""
        try:
            self.colpali_data = pd.read_csv('output/comprehensive_colpali_extraction.csv')
            print(f"✅ Données ColPali chargées: {len(self.colpali_data)} éléments")
            return True
        except Exception as e:
            print(f"❌ Erreur chargement ColPali: {e}")
            return False
    
    def analyze_page_elements(self, page_num):
        """Analyser les éléments d'une page de gauche à droite, ligne par ligne"""
        print(f"\n📄 ANALYSE PAGE {page_num}")
        print("=" * 50)
        
        # Filtrer les données pour cette page
        page_data = self.colpali_data[self.colpali_data['page_number'] == page_num].copy()
        
        if len(page_data) == 0:
            print(f"❌ Aucun élément trouvé pour la page {page_num}")
            return []
        
        print(f"📊 {len(page_data)} éléments bruts détectés")
        
        # Trier de gauche à droite, puis de haut en bas
        page_data = page_data.sort_values(['center_y', 'center_x'])
        
        # Regrouper les éléments qui paraissent comme un seul truc
        grouped_elements = self.group_related_elements(page_data)
        
        # Analyser chaque groupe pour identifier code et description
        analyzed_elements = []
        for group in grouped_elements:
            element = self.analyze_element_group(group, page_num)
            if element:
                analyzed_elements.append(element)
        
        print(f"✅ {len(analyzed_elements)} éléments regroupés identifiés")
        return analyzed_elements
    
    def group_related_elements(self, page_data):
        """Regrouper les éléments qui paraissent comme un seul truc"""
        groups = []
        used_indices = set()
        
        for i, row in page_data.iterrows():
            if i in used_indices:
                continue
            
            # Commencer un nouveau groupe avec cet élément
            current_group = [row]
            used_indices.add(i)
            
            # Chercher des éléments proches qui pourraient faire partie du même groupe
            for j, other_row in page_data.iterrows():
                if j in used_indices or i == j:
                    continue
                
                # Vérifier si les éléments sont proches (même zone)
                distance_x = abs(row['center_x'] - other_row['center_x'])
                distance_y = abs(row['center_y'] - other_row['center_y'])
                
                # Critères de regroupement
                if (distance_x < 200 and distance_y < 100) or \
                   (distance_x < 100 and distance_y < 200):
                    current_group.append(other_row)
                    used_indices.add(j)
            
            groups.append(current_group)
        
        return groups
    
    def analyze_element_group(self, group, page_num):
        """Analyser un groupe pour identifier code et description"""
        if not group:
            return None
        
        # Combiner les informations du groupe
        all_texts = []
        all_product_ids = []
        colors = []
        positions_x = []
        positions_y = []
        
        for element in group:
            if hasattr(element, 'component_text'):
                all_texts.append(element['component_text'])
            if hasattr(element, 'product_id'):
                all_product_ids.append(element['product_id'])
            if hasattr(element, 'color_detected'):
                colors.append(element['color_detected'])
            if hasattr(element, 'center_x'):
                positions_x.append(element['center_x'])
            if hasattr(element, 'center_y'):
                positions_y.append(element['center_y'])
        
        # Identifier le code principal
        main_code = None
        if all_product_ids:
            # Prendre le code le plus "complet" ou le premier
            main_code = max(all_product_ids, key=len) if all_product_ids else None
        
        # Identifier la description principale
        main_description = None
        if all_texts:
            # Prendre la description la plus longue ou combiner
            main_description = max(all_texts, key=len) if all_texts else None
        
        # Déterminer la couleur du cadrant
        cadrant_color = None
        if colors:
            # Prendre la couleur la plus fréquente
            color_counts = defaultdict(int)
            for color in colors:
                color_counts[color] += 1
            cadrant_color = max(color_counts.items(), key=lambda x: x[1])[0]
        
        # Position moyenne
        avg_x = sum(positions_x) / len(positions_x) if positions_x else 0
        avg_y = sum(positions_y) / len(positions_y) if positions_y else 0
        
        # Créer l'élément analysé
        element = {
            'page': page_num,
            'code': main_code,
            'description': main_description,
            'cadrant_color': cadrant_color,
            'position_x': int(avg_x),
            'position_y': int(avg_y),
            'group_size': len(group),
            'all_codes': list(set(all_product_ids)),
            'all_descriptions': list(set(all_texts)),
            'raw_elements': group
        }
        
        return element
    
    def print_page_analysis(self, page_elements, page_num):
        """Afficher l'analyse d'une page selon le processus demandé"""
        print(f"\n📋 ÉLÉMENTS IDENTIFIÉS - PAGE {page_num}")
        print("=" * 60)
        print("Processus: Gauche à droite, ligne par ligne")
        print("Regroupement: Éléments qui paraissent comme un seul truc")
        print("")
        
        if not page_elements:
            print("❌ Aucun élément identifié sur cette page")
            return
        
        # Trier par position (gauche à droite, haut en bas)
        sorted_elements = sorted(page_elements, key=lambda x: (x['position_y'], x['position_x']))
        
        for i, element in enumerate(sorted_elements, 1):
            cadrant = f"[{element['cadrant_color']}]" if element['cadrant_color'] else "[Sans couleur]"
            
            print(f"\n{i:2d}. ÉLÉMENT {cadrant}")
            print(f"    Code principal: {element['code'] or 'Non identifié'}")
            print(f"    Description: {element['description'] or 'Non identifiée'}")
            print(f"    Position: ({element['position_x']}, {element['position_y']})")
            print(f"    Taille groupe: {element['group_size']} élément(s)")
            
            if len(element['all_codes']) > 1:
                print(f"    Tous les codes: {', '.join(element['all_codes'])}")
            
            if len(element['all_descriptions']) > 1:
                print(f"    Toutes descriptions: {', '.join(element['all_descriptions'][:2])}...")
    
    def analyze_all_pages(self, max_pages=5):
        """Analyser toutes les pages selon le processus demandé"""
        print("🔍 ANALYSE PCM VOYAGER - PAGE PAR PAGE")
        print("=" * 70)
        print("Processus: Page par page, gauche à droite, ligne par ligne")
        print("Objectif: Regrouper éléments qui paraissent comme un seul truc")
        print("Identifier: Code et description dans chaque élément")
        print("Cadrants: Éléments dans cadrants colorés ou sans couleur")
        print("")
        
        if not self.load_colpali_data():
            return []
        
        # Obtenir la liste des pages disponibles
        available_pages = sorted(self.colpali_data['page_number'].unique())
        print(f"📄 Pages disponibles: {available_pages}")
        
        all_elements = []
        
        # Analyser chaque page (limiter pour commencer)
        for page_num in available_pages[:max_pages]:
            page_elements = self.analyze_page_elements(page_num)
            self.print_page_analysis(page_elements, page_num)
            all_elements.extend(page_elements)
            self.page_elements[page_num] = page_elements
        
        # Résumé global
        self.print_global_summary(all_elements)
        
        return all_elements
    
    def print_global_summary(self, all_elements):
        """Afficher le résumé global"""
        print(f"\n📊 RÉSUMÉ GLOBAL - PROCESSUS TERMINÉ")
        print("=" * 50)
        print(f"Total éléments regroupés: {len(all_elements)}")
        
        # Statistiques par couleur de cadrant
        color_stats = defaultdict(int)
        for elem in all_elements:
            color = elem['cadrant_color'] or 'Sans couleur'
            color_stats[color] += 1
        
        print(f"\nRépartition par couleur de cadrant:")
        for color, count in color_stats.items():
            print(f"  {color}: {count} éléments")
        
        # Statistiques par page
        page_stats = defaultdict(int)
        for elem in all_elements:
            page_stats[elem['page']] += 1
        
        print(f"\nRépartition par page:")
        for page, count in sorted(page_stats.items()):
            print(f"  Page {page}: {count} éléments")
        
        # Exemples de codes et descriptions trouvés
        print(f"\nExemples de codes identifiés:")
        codes = [elem['code'] for elem in all_elements if elem['code']][:10]
        for code in codes:
            print(f"  - {code}")
        
        print(f"\nExemples de descriptions identifiées:")
        descriptions = [elem['description'] for elem in all_elements if elem['description']][:5]
        for desc in descriptions:
            print(f"  - {desc[:50]}...")
    
    def get_descriptions_for_bom_similarity(self):
        """Obtenir les descriptions à utiliser pour la similarité avec la BOM"""
        print(f"\n🎯 DESCRIPTIONS POUR SIMILARITÉ BOM")
        print("=" * 50)
        
        descriptions = []
        for page_num, elements in self.page_elements.items():
            for element in elements:
                if element['description']:
                    descriptions.append({
                        'page': element['page'],
                        'code': element['code'],
                        'description': element['description'],
                        'cadrant_color': element['cadrant_color'],
                        'position': (element['position_x'], element['position_y'])
                    })
        
        print(f"📋 {len(descriptions)} descriptions extraites pour comparaison BOM")
        
        # Afficher quelques exemples
        print(f"\nExemples de descriptions à comparer avec la BOM:")
        for i, desc in enumerate(descriptions[:10], 1):
            color_info = f"[{desc['cadrant_color']}]" if desc['cadrant_color'] else "[Sans couleur]"
            print(f"{i:2d}. {color_info} {desc['code']} - {desc['description']}")
        
        return descriptions

def main():
    """Fonction principale"""
    print("🔍 ANALYSE PCM VOYAGER - PROCESSUS DEMANDÉ")
    print("=" * 70)
    
    analyzer = PCMPageAnalyzerColPali()
    elements = analyzer.analyze_all_pages(max_pages=5)  # Commencer par 5 pages
    
    # Obtenir les descriptions pour la similarité BOM
    descriptions = analyzer.get_descriptions_for_bom_similarity()
    
    print(f"\n✅ PROCESSUS TERMINÉ")
    print(f"📊 {len(elements)} éléments regroupés identifiés")
    print(f"📋 {len(descriptions)} descriptions prêtes pour comparaison BOM")
    print(f"\n🎯 PRÊT À DÉFINIR LES CRITÈRES DE SIMILARITÉ")
    
    return analyzer, elements, descriptions

if __name__ == "__main__":
    analyzer, elements, descriptions = main()
