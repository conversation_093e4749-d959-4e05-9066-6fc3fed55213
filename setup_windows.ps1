# BOM Analysis Environment Setup for Windows PowerShell
Write-Host "🔧 BOM Analysis Environment Setup for Windows" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# Check if Python is available
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install Python from https://python.org/downloads/" -ForegroundColor Yellow
    Write-Host "Make sure to check 'Add Python to PATH' during installation" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Create virtual environment
Write-Host "🔄 Creating virtual environment..." -ForegroundColor Yellow
if (Test-Path "bom_analysis_env") {
    Write-Host "📁 Virtual environment already exists" -ForegroundColor Blue
} else {
    python -m venv bom_analysis_env
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Virtual environment created successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create virtual environment" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""

# Activate virtual environment and install packages
Write-Host "🔄 Activating environment and installing packages..." -ForegroundColor Yellow

# Activate the virtual environment
& ".\bom_analysis_env\Scripts\Activate.ps1"

Write-Host "🔄 Upgrading pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip

Write-Host "🔄 Installing requirements..." -ForegroundColor Yellow
pip install -r requirements.txt

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install all requirements" -ForegroundColor Red
    Write-Host "Trying to install core packages individually..." -ForegroundColor Yellow
    
    $corePackages = @(
        "pandas", "numpy", "scikit-learn", "openpyxl", "matplotlib",
        "sentence-transformers", "transformers", "torch",
        "networkx", "seaborn", "plotly", "jupyter"
    )
    
    foreach ($package in $corePackages) {
        Write-Host "Installing $package..." -ForegroundColor Cyan
        pip install $package
    }
}

Write-Host ""
Write-Host "🔄 Setting up Jupyter kernel..." -ForegroundColor Yellow
python -m ipykernel install --user --name=bom-analysis --display-name="BOM Analysis"

Write-Host ""
Write-Host "🎉 Setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 To use the environment:" -ForegroundColor Cyan
Write-Host "1. Run: .\bom_analysis_env\Scripts\Activate.ps1" -ForegroundColor White
Write-Host "2. Then: python bom_similarity_analysis.py" -ForegroundColor White
Write-Host "3. Or: jupyter lab" -ForegroundColor White
Write-Host ""
Write-Host "📁 Make sure to place your Excel files in the 'input' folder" -ForegroundColor Yellow
Write-Host ""
Read-Host "Press Enter to exit"
