#!/usr/bin/env python3
"""
Quick check of cached BOM data to understand the structure
"""

import os
import pickle
import pandas as pd

def quick_check():
    print("🔍 Quick Data Structure Check")
    print("=" * 40)
    
    # Load cached BOM data
    cache_folder = 'cache'
    cache_data_file = os.path.join(cache_folder, 'bom_data.pkl')
    
    if not os.path.exists(cache_data_file):
        print("❌ No cached BOM data found")
        return
    
    try:
        with open(cache_data_file, 'rb') as f:
            bom_data = pickle.load(f)
        print(f"✅ Loaded cached BOM data: {len(bom_data)} files")
        
        # Check first file in detail
        first_file = list(bom_data.keys())[0]
        first_df = bom_data[first_file]
        
        print(f"\n📊 First file: {first_file}")
        print(f"Shape: {first_df.shape}")
        print(f"Columns: {list(first_df.columns)}")
        
        print(f"\nFirst 10 rows:")
        print(first_df.head(10).to_string())
        
        print(f"\nItem ID samples:")
        item_ids = first_df['Item ID'].astype(str).head(20)
        for i, item_id in enumerate(item_ids):
            print(f"  {i+1}. '{item_id}'")
        
        print(f"\nLevel samples:")
        levels = first_df['Level'].head(20)
        for i, level in enumerate(levels):
            print(f"  {i+1}. '{level}'")
        
        print(f"\nDescription samples:")
        descriptions = first_df['Item Description'].head(10)
        for i, desc in enumerate(descriptions):
            print(f"  {i+1}. '{desc}'")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_check()
