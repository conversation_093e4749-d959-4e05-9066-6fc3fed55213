@echo off
echo 🚀 Running BOM Similarity Analysis
echo ==================================
echo.

REM Try different Python commands
echo 🔍 Testing Python availability...

python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Using 'python' command
    set PYTHON_CMD=python
    goto :run
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Using 'py' launcher
    set PYTHON_CMD=py
    goto :run
)

echo ❌ Python not found! Please install Python or add it to PATH.
pause
exit /b 1

:run
echo.
echo 📦 Testing packages...
%PYTHON_CMD% test_packages.py

echo.
echo 🔍 Running BOM similarity analysis...
%PYTHON_CMD% bom_similarity_analysis.py

echo.
echo 🎉 Analysis complete! Check the output folder for results.
pause
