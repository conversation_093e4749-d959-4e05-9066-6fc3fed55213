import json
import pandas as pd
from collections import defaultdict

def load_tree_structure():
    """Load the tree structure from the BOM data"""
    
    # Load tree statistics
    with open('output/voyager_bom_tree_stats.json', 'r', encoding='utf-8') as f:
        stats = json.load(f)
    
    # Load tree sample for structure
    with open('output/voyager_bom_tree_sample.json', 'r', encoding='utf-8') as f:
        tree_sample = json.load(f)
    
    return stats, tree_sample

def create_tree_arborescence_html():
    """Create an interactive tree arborescence visualization"""
    
    stats, tree_sample = load_tree_structure()
    
    # Prepare hierarchical data for D3 tree
    def extract_tree_nodes(node, level=0, max_level=6):
        """Extract nodes for tree visualization with limited depth"""
        if level > max_level:
            return None
            
        tree_node = {
            'name': node.get('item_id', 'Unknown')[:20],
            'level': node.get('level', -1),
            'f_e_line': node.get('f_e_line', ''),
            'description': node.get('description', '')[:50],
            'full_name': f"{node.get('item_id', 'Unknown')} - {node.get('description', '')}",
            'children': []
        }
        
        # Add children (limit to avoid overcrowding)
        children = node.get('children', [])
        if children and level < max_level:
            # Limit children per node for better visualization
            max_children = 8 if level < 3 else 4
            for child in children[:max_children]:
                child_node = extract_tree_nodes(child, level + 1, max_level)
                if child_node:
                    tree_node['children'].append(child_node)
        
        return tree_node
    
    # Extract tree structure
    tree_data = extract_tree_nodes(tree_sample)
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voyager BOM - Tree Arborescence Visualization</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 3.5em;
            font-weight: 300;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .header p {{
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 1.3em;
        }}
        
        .controls {{
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 2px solid #dee2e6;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }}
        
        .control-button {{
            padding: 12px 25px;
            border: none;
            border-radius: 30px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }}
        
        .control-button:hover {{
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }}
        
        .visualization {{
            padding: 30px;
            min-height: 1000px;
            background: linear-gradient(45deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
        }}
        
        .tree-container {{
            width: 100%;
            height: 900px;
            border: 3px solid #dee2e6;
            border-radius: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: inset 0 4px 20px rgba(0,0,0,0.1);
            overflow: auto;
        }}
        
        .node circle {{
            fill: #fff;
            stroke: steelblue;
            stroke-width: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
        }}
        
        .node circle:hover {{
            stroke-width: 5px;
            filter: drop-shadow(0 0 10px steelblue);
            transform: scale(1.1);
        }}
        
        .node text {{
            font: 12px sans-serif;
            font-weight: bold;
            text-anchor: middle;
            pointer-events: none;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }}
        
        .link {{
            fill: none;
            stroke: #ccc;
            stroke-width: 2px;
            transition: all 0.3s ease;
        }}
        
        .link:hover {{
            stroke: steelblue;
            stroke-width: 4px;
            filter: drop-shadow(0 0 5px steelblue);
        }}
        
        .level-0 circle {{ fill: #e74c3c; stroke: #c0392b; }}
        .level-1 circle {{ fill: #9b59b6; stroke: #8e44ad; }}
        .level-2 circle {{ fill: #27ae60; stroke: #229954; }}
        .level-3 circle {{ fill: #f39c12; stroke: #e67e22; }}
        .level-4 circle {{ fill: #3498db; stroke: #2980b9; }}
        .level-5 circle {{ fill: #1abc9c; stroke: #16a085; }}
        .level-6 circle {{ fill: #34495e; stroke: #2c3e50; }}
        
        .tooltip {{
            position: absolute;
            background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(52,73,94,0.9) 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            font-size: 14px;
            pointer-events: none;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 1000;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: 2px solid rgba(255,255,255,0.1);
        }}
        
        .stats-panel {{
            margin-top: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }}
        
        .stat-card {{
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 6px solid #3498db;
            transition: all 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }}
        
        .stat-title {{
            font-size: 1.1em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }}
        
        .stat-value {{
            font-size: 2.5em;
            font-weight: bold;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .stat-description {{
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }}
        
        .legend {{
            margin-top: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            border-radius: 15px;
        }}
        
        .legend-title {{
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }}
        
        .legend-items {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }}
        
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        
        .legend-circle {{
            width: 25px;
            height: 25px;
            border-radius: 50%;
            border: 3px solid;
        }}
        
        .legend-text {{
            font-weight: bold;
            color: #2c3e50;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌳 VOYAGER BOM Tree Arborescence</h1>
            <p>Interactive Hierarchical Tree Structure Visualization</p>
        </div>
        
        <div class="controls">
            <button class="control-button" onclick="expandAll()">🌿 Expand All</button>
            <button class="control-button" onclick="collapseAll()">🌱 Collapse All</button>
            <button class="control-button" onclick="centerTree()">🎯 Center Tree</button>
            <button class="control-button" onclick="exportTree()">💾 Export Tree</button>
            <button class="control-button" onclick="animateTree()">🎬 Animate</button>
        </div>
        
        <div class="visualization">
            <div id="tree" class="tree-container"></div>
            
            <div class="legend">
                <div class="legend-title">🎨 Level Color Legend</div>
                <div class="legend-items">
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #e74c3c; border-color: #c0392b;"></div>
                        <div class="legend-text">Root - VOYAGER System</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #9b59b6; border-color: #8e44ad;"></div>
                        <div class="legend-text">Level 1 - Major Assemblies</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #27ae60; border-color: #229954;"></div>
                        <div class="legend-text">Level 2 - Sub-assemblies</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #f39c12; border-color: #e67e22;"></div>
                        <div class="legend-text">Level 3 - Components</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #3498db; border-color: #2980b9;"></div>
                        <div class="legend-text">Level 4 - Sub-components</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #1abc9c; border-color: #16a085;"></div>
                        <div class="legend-text">Level 5 - Parts</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-circle" style="background: #34495e; border-color: #2c3e50;"></div>
                        <div class="legend-text">Level 6+ - Detailed Parts</div>
                    </div>
                </div>
            </div>
            
            <div class="stats-panel">
                <div class="stat-card">
                    <div class="stat-title">📊 Total Components</div>
                    <div class="stat-value">{stats['total_nodes']:,}</div>
                    <div class="stat-description">Complete BOM structure</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🏗️ Maximum Depth</div>
                    <div class="stat-value">{stats['max_level']}</div>
                    <div class="stat-description">Deepest hierarchy level</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🌿 F-E-Lines</div>
                    <div class="stat-value">{stats['total_f_e_lines']}</div>
                    <div class="stat-description">Product line branches</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🌳 Tree Nodes Shown</div>
                    <div class="stat-value" id="visible-nodes">0</div>
                    <div class="stat-description">Currently visible in tree</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="tooltip" id="tooltip"></div>
    
    <script>
        // Tree data
        const treeData = {json.dumps(tree_data)};
        
        // Set up dimensions
        const margin = {{top: 40, right: 40, bottom: 40, left: 40}};
        const width = 1800 - margin.left - margin.right;
        const height = 820 - margin.top - margin.bottom;
        
        // Create SVG
        const svg = d3.select("#tree")
            .append("svg")
            .attr("width", width + margin.left + margin.right)
            .attr("height", height + margin.top + margin.bottom);
        
        const g = svg.append("g")
            .attr("transform", `translate(${{margin.left}},${{margin.top}})`);
        
        // Create tree layout with horizontal orientation
        const tree = d3.tree()
            .size([width - 200, height]);
        
        // Create hierarchy
        const root = d3.hierarchy(treeData);
        
        // Collapse nodes initially (show first 3 levels)
        function collapse(d) {{
            if (d.children) {{
                if (d.depth > 2) {{
                    d._children = d.children;
                    d._children.forEach(collapse);
                    d.children = null;
                }}
            }}
        }}
        
        root.children.forEach(collapse);
        
        let i = 0;
        const duration = 750;
        
        function update(source) {{
            // Compute the new tree layout
            const treeData = tree(root);
            const nodes = treeData.descendants();
            const links = treeData.descendants().slice(1);
            
            // Normalize for fixed-depth
            nodes.forEach(d => {{ d.y = d.depth * 250; }});
            
            // Update nodes
            const node = g.selectAll('g.node')
                .data(nodes, d => d.id || (d.id = ++i));
            
            // Enter new nodes
            const nodeEnter = node.enter().append('g')
                .attr('class', 'node')
                .attr('transform', d => `translate(${{source.y0}},${{source.x0}})`)
                .on('click', click)
                .on('mouseover', showTooltip)
                .on('mouseout', hideTooltip);
            
            nodeEnter.append('circle')
                .attr('class', d => `level-${{Math.min(d.data.level + 1, 6)}}`)
                .attr('r', 1e-6)
                .style('fill', d => d._children ? 'lightsteelblue' : '#fff');
            
            nodeEnter.append('text')
                .attr('dy', '.35em')
                .attr('x', d => d.children || d._children ? -13 : 13)
                .attr('text-anchor', d => d.children || d._children ? 'end' : 'start')
                .text(d => d.data.name)
                .style('fill-opacity', 1e-6);
            
            // Update existing nodes
            const nodeUpdate = nodeEnter.merge(node);
            
            nodeUpdate.transition()
                .duration(duration)
                .attr('transform', d => `translate(${{d.y}},${{d.x}})`);
            
            nodeUpdate.select('circle')
                .attr('r', 8)
                .style('fill', d => d._children ? 'lightsteelblue' : '#fff')
                .attr('cursor', 'pointer');
            
            nodeUpdate.select('text')
                .style('fill-opacity', 1);
            
            // Remove exiting nodes
            const nodeExit = node.exit().transition()
                .duration(duration)
                .attr('transform', d => `translate(${{source.y}},${{source.x}})`)
                .remove();
            
            nodeExit.select('circle')
                .attr('r', 1e-6);
            
            nodeExit.select('text')
                .style('fill-opacity', 1e-6);
            
            // Update links
            const link = g.selectAll('path.link')
                .data(links, d => d.id);
            
            // Enter new links
            const linkEnter = link.enter().insert('path', 'g')
                .attr('class', 'link')
                .attr('d', d => {{
                    const o = {{x: source.x0, y: source.y0}};
                    return diagonal(o, o);
                }});
            
            // Update existing links
            const linkUpdate = linkEnter.merge(link);
            
            linkUpdate.transition()
                .duration(duration)
                .attr('d', d => diagonal(d, d.parent));
            
            // Remove exiting links
            link.exit().transition()
                .duration(duration)
                .attr('d', d => {{
                    const o = {{x: source.x, y: source.y}};
                    return diagonal(o, o);
                }})
                .remove();
            
            // Store old positions for transition
            nodes.forEach(d => {{
                d.x0 = d.x;
                d.y0 = d.y;
            }});
            
            // Update visible nodes count
            document.getElementById('visible-nodes').textContent = nodes.length;
        }}
        
        // Creates a curved (diagonal) path from parent to child nodes
        function diagonal(s, d) {{
            const path = `M ${{s.y}} ${{s.x}}
                         C ${{(s.y + d.y) / 2}} ${{s.x}},
                           ${{(s.y + d.y) / 2}} ${{d.x}},
                           ${{d.y}} ${{d.x}}`;
            return path;
        }}
        
        // Toggle children on click
        function click(event, d) {{
            if (d.children) {{
                d._children = d.children;
                d.children = null;
            }} else {{
                d.children = d._children;
                d._children = null;
            }}
            update(d);
        }}
        
        // Tooltip functions
        const tooltip = d3.select("#tooltip");
        
        function showTooltip(event, d) {{
            tooltip.style("opacity", 1)
                .html(`
                    <strong>${{d.data.full_name}}</strong><br>
                    <strong>Level:</strong> ${{d.data.level}}<br>
                    <strong>F-E-Line:</strong> ${{d.data.f_e_line}}<br>
                    <strong>Children:</strong> ${{(d.children || d._children || []).length}}<br>
                    <strong>Depth:</strong> ${{d.depth}}
                `)
                .style("left", (event.pageX + 15) + "px")
                .style("top", (event.pageY - 15) + "px");
        }}
        
        function hideTooltip() {{
            tooltip.style("opacity", 0);
        }}
        
        // Control functions
        function expandAll() {{
            function expand(d) {{
                if (d._children) {{
                    d.children = d._children;
                    d._children = null;
                }}
                if (d.children) {{
                    d.children.forEach(expand);
                }}
            }}
            expand(root);
            update(root);
        }}
        
        function collapseAll() {{
            function collapse(d) {{
                if (d.children) {{
                    d._children = d.children;
                    d._children.forEach(collapse);
                    d.children = null;
                }}
            }}
            root.children.forEach(collapse);
            update(root);
        }}
        
        function centerTree() {{
            const bounds = g.node().getBBox();
            const parent = g.node().parentElement;
            const fullWidth = parent.clientWidth || parent.parentNode.clientWidth;
            const fullHeight = parent.clientHeight || parent.parentNode.clientHeight;
            const width = bounds.width;
            const height = bounds.height;
            const midX = bounds.x + width / 2;
            const midY = bounds.y + height / 2;
            
            if (width == 0 || height == 0) return;
            
            const scale = 0.8 / Math.max(width / fullWidth, height / fullHeight);
            const translate = [fullWidth / 2 - scale * midX, fullHeight / 2 - scale * midY];
            
            svg.transition()
                .duration(750)
                .call(d3.zoom().transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale));
        }}
        
        function animateTree() {{
            const nodes = g.selectAll('.node');
            nodes.transition()
                .duration(2000)
                .style('opacity', 0.3)
                .transition()
                .duration(2000)
                .style('opacity', 1);
        }}
        
        function exportTree() {{
            const exportData = {{
                treeStructure: treeData,
                statistics: {{
                    totalNodes: {stats['total_nodes']},
                    maxLevel: {stats['max_level']},
                    totalFELines: {stats['total_f_e_lines']}
                }},
                metadata: {{
                    created: new Date().toISOString(),
                    source: "Voyager BOM Tree Arborescence"
                }}
            }};
            
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {{type: 'application/json'}});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'voyager_bom_tree_arborescence.json';
            link.click();
        }}
        
        // Add zoom behavior
        const zoom = d3.zoom()
            .scaleExtent([0.1, 3])
            .on('zoom', function(event) {{
                g.attr('transform', event.transform);
            }});
        
        svg.call(zoom);
        
        // Initialize tree
        root.x0 = height / 2;
        root.y0 = 0;
        
        update(root);
        
        // Center tree after initial render
        setTimeout(centerTree, 1000);
    </script>
</body>
</html>
"""
    
    return html_content

def main():
    """Main function"""
    print("🌳 CREATING TREE ARBORESCENCE VISUALIZATION")
    print("=" * 50)
    
    html_content = create_tree_arborescence_html()
    
    with open('output/voyager_bom_tree_arborescence.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ Tree arborescence visualization created!")
    print("📁 File: output/voyager_bom_tree_arborescence.html")
    print("🌳 Features:")
    print("  - Interactive tree structure with expand/collapse")
    print("  - Color-coded levels matching your requirements")
    print("  - Hierarchical connections between nodes")
    print("  - Zoom and pan functionality")
    print("  - Detailed tooltips with component information")

if __name__ == "__main__":
    main()
