[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "bom-cluster-analysis"
version = "1.0.0"
description = "Bill of Materials (BOM) similarity analysis and clustering tool"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "BOM Analysis Team"},
]
keywords = ["bom", "clustering", "similarity", "analysis", "manufacturing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Manufacturing",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Information Analysis",
]

dependencies = [
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "scipy>=1.10.0",
    "openpyxl>=3.1.0",
    "xlrd>=2.0.1",
    "sentence-transformers>=2.2.0",
    "transformers>=4.30.0",
    "torch>=2.0.0",
    "networkx>=3.1",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.15.0",
    "tqdm>=4.65.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.4.0",
    "jupyter>=1.0.0",
    "jupyterlab>=4.0.0",
]
ml = [
    "umap-learn>=0.5.3",
    "hdbscan>=0.8.29",
    "faiss-cpu>=1.7.4",
    "spacy>=3.6.0",
    "nltk>=3.8.0",
]
viz = [
    "graphviz>=0.20.0",
    "streamlit>=1.25.0",
    "dash>=2.11.0",
]
all = [
    "bom-cluster-analysis[dev,ml,viz]",
]

[project.urls]
Homepage = "https://github.com/your-org/bom-cluster-analysis"
Documentation = "https://github.com/your-org/bom-cluster-analysis/wiki"
Repository = "https://github.com/your-org/bom-cluster-analysis.git"
"Bug Tracker" = "https://github.com/your-org/bom-cluster-analysis/issues"

[project.scripts]
bom-analyze = "bom_similarity_analysis:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["bom_*", "scripts.*"]
exclude = ["tests*", "docs*"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    "venv",
]
