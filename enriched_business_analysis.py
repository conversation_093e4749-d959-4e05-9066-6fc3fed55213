#!/usr/bin/env python3
"""
Enriched Business Analysis - Supply Chain, Packaging, Pricing & Sales Integration
Applies economic laws to identify business-relevant product families
"""

import os
import pandas as pd
import numpy as np
import json
from sklearn.cluster import KMeans, AgglomerativeClustering
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

class EconomicLawsAnalyzer:
    """Apply economic laws and principles to BOM analysis"""
    
    def __init__(self):
        self.pareto_threshold = 0.8  # 80/20 rule
        self.zipf_alpha = 1.0  # Zipf distribution parameter
        
    def apply_pareto_analysis(self, df, value_column):
        """Apply Pareto 80/20 rule to identify critical items"""
        df_sorted = df.sort_values(value_column, ascending=False)
        df_sorted['cumulative_pct'] = df_sorted[value_column].cumsum() / df_sorted[value_column].sum()
        df_sorted['pareto_class'] = df_sorted['cumulative_pct'].apply(
            lambda x: 'A' if x <= 0.8 else 'B' if x <= 0.95 else 'C'
        )
        return df_sorted
    
    def calculate_economies_of_scale(self, df, volume_col, price_col):
        """Calculate economies of scale potential"""
        df['unit_cost_efficiency'] = df[price_col] / (df[volume_col] + 1)  # +1 to avoid division by 0
        df['scale_potential'] = 1 / (1 + df['unit_cost_efficiency'])  # Higher volume = higher potential
        return df
    
    def apply_abc_analysis(self, df, criteria_columns, weights=None):
        """Multi-criteria ABC analysis"""
        if weights is None:
            weights = [1/len(criteria_columns)] * len(criteria_columns)
        
        # Normalize each criterion
        normalized_scores = []
        for col, weight in zip(criteria_columns, weights):
            normalized = (df[col] - df[col].min()) / (df[col].max() - df[col].min() + 1e-8)
            normalized_scores.append(normalized * weight)
        
        df['abc_score'] = sum(normalized_scores)
        df['abc_class'] = pd.cut(df['abc_score'], bins=3, labels=['C', 'B', 'A'])
        return df
    
    def calculate_network_effects(self, df, supplier_count_col):
        """Calculate network effects value (Metcalfe's law approximation)"""
        df['network_value'] = df[supplier_count_col] ** 1.5  # Modified Metcalfe's law
        return df

def load_enriched_data():
    """Load BOM data and simulate additional business data"""
    print("Loading enriched business data...")
    
    # Load existing BOM analysis results
    output_folder = 'output'
    
    # Load similarity data
    similarity_files = [f for f in os.listdir(output_folder) if 'similarity' in f and f.endswith('.xlsx')]
    similarity_data = None
    
    for sim_file in similarity_files:
        try:
            file_path = os.path.join(output_folder, sim_file)
            similarity_data = pd.read_excel(file_path, engine='openpyxl')
            break
        except:
            continue
    
    if similarity_data is None:
        print("Warning: No similarity data found, creating sample data")
        return create_sample_enriched_data()
    
    # Check actual column names in similarity data
    print(f"Similarity data columns: {list(similarity_data.columns)}")

    # Try to find the right column names
    possible_id_cols = [col for col in similarity_data.columns if 'ID' in col or 'Item' in col]
    possible_desc_cols = [col for col in similarity_data.columns if 'Description' in col or 'Desc' in col]
    possible_file_cols = [col for col in similarity_data.columns if 'File' in col or 'Source' in col]

    print(f"Possible ID columns: {possible_id_cols}")
    print(f"Possible description columns: {possible_desc_cols}")
    print(f"Possible file columns: {possible_file_cols}")

    # Extract unique items using available columns
    if len(possible_id_cols) >= 2 and len(possible_desc_cols) >= 2:
        items_1 = similarity_data[[possible_id_cols[0], possible_desc_cols[0]]].rename(columns={
            possible_id_cols[0]: 'Item_ID', possible_desc_cols[0]: 'Item_Description'
        })
        items_2 = similarity_data[[possible_id_cols[1], possible_desc_cols[1]]].rename(columns={
            possible_id_cols[1]: 'Item_ID', possible_desc_cols[1]: 'Item_Description'
        })

        # Add source file if available
        if possible_file_cols:
            items_1['Source_File'] = similarity_data[possible_file_cols[0]] if len(possible_file_cols) > 0 else 'Unknown'
            items_2['Source_File'] = similarity_data[possible_file_cols[1]] if len(possible_file_cols) > 1 else 'Unknown'
        else:
            items_1['Source_File'] = 'Unknown'
            items_2['Source_File'] = 'Unknown'

        unique_items = pd.concat([items_1, items_2]).drop_duplicates(subset=['Item_ID'])
    else:
        print("Could not find expected columns, using first few columns")
        # Fallback: use first few columns
        cols = list(similarity_data.columns)
        unique_items = pd.DataFrame({
            'Item_ID': similarity_data[cols[0]] if len(cols) > 0 else range(len(similarity_data)),
            'Item_Description': similarity_data[cols[1]] if len(cols) > 1 else 'Unknown',
            'Source_File': similarity_data[cols[2]] if len(cols) > 2 else 'Unknown'
        }).drop_duplicates(subset=['Item_ID'])
    
    print(f"Found {len(unique_items)} unique items from similarity analysis")
    
    # Enrich with simulated business data
    enriched_data = enrich_with_business_data(unique_items)
    
    return enriched_data, similarity_data

def create_sample_enriched_data():
    """Create sample data if no similarity data available"""
    print("Creating sample enriched data...")
    
    np.random.seed(42)
    n_items = 1000
    
    sample_data = pd.DataFrame({
        'Item_ID': [f'ITEM_{i:04d}' for i in range(n_items)],
        'Item_Description': [f'Component Type {i%20} Variant {i//20}' for i in range(n_items)],
        'Source_File': [f'BOM_File_{i%10}.xlsx' for i in range(n_items)]
    })
    
    enriched_data = enrich_with_business_data(sample_data)
    return enriched_data, None

def enrich_with_business_data(base_data):
    """Enrich base data with simulated supply chain, packaging, pricing, and sales data"""
    print("Enriching with business dimensions...")
    
    np.random.seed(42)
    n_items = len(base_data)
    
    # Supply Chain Data
    suppliers = ['Supplier_A', 'Supplier_B', 'Supplier_C', 'Supplier_D', 'Supplier_E']
    countries = ['Germany', 'China', 'USA', 'Japan', 'France', 'Italy']
    
    enriched = base_data.copy()
    
    # Supply Chain Metrics
    enriched['Primary_Supplier'] = np.random.choice(suppliers, n_items)
    enriched['Supplier_Count'] = np.random.poisson(2, n_items) + 1  # 1-6 suppliers
    enriched['Lead_Time_Days'] = np.random.gamma(2, 10)  # Gamma distribution for lead times
    enriched['Supplier_Country'] = np.random.choice(countries, n_items)
    enriched['Supply_Risk_Score'] = np.random.beta(2, 5) * 10  # 0-10 risk score
    
    # Packaging & Logistics
    enriched['Weight_kg'] = np.random.lognormal(0, 1)  # Log-normal distribution
    enriched['Volume_cm3'] = np.random.lognormal(3, 1.5)
    enriched['Packaging_Type'] = np.random.choice(['Box', 'Bag', 'Tube', 'Pallet'], n_items)
    enriched['Transport_Mode'] = np.random.choice(['Air', 'Sea', 'Road', 'Rail'], n_items)
    enriched['Storage_Temp'] = np.random.choice(['Ambient', 'Cold', 'Frozen'], n_items)
    
    # Pricing Data
    base_price = np.random.lognormal(2, 1.5)  # Base price distribution
    enriched['Unit_Price_EUR'] = base_price
    enriched['Price_Volatility'] = np.random.beta(2, 8)  # 0-1 volatility score
    enriched['Negotiation_Potential'] = np.random.beta(3, 7)  # 0-1 potential
    enriched['Total_Cost_Ownership'] = enriched['Unit_Price_EUR'] * (1 + enriched['Supply_Risk_Score']/20)
    
    # Sales & Demand Data
    enriched['Annual_Volume'] = np.random.pareto(1) * 1000  # Pareto distribution for volumes
    enriched['Revenue_EUR'] = enriched['Annual_Volume'] * enriched['Unit_Price_EUR']
    enriched['Demand_Seasonality'] = np.random.beta(2, 2)  # 0-1 seasonality
    enriched['Growth_Rate'] = np.random.normal(0.05, 0.15)  # 5% average growth, 15% std
    enriched['Product_Lifecycle'] = np.random.choice(['Introduction', 'Growth', 'Maturity', 'Decline'], n_items)
    
    # Calculate derived metrics
    enriched['Margin_EUR'] = enriched['Revenue_EUR'] * np.random.beta(2, 8)  # Margin distribution
    enriched['Inventory_Turns'] = 365 / (enriched['Lead_Time_Days'] + np.random.gamma(2, 5))
    enriched['Logistics_Cost_EUR'] = enriched['Weight_kg'] * 0.5 + enriched['Volume_cm3'] * 0.001
    
    print(f"Enriched {len(enriched)} items with business data")
    return enriched

def apply_economic_laws_analysis(enriched_data):
    """Apply economic laws to identify business patterns"""
    print("Applying economic laws analysis...")
    
    analyzer = EconomicLawsAnalyzer()
    
    # 1. Pareto Analysis on Revenue
    enriched_data = analyzer.apply_pareto_analysis(enriched_data, 'Revenue_EUR')
    
    # 2. Economies of Scale Analysis
    enriched_data = analyzer.calculate_economies_of_scale(
        enriched_data, 'Annual_Volume', 'Unit_Price_EUR'
    )
    
    # 3. ABC Analysis (Multi-criteria)
    abc_criteria = ['Revenue_EUR', 'Annual_Volume', 'Supply_Risk_Score', 'Margin_EUR']
    abc_weights = [0.4, 0.3, 0.2, 0.1]  # Revenue and volume weighted higher
    enriched_data = analyzer.apply_abc_analysis(enriched_data, abc_criteria, abc_weights)
    
    # 4. Network Effects Analysis
    enriched_data = analyzer.calculate_network_effects(enriched_data, 'Supplier_Count')
    
    # 5. Supply Chain Efficiency Score
    enriched_data['supply_chain_efficiency'] = (
        (1 / (enriched_data['Lead_Time_Days'] + 1)) * 
        enriched_data['Inventory_Turns'] * 
        (1 / (enriched_data['Supply_Risk_Score'] + 1))
    )
    
    # 6. Business Value Score (Combined metric)
    enriched_data['business_value_score'] = (
        enriched_data['abc_score'] * 0.3 +
        enriched_data['scale_potential'] * 0.2 +
        enriched_data['network_value'] / enriched_data['network_value'].max() * 0.2 +
        enriched_data['supply_chain_efficiency'] / enriched_data['supply_chain_efficiency'].max() * 0.3
    )
    
    print("Economic laws analysis complete")
    return enriched_data

def create_business_relevant_clusters(enriched_data):
    """Create clusters based on business relevance using economic principles"""
    print("Creating business-relevant clusters...")
    
    # Select features for clustering based on business impact
    clustering_features = [
        'Unit_Price_EUR', 'Annual_Volume', 'Revenue_EUR', 'Margin_EUR',
        'Lead_Time_Days', 'Supply_Risk_Score', 'Supplier_Count',
        'Weight_kg', 'Volume_cm3', 'Logistics_Cost_EUR',
        'business_value_score', 'supply_chain_efficiency', 'scale_potential'
    ]
    
    # Prepare data for clustering
    X = enriched_data[clustering_features].fillna(0)
    
    # Handle infinite values
    X = X.replace([np.inf, -np.inf], 0)
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Apply multiple clustering algorithms
    clustering_results = {}
    
    # 1. K-Means for business segments
    n_clusters = min(8, len(enriched_data) // 50)  # Adaptive cluster count
    if n_clusters >= 2:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        enriched_data['business_cluster'] = kmeans.fit_predict(X_scaled)
        clustering_results['kmeans'] = kmeans
    
    # 2. Hierarchical clustering for supply chain groups
    if len(enriched_data) < 1000:  # Only for manageable sizes
        hierarchical = AgglomerativeClustering(n_clusters=min(6, n_clusters))
        enriched_data['supply_chain_cluster'] = hierarchical.fit_predict(X_scaled)
        clustering_results['hierarchical'] = hierarchical
    
    print(f"Created {n_clusters} business clusters")
    return enriched_data, clustering_results, X_scaled

def analyze_cluster_characteristics(enriched_data):
    """Analyze characteristics of each business cluster"""
    print("Analyzing cluster characteristics...")
    
    cluster_analysis = {}
    
    for cluster_id in enriched_data['business_cluster'].unique():
        cluster_data = enriched_data[enriched_data['business_cluster'] == cluster_id]
        
        characteristics = {
            'cluster_id': int(cluster_id),
            'item_count': len(cluster_data),
            'total_revenue': float(cluster_data['Revenue_EUR'].sum()),
            'avg_unit_price': float(cluster_data['Unit_Price_EUR'].mean()),
            'avg_volume': float(cluster_data['Annual_Volume'].mean()),
            'avg_margin': float(cluster_data['Margin_EUR'].mean()),
            'avg_lead_time': float(cluster_data['Lead_Time_Days'].mean()),
            'avg_risk_score': float(cluster_data['Supply_Risk_Score'].mean()),
            'avg_supplier_count': float(cluster_data['Supplier_Count'].mean()),
            'pareto_distribution': dict(cluster_data['pareto_class'].value_counts()),
            'abc_distribution': dict(cluster_data['abc_class'].value_counts()),
            'dominant_suppliers': list(cluster_data['Primary_Supplier'].value_counts().head(3).index),
            'dominant_countries': list(cluster_data['Supplier_Country'].value_counts().head(3).index),
            'lifecycle_stages': dict(cluster_data['Product_Lifecycle'].value_counts())
        }
        
        # Classify cluster type based on economic characteristics
        if characteristics['avg_unit_price'] > enriched_data['Unit_Price_EUR'].quantile(0.8):
            cluster_type = "Premium Components"
        elif characteristics['avg_volume'] > enriched_data['Annual_Volume'].quantile(0.8):
            cluster_type = "High Volume Commodities"
        elif characteristics['avg_risk_score'] > enriched_data['Supply_Risk_Score'].quantile(0.8):
            cluster_type = "High Risk Items"
        elif characteristics['avg_supplier_count'] > enriched_data['Supplier_Count'].quantile(0.8):
            cluster_type = "Multi-Sourced Items"
        elif characteristics['avg_lead_time'] > enriched_data['Lead_Time_Days'].quantile(0.8):
            cluster_type = "Long Lead Time Items"
        else:
            cluster_type = "Standard Components"
        
        characteristics['cluster_type'] = cluster_type
        characteristics['business_priority'] = calculate_business_priority(characteristics)
        
        cluster_analysis[f"Cluster_{cluster_id}"] = characteristics
    
    print(f"Analyzed {len(cluster_analysis)} business clusters")
    return cluster_analysis

def calculate_business_priority(characteristics):
    """Calculate business priority based on economic impact"""
    revenue_weight = 0.4
    volume_weight = 0.2
    risk_weight = 0.2
    margin_weight = 0.2
    
    # Normalize scores (simplified)
    revenue_score = min(characteristics['total_revenue'] / 1000000, 1)  # Cap at 1M
    volume_score = min(characteristics['avg_volume'] / 10000, 1)  # Cap at 10K
    risk_score = characteristics['avg_risk_score'] / 10  # 0-1 scale
    margin_score = min(characteristics['avg_margin'] / 100000, 1)  # Cap at 100K
    
    priority_score = (
        revenue_score * revenue_weight +
        volume_score * volume_weight +
        (1 - risk_score) * risk_weight +  # Lower risk = higher priority
        margin_score * margin_weight
    )
    
    if priority_score > 0.7:
        return "High"
    elif priority_score > 0.4:
        return "Medium"
    else:
        return "Low"

def create_business_visualizations(enriched_data, cluster_analysis, output_folder):
    """Create business-focused visualizations"""
    print("Creating business visualizations...")
    
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    
    # 1. Revenue vs Volume by Cluster
    scatter = axes[0,0].scatter(enriched_data['Annual_Volume'], enriched_data['Revenue_EUR'], 
                               c=enriched_data['business_cluster'], alpha=0.6, cmap='tab10')
    axes[0,0].set_xlabel('Annual Volume')
    axes[0,0].set_ylabel('Revenue (EUR)')
    axes[0,0].set_title('Revenue vs Volume by Business Cluster')
    axes[0,0].set_xscale('log')
    axes[0,0].set_yscale('log')
    
    # 2. Pareto Analysis
    pareto_counts = enriched_data['pareto_class'].value_counts()
    axes[0,1].pie(pareto_counts.values, labels=pareto_counts.index, autopct='%1.1f%%')
    axes[0,1].set_title('Pareto Distribution (80/20 Rule)')
    
    # 3. Supply Chain Risk vs Lead Time
    scatter2 = axes[0,2].scatter(enriched_data['Lead_Time_Days'], enriched_data['Supply_Risk_Score'],
                                c=enriched_data['business_cluster'], alpha=0.6, cmap='tab10')
    axes[0,2].set_xlabel('Lead Time (Days)')
    axes[0,2].set_ylabel('Supply Risk Score')
    axes[0,2].set_title('Supply Chain Risk Profile')
    
    # 4. Business Value Score Distribution
    axes[1,0].hist(enriched_data['business_value_score'], bins=20, alpha=0.7)
    axes[1,0].set_xlabel('Business Value Score')
    axes[1,0].set_ylabel('Frequency')
    axes[1,0].set_title('Business Value Score Distribution')
    
    # 5. Cluster Revenue Contribution
    cluster_revenue = enriched_data.groupby('business_cluster')['Revenue_EUR'].sum()
    axes[1,1].bar(range(len(cluster_revenue)), cluster_revenue.values)
    axes[1,1].set_xlabel('Business Cluster')
    axes[1,1].set_ylabel('Total Revenue (EUR)')
    axes[1,1].set_title('Revenue Contribution by Cluster')
    
    # 6. ABC Analysis
    abc_counts = enriched_data['abc_class'].value_counts()
    axes[1,2].bar(abc_counts.index, abc_counts.values)
    axes[1,2].set_xlabel('ABC Class')
    axes[1,2].set_ylabel('Item Count')
    axes[1,2].set_title('ABC Analysis Distribution')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder, 'enriched_business_analysis.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Business visualizations saved")

def save_enriched_results(enriched_data, cluster_analysis, output_folder):
    """Save enriched analysis results"""
    print("Saving enriched analysis results...")
    
    # Save enriched data
    enriched_data.to_excel(os.path.join(output_folder, 'enriched_business_data.xlsx'), index=False)
    
    # Save cluster analysis
    cluster_df = pd.DataFrame([data for data in cluster_analysis.values()])
    cluster_df.to_excel(os.path.join(output_folder, 'business_cluster_analysis.xlsx'), index=False)
    
    # Save business recommendations
    with open(os.path.join(output_folder, 'business_recommendations.txt'), 'w', encoding='utf-8') as f:
        f.write("ENRICHED BUSINESS ANALYSIS - RECOMMENDATIONS\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("ECONOMIC LAWS APPLIED:\n")
        f.write("- Pareto Analysis (80/20 rule) for prioritization\n")
        f.write("- Economies of Scale for volume optimization\n")
        f.write("- ABC Analysis for multi-criteria classification\n")
        f.write("- Network Effects for supplier diversification\n")
        f.write("- Supply Chain Efficiency optimization\n\n")
        
        f.write("BUSINESS CLUSTER RECOMMENDATIONS:\n")
        f.write("=" * 40 + "\n")
        
        for cluster_name, data in cluster_analysis.items():
            f.write(f"\n{cluster_name} - {data['cluster_type']}\n")
            f.write(f"Priority: {data['business_priority']}\n")
            f.write(f"Items: {data['item_count']}\n")
            f.write(f"Total Revenue: {data['total_revenue']:,.0f} EUR\n")
            f.write(f"Avg Unit Price: {data['avg_unit_price']:.2f} EUR\n")
            f.write(f"Avg Lead Time: {data['avg_lead_time']:.1f} days\n")
            f.write(f"Risk Score: {data['avg_risk_score']:.1f}/10\n")
            
            # Business recommendations based on cluster type
            if data['cluster_type'] == "Premium Components":
                f.write("Recommendation: Focus on quality and reliability\n")
            elif data['cluster_type'] == "High Volume Commodities":
                f.write("Recommendation: Optimize for cost and economies of scale\n")
            elif data['cluster_type'] == "High Risk Items":
                f.write("Recommendation: Diversify suppliers and reduce risk\n")
            elif data['cluster_type'] == "Multi-Sourced Items":
                f.write("Recommendation: Leverage competition for better pricing\n")
            elif data['cluster_type'] == "Long Lead Time Items":
                f.write("Recommendation: Improve forecasting and inventory management\n")
            else:
                f.write("Recommendation: Standard procurement practices\n")
    
    print("Enriched results saved")

def main():
    print("Starting Enriched Business Analysis")
    print("=" * 60)
    print("Integrating Supply Chain, Packaging, Pricing & Sales Data")
    print("Applying Economic Laws for Business-Relevant Product Families")
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Load and enrich data
    enriched_data, similarity_data = load_enriched_data()
    
    # Apply economic laws analysis
    enriched_data = apply_economic_laws_analysis(enriched_data)
    
    # Create business-relevant clusters
    enriched_data, clustering_results, X_scaled = create_business_relevant_clusters(enriched_data)
    
    # Analyze cluster characteristics
    cluster_analysis = analyze_cluster_characteristics(enriched_data)
    
    # Create visualizations
    create_business_visualizations(enriched_data, cluster_analysis, output_folder)
    
    # Save results
    save_enriched_results(enriched_data, cluster_analysis, output_folder)
    
    print(f"\nEnriched Business Analysis Complete!")
    print("=" * 60)
    print(f"Items analyzed: {len(enriched_data)}")
    print(f"Business clusters: {len(cluster_analysis)}")
    print(f"Economic laws applied: Pareto, Economies of Scale, ABC, Network Effects")
    print(f"\nFiles created:")
    print(f"- enriched_business_data.xlsx")
    print(f"- business_cluster_analysis.xlsx") 
    print(f"- business_recommendations.txt")
    print(f"- enriched_business_analysis.png")

if __name__ == "__main__":
    main()
