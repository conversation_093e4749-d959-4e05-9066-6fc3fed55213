import pandas as pd
import numpy as np
from anytree import Node, RenderTree
from anytree.exporter import DictExporter
import json

def load_voyager_bom():
    """Load the Voyager BOM data from the Excel file"""
    file_path = "input/5408509_41c3917a-ff9f-427d-9862-2379b6e85a35_1750170742103.xlsx"
    
    try:
        # Read the second sheet "Lines"
        df = pd.read_excel(file_path, sheet_name="Lines")
        print(f"✅ Loaded BOM data: {len(df)} rows")
        print(f"📋 Columns: {list(df.columns)}")
        
        # Display first few rows to understand structure
        print("\n🔍 First 10 rows:")
        print(df.head(10))
        
        return df
    except Exception as e:
        print(f"❌ Error loading BOM: {e}")
        return None

def parse_f_e_line(f_e_line_str):
    """Parse F-E-Line string to extract components"""
    if pd.isna(f_e_line_str):
        return None
    
    # Convert to string and split by '-'
    parts = str(f_e_line_str).split('-')
    return parts

def create_bom_tree(df):
    """Create hierarchical tree structure from BOM data"""
    
    # Create root node (Voyager product)
    root = Node("VOYAGER_ROOT", 
                item_id="VOYAGER", 
                description="SIGNA Voyager 1.5T MRI System",
                level=-1,
                f_e_line="ROOT",
                row_index=-1)
    
    # Stack to keep track of parent nodes at each level
    parent_stack = [root]  # Start with root
    current_f_e_line = None
    
    print("🌳 Building BOM Tree Structure...")
    
    for idx, row in df.iterrows():
        try:
            # Extract data
            f_e_line = str(row.iloc[0]) if not pd.isna(row.iloc[0]) else None
            level = int(row.iloc[1]) if not pd.isna(row.iloc[1]) else 0
            item_id = str(row.iloc[2]) if not pd.isna(row.iloc[2]) else f"ITEM_{idx}"
            description = str(row.iloc[3]) if not pd.isna(row.iloc[3]) else "No Description"
            
            # Skip empty rows
            if pd.isna(row.iloc[0]) and pd.isna(row.iloc[1]):
                continue
                
            # Create node name
            node_name = f"{item_id}_{idx}"
            
            # Determine parent based on hierarchy rules
            parent_node = None
            
            # Rule 1: F-E-Line change = new son of main product (Voyager)
            if current_f_e_line is not None and f_e_line != current_f_e_line:
                print(f"📍 F-E-Line change: {current_f_e_line} → {f_e_line}")
                parent_node = root
                parent_stack = [root]  # Reset stack
                current_f_e_line = f_e_line
            
            # Rule 2: Level 0 = direct child of Voyager
            elif level == 0:
                parent_node = root
                parent_stack = [root]
                current_f_e_line = f_e_line
            
            # Rule 3: Level 1 with same F-E-Line = father/parent
            elif level == 1:
                parent_node = root
                parent_stack = [root]  # Reset for new level 1
                current_f_e_line = f_e_line
            
            # Rule 4: Level increases by 1 = child of previous item
            elif len(parent_stack) > 0:
                # Adjust parent stack based on level
                target_level = level - 1  # Parent should be at level-1
                
                # Trim stack to appropriate level
                while len(parent_stack) > target_level + 2:  # +2 because root is at -1
                    parent_stack.pop()
                
                if len(parent_stack) > 0:
                    parent_node = parent_stack[-1]
                else:
                    parent_node = root
            
            else:
                parent_node = root
            
            # Create the node
            node = Node(node_name,
                       parent=parent_node,
                       item_id=item_id,
                       description=description,
                       level=level,
                       f_e_line=f_e_line,
                       row_index=idx)
            
            # Update parent stack
            if level >= len(parent_stack) - 1:  # -1 because root is at position 0
                parent_stack.append(node)
            else:
                # Replace at appropriate level
                parent_stack = parent_stack[:level + 1] + [node]
            
            # Progress indicator
            if idx % 1000 == 0:
                print(f"📊 Processed {idx} items...")
                
        except Exception as e:
            print(f"⚠️ Error processing row {idx}: {e}")
            continue
    
    print(f"✅ Tree creation completed! Total nodes: {len(list(root.descendants)) + 1}")
    return root

def analyze_tree_structure(root):
    """Analyze the tree structure and provide statistics"""
    
    all_nodes = [root] + list(root.descendants)
    
    # Count by level
    level_counts = {}
    f_e_line_counts = {}
    
    for node in all_nodes:
        level = getattr(node, 'level', -1)
        f_e_line = getattr(node, 'f_e_line', 'ROOT')
        
        level_counts[level] = level_counts.get(level, 0) + 1
        f_e_line_counts[f_e_line] = f_e_line_counts.get(f_e_line, 0) + 1
    
    print("\n📊 TREE STRUCTURE ANALYSIS:")
    print("=" * 50)
    
    print("\n🔢 Distribution by Level:")
    for level in sorted(level_counts.keys()):
        print(f"  Level {level}: {level_counts[level]} items")
    
    print(f"\n🌿 Total F-E-Lines: {len(f_e_line_counts)}")
    print("\n📋 Top 10 F-E-Lines by item count:")
    sorted_f_e_lines = sorted(f_e_line_counts.items(), key=lambda x: x[1], reverse=True)
    for f_e_line, count in sorted_f_e_lines[:10]:
        print(f"  {f_e_line}: {count} items")
    
    return level_counts, f_e_line_counts

def export_tree_sample(root, max_depth=3, max_children=5):
    """Export a sample of the tree structure for visualization"""
    
    def render_node_sample(node, current_depth=0, max_depth=3, max_children=5):
        if current_depth > max_depth:
            return None
            
        children = list(node.children)[:max_children]
        
        result = {
            'name': node.name,
            'item_id': getattr(node, 'item_id', ''),
            'description': getattr(node, 'description', '')[:50] + "..." if len(getattr(node, 'description', '')) > 50 else getattr(node, 'description', ''),
            'level': getattr(node, 'level', -1),
            'f_e_line': getattr(node, 'f_e_line', ''),
            'children_count': len(node.children),
            'children': []
        }
        
        for child in children:
            child_data = render_node_sample(child, current_depth + 1, max_depth, max_children)
            if child_data:
                result['children'].append(child_data)
        
        if len(node.children) > max_children:
            result['children'].append({
                'name': f"... and {len(node.children) - max_children} more children",
                'item_id': '...',
                'description': 'Additional children not shown',
                'level': -999,
                'f_e_line': '...',
                'children_count': 0,
                'children': []
            })
        
        return result
    
    return render_node_sample(root, 0, max_depth, max_children)

def save_tree_results(root, level_counts, f_e_line_counts):
    """Save tree analysis results to files"""

    # 1. Save tree sample as JSON
    tree_sample = export_tree_sample(root, max_depth=4, max_children=10)

    with open('output/voyager_bom_tree_sample.json', 'w', encoding='utf-8') as f:
        json.dump(tree_sample, f, indent=2, ensure_ascii=False)

    # 2. Save tree statistics
    stats = {
        'total_nodes': len(list(root.descendants)) + 1,
        'level_distribution': level_counts,
        'f_e_line_distribution': dict(sorted(f_e_line_counts.items(), key=lambda x: x[1], reverse=True)),
        'max_level': max(level_counts.keys()) if level_counts else -1,
        'total_f_e_lines': len(f_e_line_counts)
    }

    with open('output/voyager_bom_tree_stats.json', 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)

    # 3. Create detailed tree structure as text
    with open('output/voyager_bom_tree_structure.txt', 'w', encoding='utf-8') as f:
        f.write("VOYAGER BOM HIERARCHICAL TREE STRUCTURE\n")
        f.write("=" * 60 + "\n\n")

        # Render first few levels of the tree
        for pre, fill, node in RenderTree(root):
            if getattr(node, 'level', -1) <= 5:  # Show only first 5 levels
                item_id = getattr(node, 'item_id', '')
                description = getattr(node, 'description', '')[:40]
                level = getattr(node, 'level', -1)
                f_e_line = getattr(node, 'f_e_line', '')

                f.write(f"{pre}{item_id} | L{level} | {f_e_line} | {description}\n")

                # Limit output size
                if len(list(root.descendants)) > 1000 and getattr(node, 'row_index', 0) > 500:
                    f.write(f"{pre}... (truncated for readability)\n")
                    break

    print("\n💾 SAVED FILES:")
    print("  📄 voyager_bom_tree_sample.json - Tree structure sample")
    print("  📊 voyager_bom_tree_stats.json - Tree statistics")
    print("  📋 voyager_bom_tree_structure.txt - Detailed tree structure")

def main():
    """Main function to create Voyager BOM tree"""

    print("🚀 CREATING VOYAGER BOM HIERARCHICAL TREE")
    print("=" * 60)

    # Load BOM data
    df = load_voyager_bom()
    if df is None:
        return

    # Create tree structure
    root = create_bom_tree(df)

    # Analyze tree
    level_counts, f_e_line_counts = analyze_tree_structure(root)

    # Save results
    save_tree_results(root, level_counts, f_e_line_counts)

    print("\n🎉 VOYAGER BOM TREE CREATION COMPLETED!")
    print("\nTree follows your hierarchy rules:")
    print("  🌳 Level 0/1 + same F-E-Line = Parent")
    print("  👶 Level +1 = Child of previous")
    print("  🌿 Level decrease = New branch")
    print("  🔄 F-E-Line change = New son of Voyager")

if __name__ == "__main__":
    main()
