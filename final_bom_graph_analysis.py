#!/usr/bin/env python3
"""
Final BOM Graph Analysis
Based on actual F-E-Line patterns: 1-1-X format
Creates proper hierarchical graphs with File Root → 1 → 1-1 → 1-1-X structure
"""

import os
import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from collections import defaultdict
import json
import re

def parse_actual_fe_line(level_str):
    """Parse actual F-E-Line format like '1-1-26' into components"""
    try:
        level_str = str(level_str).strip()
        parts = level_str.split('-')
        
        # Convert to integers
        numeric_parts = []
        for part in parts:
            try:
                numeric_parts.append(int(part))
            except ValueError:
                # Extract first number if non-numeric
                match = re.search(r'(\d+)', part)
                if match:
                    numeric_parts.append(int(match.group(1)))
                else:
                    numeric_parts.append(0)
        
        return numeric_parts
    except:
        return [0]

def create_hierarchy_path(fe_line_parts):
    """Create the full hierarchy path for a F-E-Line"""
    if not fe_line_parts:
        return []
    
    # For 1-1-X format, create: [1] → [1,1] → [1,1,X]
    hierarchy = []
    for i in range(1, len(fe_line_parts) + 1):
        hierarchy.append(fe_line_parts[:i])
    
    return hierarchy

def load_bom_file_for_final_graph(file_path):
    """Load BOM file with proper F-E-Line interpretation"""
    try:
        xls = pd.ExcelFile(file_path)
        if len(xls.sheet_names) < 2:
            return None
            
        df = pd.read_excel(file_path, sheet_name=1)
        
        if len(df.columns) < 4:
            return None
        
        # Column mapping: F-E-Line (Level), Sequence, Item_ID, Description
        df = df.iloc[:, :4]
        df.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
        
        # Clean data
        df = df.dropna(subset=['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description'])
        df = df[df['F_E_Line'].astype(str).str.strip() != '']
        df = df[df['Item_ID'].astype(str).str.strip() != '']
        df = df[df['Item_Description'].astype(str).str.strip() != '']
        
        # Remove numeric-only Item IDs
        df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
        
        # Parse F-E-Line
        df['F_E_Line_Parts'] = df['F_E_Line'].apply(parse_actual_fe_line)
        df['Hierarchy_Depth'] = df['F_E_Line_Parts'].apply(len)
        
        # Add row index
        df['Row_Index'] = range(len(df))
        
        return df
        
    except Exception as e:
        print(f"Error loading file: {e}")
        return None

def create_final_bom_graph_structure(df, file_name):
    """Create final graph structure based on actual F-E-Line patterns"""
    print(f"🔍 Creating final graph structure for: {file_name}")
    
    file_graph = {
        'file_name': file_name,
        'file_root': f"FILE_ROOT",
        'total_items': len(df),
        'nodes': {},
        'edges': [],
        'hierarchy_levels': defaultdict(list)
    }
    
    # Add file root node (grand father)
    file_root = file_graph['file_root']
    file_graph['nodes'][file_root] = {
        'id': file_root,
        'hierarchy_depth': 0,
        'fe_line': 'FILE_ROOT',
        'fe_line_parts': [],
        'description': f"File: {file_name[:30]}...",
        'item_type': 'file_root',
        'row_index': -1
    }
    file_graph['hierarchy_levels'][0].append(file_root)
    
    # Track created intermediate nodes
    intermediate_nodes = {}
    
    # Process each item
    for idx, (_, row) in enumerate(df.iterrows()):
        fe_line = str(row['F_E_Line'])
        fe_line_parts = row['F_E_Line_Parts']
        hierarchy_depth = row['Hierarchy_Depth']
        
        # Create the full hierarchy path
        hierarchy_path = create_hierarchy_path(fe_line_parts)
        
        # Create intermediate nodes if needed
        for level, path_parts in enumerate(hierarchy_path[:-1], 1):
            path_key = '-'.join(map(str, path_parts))
            
            if path_key not in intermediate_nodes:
                intermediate_node_id = f"LEVEL_{level}_{path_key}"
                
                file_graph['nodes'][intermediate_node_id] = {
                    'id': intermediate_node_id,
                    'hierarchy_depth': level,
                    'fe_line': path_key,
                    'fe_line_parts': path_parts,
                    'description': f"Level {level}: {path_key}",
                    'item_type': 'intermediate',
                    'row_index': -1
                }
                
                intermediate_nodes[path_key] = intermediate_node_id
                file_graph['hierarchy_levels'][level].append(intermediate_node_id)
                
                # Connect to parent
                if level == 1:
                    parent_id = file_root
                else:
                    parent_path = '-'.join(map(str, path_parts[:-1]))
                    parent_id = intermediate_nodes.get(parent_path, file_root)
                
                file_graph['edges'].append({
                    'parent': parent_id,
                    'child': intermediate_node_id,
                    'parent_depth': file_graph['nodes'][parent_id]['hierarchy_depth'],
                    'child_depth': level
                })
        
        # Create leaf node (actual item)
        leaf_node_id = f"ITEM_{idx}_{fe_line}"
        
        file_graph['nodes'][leaf_node_id] = {
            'id': leaf_node_id,
            'hierarchy_depth': hierarchy_depth,
            'fe_line': fe_line,
            'fe_line_parts': fe_line_parts,
            'sequence': str(row['Sequence']),
            'item_id': str(row['Item_ID']),
            'description': str(row['Item_Description'])[:40],
            'full_description': str(row['Item_Description']),
            'item_type': 'leaf_item',
            'row_index': row['Row_Index']
        }
        
        file_graph['hierarchy_levels'][hierarchy_depth].append(leaf_node_id)
        
        # Connect to parent (last intermediate node or file root)
        if len(hierarchy_path) > 1:
            parent_path = '-'.join(map(str, hierarchy_path[-2]))
            parent_id = intermediate_nodes.get(parent_path, file_root)
        else:
            parent_id = file_root
        
        file_graph['edges'].append({
            'parent': parent_id,
            'child': leaf_node_id,
            'parent_depth': file_graph['nodes'][parent_id]['hierarchy_depth'],
            'child_depth': hierarchy_depth
        })
    
    print(f"  ✅ Created {len(file_graph['nodes'])} nodes and {len(file_graph['edges'])} edges")
    print(f"  📊 Hierarchy levels: {dict([(k, len(v)) for k, v in file_graph['hierarchy_levels'].items()])}")
    
    return file_graph

def create_final_networkx_graph(file_graph):
    """Convert to NetworkX graph"""
    G = nx.DiGraph()
    
    # Add all nodes
    for node_id, node_data in file_graph['nodes'].items():
        G.add_node(node_id,
                   hierarchy_depth=node_data['hierarchy_depth'],
                   node_type=node_data['item_type'],
                   label=node_data.get('description', ''),
                   fe_line=node_data.get('fe_line', ''),
                   item_id=node_data.get('item_id', ''))
    
    # Add all edges
    for edge in file_graph['edges']:
        G.add_edge(edge['parent'], edge['child'])
    
    return G

def visualize_final_bom_graph(G, file_name, output_folder):
    """Create final visualization"""
    print(f"📊 Creating final visualization for: {file_name}")
    
    plt.figure(figsize=(20, 16))
    
    # Create layout
    pos = create_final_hierarchical_layout(G)
    
    # Define colors and sizes
    node_colors = []
    node_sizes = []
    
    for node in G.nodes():
        node_type = G.nodes[node].get('node_type', 'leaf_item')
        hierarchy_depth = G.nodes[node].get('hierarchy_depth', 0)
        
        if node_type == 'file_root':
            node_colors.append('red')
            node_sizes.append(800)
        elif node_type == 'intermediate':
            if hierarchy_depth == 1:
                node_colors.append('orange')
                node_sizes.append(400)
            elif hierarchy_depth == 2:
                node_colors.append('yellow')
                node_sizes.append(300)
            else:
                node_colors.append('lightcoral')
                node_sizes.append(200)
        else:  # leaf_item
            node_colors.append('lightblue')
            node_sizes.append(50)
    
    # Draw graph
    nx.draw(G, pos, 
            node_color=node_colors,
            node_size=node_sizes,
            with_labels=False,
            arrows=True,
            arrowsize=10,
            edge_color='gray',
            alpha=0.7,
            arrowstyle='->')
    
    # Add labels for important nodes only
    important_nodes = {}
    for node in G.nodes():
        node_type = G.nodes[node].get('node_type', 'leaf_item')
        if node_type in ['file_root', 'intermediate']:
            label = G.nodes[node].get('label', '')[:15]
            important_nodes[node] = label
    
    nx.draw_networkx_labels(G, pos, important_nodes, font_size=8, font_weight='bold')
    
    # Legend
    legend_elements = [
        mpatches.Patch(color='red', label='File Root'),
        mpatches.Patch(color='orange', label='Level 1 (e.g., "1")'),
        mpatches.Patch(color='yellow', label='Level 2 (e.g., "1-1")'),
        mpatches.Patch(color='lightcoral', label='Level 3+ (e.g., "1-1-1")'),
        mpatches.Patch(color='lightblue', label='Leaf Items (Components)')
    ]
    
    plt.legend(handles=legend_elements, loc='upper right', fontsize=10)
    plt.title(f'Final BOM Hierarchical Graph: {file_name}', fontsize=16, fontweight='bold')
    
    # Save
    safe_filename = re.sub(r'[^\w\-_\.]', '_', file_name)
    viz_file = os.path.join(output_folder, f'final_bom_graph_{safe_filename}.png')
    plt.savefig(viz_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Final visualization saved: {viz_file}")
    return viz_file

def create_final_hierarchical_layout(G):
    """Create hierarchical layout"""
    pos = {}
    
    # Group by hierarchy depth
    levels = defaultdict(list)
    for node in G.nodes():
        depth = G.nodes[node].get('hierarchy_depth', 0)
        levels[depth].append(node)
    
    # Position nodes
    y_spacing = 2.0
    
    for depth, nodes in levels.items():
        y = -depth * y_spacing
        
        if len(nodes) == 1:
            x_positions = [0]
        else:
            # Spread nodes horizontally
            x_spacing = min(20.0 / len(nodes), 2.0)  # Adaptive spacing
            total_width = (len(nodes) - 1) * x_spacing
            x_positions = [i * x_spacing - total_width/2 for i in range(len(nodes))]
        
        for i, node in enumerate(nodes):
            pos[node] = (x_positions[i], y)
    
    return pos

def main():
    print("🚀 Starting Final BOM Graph Analysis")
    print("=" * 60)
    print("🎯 Based on actual F-E-Line patterns: 1-1-X format")
    print("📊 Creating proper hierarchical graphs")
    
    input_folder = 'input'
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    print(f"📁 Found {len(excel_files)} Excel files")
    
    # Process first 3 files
    for i, file in enumerate(excel_files[:3], 1):
        print(f"\n[{i}/3] Processing: {file}")
        file_path = os.path.join(input_folder, file)
        
        # Load data
        df = load_bom_file_for_final_graph(file_path)
        if df is None:
            print(f"❌ Failed to load {file}")
            continue
        
        print(f"✅ Loaded {len(df)} items")
        
        # Show F-E-Line patterns
        unique_patterns = df['F_E_Line'].unique()
        print(f"📊 Unique F-E-Line patterns: {len(unique_patterns)}")
        print(f"📋 Sample patterns: {list(unique_patterns[:10])}")
        
        # Create graph
        file_graph = create_final_bom_graph_structure(df, file)
        
        # Convert to NetworkX
        G = create_final_networkx_graph(file_graph)
        print(f"📊 Final graph: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
        
        # Visualize
        visualize_final_bom_graph(G, file, output_folder)
        
        # Save data
        safe_filename = re.sub(r'[^\w\-_\.]', '_', file)
        json_file = os.path.join(output_folder, f'final_bom_graph_data_{safe_filename}.json')
        
        json_data = {
            'file_name': file_graph['file_name'],
            'total_items': file_graph['total_items'],
            'total_nodes': len(file_graph['nodes']),
            'total_edges': len(file_graph['edges']),
            'hierarchy_levels': {str(k): len(v) for k, v in file_graph['hierarchy_levels'].items()}
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2)
        
        print(f"✅ Graph data saved: {json_file}")
    
    print(f"\n🎉 Final BOM Graph Analysis Complete!")
    print("=" * 60)
    print(f"📁 Files created in: {os.path.abspath(output_folder)}")
    print(f"   • final_bom_graph_*.png - Hierarchical visualizations")
    print(f"   • final_bom_graph_data_*.json - Graph structure data")

if __name__ == "__main__":
    main()
