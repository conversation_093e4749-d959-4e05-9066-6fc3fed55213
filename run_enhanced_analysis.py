#!/usr/bin/env python3
"""
Enhanced BOM Similarity Analysis Runner
Runs the analysis with proper error handling and package checking
"""

import sys
import os

def check_packages():
    """Check if required packages are available"""
    required_packages = {
        'pandas': 'pandas',
        'numpy': 'numpy', 
        'sklearn': 'scikit-learn',
        'openpyxl': 'openpyxl'
    }
    
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} available")
        except ImportError:
            missing_packages.append(pip_name)
            print(f"❌ {package} missing")
    
    return missing_packages

def install_packages(packages):
    """Install missing packages"""
    if not packages:
        return True
        
    print(f"\n📦 Installing missing packages: {', '.join(packages)}")
    
    import subprocess
    
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    return True

def main():
    print("🔧 Enhanced BOM Similarity Analysis")
    print("=" * 40)
    
    # Check if input folder exists
    if not os.path.exists('input'):
        print("❌ 'input' folder not found!")
        print("Please create an 'input' folder and add your Excel files.")
        return
    
    # Check for Excel files
    excel_files = [f for f in os.listdir('input') if f.endswith(('.xlsx', '.xls'))]
    if not excel_files:
        print("❌ No Excel files found in 'input' folder!")
        print("Please add .xlsx or .xls files to the 'input' folder.")
        return
    
    print(f"📊 Found {len(excel_files)} Excel files")
    
    # Check packages
    print("\n🔍 Checking required packages...")
    missing = check_packages()
    
    if missing:
        print(f"\n⚠️  Missing packages: {', '.join(missing)}")
        response = input("Install missing packages? (y/n): ").lower().strip()
        
        if response == 'y':
            if not install_packages(missing):
                print("❌ Failed to install packages. Please install manually:")
                for package in missing:
                    print(f"  pip install {package}")
                return
        else:
            print("❌ Cannot proceed without required packages.")
            return
    
    # Run the analysis
    print("\n🚀 Starting enhanced BOM similarity analysis...")
    print("📋 Analysis features:")
    print("  • Only considers rows with complete data in all 4 columns")
    print("  • Enhanced TF-IDF with bigrams and stop word removal")
    print("  • Multiple similarity thresholds (Exact >95%, High 85-95%, Medium 70-85%)")
    print("  • Detailed reporting with all BOM information")
    print("  • Excel output with summary and detailed match sheets")
    
    try:
        # Import and run the main analysis
        from bom_similarity_analysis import main as run_analysis
        run_analysis()
        
    except Exception as e:
        print(f"❌ Error running analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
