#!/usr/bin/env python3
"""
Product Family Clustering Analysis
Combines F-E-Line graph structures + similarity data to create product family clusters.
Creates dimensional modeling for product family identification.
"""

import os
import pandas as pd
import numpy as np
import json
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt
from collections import defaultdict, Counter
import networkx as nx

def load_graph_and_similarity_data():
    """Load both graph structure data and similarity data"""
    print("🔍 Loading graph structures and similarity data...")
    
    output_folder = 'output'
    
    # Load graph data
    graph_files = [f for f in os.listdir(output_folder) if f.startswith('fe_line_graph_data_') and f.endswith('.json')]
    graph_data = {}
    
    for graph_file in graph_files:
        file_path = os.path.join(output_folder, graph_file)
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            graph_data[data['file_name']] = data
    
    print(f"✅ Loaded {len(graph_data)} graph structure files")
    
    # Load similarity data
    similarity_files = [f for f in os.listdir(output_folder) if 'similarity' in f and f.endswith('.xlsx')]
    similarity_data = None
    
    for sim_file in similarity_files:
        try:
            file_path = os.path.join(output_folder, sim_file)
            similarity_data = pd.read_excel(file_path, engine='openpyxl')
            print(f"✅ Loaded similarity data: {len(similarity_data)} pairs")
            break
        except:
            continue
    
    if similarity_data is None:
        print("⚠️  No similarity data found, will create basic clustering")
    
    return graph_data, similarity_data

def extract_component_features(graph_data):
    """Extract features for each component from graph structures"""
    print("🔍 Extracting component features from graph structures...")
    
    all_components = []
    
    for file_name, data in graph_data.items():
        fe_line_summary = data.get('fe_line_summary', {})
        
        for fe_line, fe_data in fe_line_summary.items():
            item_count = fe_data.get('item_count', 0)
            level_progression = fe_data.get('level_progression', [])
            
            # Calculate F-E-Line characteristics
            max_level = max(level_progression) if level_progression else 0
            min_level = min(level_progression) if level_progression else 0
            level_range = max_level - min_level
            level_complexity = len(set(level_progression)) if level_progression else 0
            level_transitions = len([i for i in range(1, len(level_progression)) 
                                   if level_progression[i] != level_progression[i-1]]) if len(level_progression) > 1 else 0
            
            # Count level returns (when level decreases)
            level_returns = len([i for i in range(1, len(level_progression)) 
                               if level_progression[i] < level_progression[i-1]]) if len(level_progression) > 1 else 0
            
            component_features = {
                'file_name': file_name,
                'fe_line': fe_line,
                'item_count': item_count,
                'max_level': max_level,
                'min_level': min_level,
                'level_range': level_range,
                'level_complexity': level_complexity,
                'level_transitions': level_transitions,
                'level_returns': level_returns,
                'avg_level': np.mean(level_progression) if level_progression else 0,
                'level_std': np.std(level_progression) if level_progression else 0,
                'level_progression_length': len(level_progression)
            }
            
            all_components.append(component_features)
    
    components_df = pd.DataFrame(all_components)
    print(f"✅ Extracted features for {len(components_df)} F-E-Line components")
    
    return components_df

def create_similarity_features(similarity_data, components_df):
    """Create similarity-based features for components"""
    print("🔍 Creating similarity-based features...")
    
    if similarity_data is None:
        print("⚠️  No similarity data available, using basic features only")
        return components_df
    
    # Create similarity network
    similarity_network = defaultdict(list)
    
    for _, row in similarity_data.iterrows():
        item1 = row.get('Item_ID_1', '')
        item2 = row.get('Item_ID_2', '')
        similarity_score = row.get('Similarity_Score', 0)
        
        similarity_network[item1].append((item2, similarity_score))
        similarity_network[item2].append((item1, similarity_score))
    
    # Add similarity features to components
    components_df['similarity_connections'] = 0
    components_df['avg_similarity_score'] = 0.0
    components_df['max_similarity_score'] = 0.0
    
    # Note: This is a simplified approach since we don't have direct item-to-F-E-Line mapping
    # In a real implementation, you'd need to map items to their F-E-Lines
    
    print(f"✅ Added similarity features (simplified)")
    return components_df

def perform_clustering_analysis(components_df):
    """Perform multiple clustering algorithms on component features"""
    print("🔍 Performing clustering analysis...")
    
    # Prepare features for clustering
    feature_columns = [
        'item_count', 'max_level', 'min_level', 'level_range', 
        'level_complexity', 'level_transitions', 'level_returns',
        'avg_level', 'level_std', 'level_progression_length'
    ]
    
    X = components_df[feature_columns].fillna(0)
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    clustering_results = {}
    
    # 1. K-Means Clustering
    print("  📊 K-Means clustering...")
    n_clusters_kmeans = min(8, len(components_df) // 3)  # Adaptive number of clusters
    if n_clusters_kmeans >= 2:
        kmeans = KMeans(n_clusters=n_clusters_kmeans, random_state=42, n_init=10)
        components_df['cluster_kmeans'] = kmeans.fit_predict(X_scaled)
        clustering_results['kmeans'] = {
            'model': kmeans,
            'n_clusters': n_clusters_kmeans,
            'labels': components_df['cluster_kmeans'].values
        }
    
    # 2. Hierarchical Clustering
    print("  📊 Hierarchical clustering...")
    n_clusters_hier = min(6, len(components_df) // 4)
    if n_clusters_hier >= 2:
        hierarchical = AgglomerativeClustering(n_clusters=n_clusters_hier)
        components_df['cluster_hierarchical'] = hierarchical.fit_predict(X_scaled)
        clustering_results['hierarchical'] = {
            'model': hierarchical,
            'n_clusters': n_clusters_hier,
            'labels': components_df['cluster_hierarchical'].values
        }
    
    # 3. DBSCAN Clustering
    print("  📊 DBSCAN clustering...")
    dbscan = DBSCAN(eps=0.5, min_samples=2)
    components_df['cluster_dbscan'] = dbscan.fit_predict(X_scaled)
    n_clusters_dbscan = len(set(components_df['cluster_dbscan'])) - (1 if -1 in components_df['cluster_dbscan'] else 0)
    clustering_results['dbscan'] = {
        'model': dbscan,
        'n_clusters': n_clusters_dbscan,
        'labels': components_df['cluster_dbscan'].values
    }
    
    print(f"✅ Clustering complete:")
    print(f"    K-Means: {clustering_results.get('kmeans', {}).get('n_clusters', 0)} clusters")
    print(f"    Hierarchical: {clustering_results.get('hierarchical', {}).get('n_clusters', 0)} clusters")
    print(f"    DBSCAN: {clustering_results['dbscan']['n_clusters']} clusters")
    
    return components_df, clustering_results, X_scaled, scaler

def create_dimensional_modeling(components_df, X_scaled):
    """Create dimensional models for visualization"""
    print("🔍 Creating dimensional modeling...")
    
    dimensional_models = {}
    
    # 1. PCA (Principal Component Analysis)
    print("  📊 PCA analysis...")
    pca = PCA(n_components=min(3, X_scaled.shape[1]))
    X_pca = pca.fit_transform(X_scaled)
    
    components_df['pca_1'] = X_pca[:, 0]
    components_df['pca_2'] = X_pca[:, 1]
    if X_pca.shape[1] > 2:
        components_df['pca_3'] = X_pca[:, 2]
    
    dimensional_models['pca'] = {
        'model': pca,
        'coordinates': X_pca,
        'explained_variance': pca.explained_variance_ratio_
    }
    
    # 2. t-SNE (for non-linear dimensionality reduction)
    if len(components_df) > 3:
        print("  📊 t-SNE analysis...")
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(components_df)-1))
        X_tsne = tsne.fit_transform(X_scaled)
        
        components_df['tsne_1'] = X_tsne[:, 0]
        components_df['tsne_2'] = X_tsne[:, 1]
        
        dimensional_models['tsne'] = {
            'model': tsne,
            'coordinates': X_tsne
        }
    
    print(f"✅ Dimensional modeling complete")
    return components_df, dimensional_models

def identify_product_families(components_df, clustering_results):
    """Identify product families based on clustering results"""
    print("🔍 Identifying product families...")
    
    product_families = {}
    
    # Use K-Means results as primary clustering
    if 'kmeans' in clustering_results:
        cluster_column = 'cluster_kmeans'
    elif 'hierarchical' in clustering_results:
        cluster_column = 'cluster_hierarchical'
    else:
        cluster_column = 'cluster_dbscan'
    
    for cluster_id in components_df[cluster_column].unique():
        if cluster_id == -1:  # Skip noise points in DBSCAN
            continue
            
        cluster_components = components_df[components_df[cluster_column] == cluster_id]
        
        # Analyze cluster characteristics
        family_characteristics = {
            'cluster_id': cluster_id,
            'component_count': len(cluster_components),
            'files_involved': cluster_components['file_name'].nunique(),
            'avg_item_count': cluster_components['item_count'].mean(),
            'avg_complexity': cluster_components['level_complexity'].mean(),
            'avg_max_level': cluster_components['max_level'].mean(),
            'avg_transitions': cluster_components['level_transitions'].mean(),
            'avg_returns': cluster_components['level_returns'].mean(),
            'fe_lines': cluster_components['fe_line'].tolist(),
            'files': cluster_components['file_name'].unique().tolist()
        }
        
        # Classify family type based on characteristics
        if family_characteristics['avg_complexity'] > 3 and family_characteristics['avg_transitions'] > 5:
            family_type = "Complex Assembly"
        elif family_characteristics['avg_max_level'] <= 2 and family_characteristics['avg_item_count'] < 50:
            family_type = "Simple Component"
        elif family_characteristics['avg_item_count'] > 1000:
            family_type = "Major Assembly"
        elif family_characteristics['avg_returns'] > 2:
            family_type = "Branched Structure"
        else:
            family_type = "Standard Component"
        
        family_characteristics['family_type'] = family_type
        product_families[f"Family_{cluster_id}"] = family_characteristics
    
    print(f"✅ Identified {len(product_families)} product families")
    
    return product_families

def create_visualizations(components_df, clustering_results, dimensional_models, product_families, output_folder):
    """Create comprehensive visualizations"""
    print("📊 Creating visualizations...")
    
    # 1. Cluster visualization in PCA space
    if 'pca' in dimensional_models and 'kmeans' in clustering_results:
        plt.figure(figsize=(15, 10))
        
        plt.subplot(2, 2, 1)
        scatter = plt.scatter(components_df['pca_1'], components_df['pca_2'], 
                            c=components_df['cluster_kmeans'], cmap='tab10', alpha=0.7)
        plt.xlabel(f'PCA 1 ({dimensional_models["pca"]["explained_variance"][0]:.1%} variance)')
        plt.ylabel(f'PCA 2 ({dimensional_models["pca"]["explained_variance"][1]:.1%} variance)')
        plt.title('Product Family Clusters (PCA Space)')
        plt.colorbar(scatter, label='Cluster ID')
        
        # 2. t-SNE visualization
        if 'tsne' in dimensional_models:
            plt.subplot(2, 2, 2)
            scatter = plt.scatter(components_df['tsne_1'], components_df['tsne_2'], 
                                c=components_df['cluster_kmeans'], cmap='tab10', alpha=0.7)
            plt.xlabel('t-SNE 1')
            plt.ylabel('t-SNE 2')
            plt.title('Product Family Clusters (t-SNE Space)')
            plt.colorbar(scatter, label='Cluster ID')
        
        # 3. Feature importance
        plt.subplot(2, 2, 3)
        feature_names = ['item_count', 'max_level', 'level_complexity', 'level_transitions', 'level_returns']
        pca_components = dimensional_models['pca']['model'].components_[:2, :len(feature_names)]
        
        for i, feature in enumerate(feature_names):
            plt.arrow(0, 0, pca_components[0, i], pca_components[1, i], 
                     head_width=0.05, head_length=0.05, fc='red', ec='red')
            plt.text(pca_components[0, i]*1.1, pca_components[1, i]*1.1, feature, fontsize=8)
        
        plt.xlim(-1, 1)
        plt.ylim(-1, 1)
        plt.xlabel('PCA 1')
        plt.ylabel('PCA 2')
        plt.title('Feature Contributions to PCA')
        plt.grid(True, alpha=0.3)
        
        # 4. Cluster characteristics
        plt.subplot(2, 2, 4)
        family_types = [family['family_type'] for family in product_families.values()]
        family_counts = Counter(family_types)
        
        plt.pie(family_counts.values(), labels=family_counts.keys(), autopct='%1.1f%%')
        plt.title('Product Family Types Distribution')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_folder, 'product_family_clustering_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    print("✅ Visualizations saved")

def save_clustering_results(components_df, clustering_results, product_families, output_folder):
    """Save all clustering results"""
    print("💾 Saving clustering results...")

    # Save component clustering data
    components_df.to_excel(os.path.join(output_folder, 'product_family_components.xlsx'), index=False)

    # Save product families summary
    families_data = []
    for family_name, family_data in product_families.items():
        # Convert numpy types to regular Python types
        clean_family_data = {}
        for key, value in family_data.items():
            if isinstance(value, (np.integer, np.int32, np.int64)):
                clean_family_data[key] = int(value)
            elif isinstance(value, (np.floating, np.float32, np.float64)):
                clean_family_data[key] = float(value)
            elif isinstance(value, np.ndarray):
                clean_family_data[key] = value.tolist()
            else:
                clean_family_data[key] = value
        families_data.append(clean_family_data)

    families_df = pd.DataFrame(families_data)
    families_df.to_excel(os.path.join(output_folder, 'product_families_summary.xlsx'), index=False)

    # Save simplified text summary instead of JSON to avoid serialization issues
    with open(os.path.join(output_folder, 'product_family_clustering_summary.txt'), 'w', encoding='utf-8') as f:
        f.write("PRODUCT FAMILY CLUSTERING ANALYSIS SUMMARY\n")
        f.write("=" * 60 + "\n\n")

        f.write(f"CLUSTERING SUMMARY\n")
        f.write("=" * 20 + "\n")
        f.write(f"Total components analyzed: {len(components_df)}\n")
        f.write(f"Total product families identified: {len(product_families)}\n")
        f.write(f"Clustering methods used: {', '.join(clustering_results.keys())}\n\n")

        for method, data in clustering_results.items():
            f.write(f"{method.upper()} CLUSTERING:\n")
            f.write(f"  Number of clusters: {data['n_clusters']}\n")
            cluster_sizes = Counter(data['labels'])
            f.write(f"  Cluster sizes: {dict(cluster_sizes.most_common())}\n\n")

        f.write("PRODUCT FAMILIES IDENTIFIED\n")
        f.write("=" * 30 + "\n")
        for family_name, family_data in product_families.items():
            f.write(f"\n{family_name}:\n")
            f.write(f"  Type: {family_data['family_type']}\n")
            f.write(f"  Components: {family_data['component_count']}\n")
            f.write(f"  Files involved: {family_data['files_involved']}\n")
            f.write(f"  Average complexity: {family_data['avg_complexity']:.2f}\n")
            f.write(f"  Average max level: {family_data['avg_max_level']:.2f}\n")

    print("✅ Results saved")

def main():
    print("🚀 Starting Product Family Clustering Analysis")
    print("=" * 70)
    print("🎯 Combining F-E-Line graphs + similarity data")
    print("📊 Creating product family clusters with dimensional modeling")
    
    output_folder = 'output'
    
    # Load data
    graph_data, similarity_data = load_graph_and_similarity_data()
    
    if not graph_data:
        print("❌ No graph data found!")
        return
    
    # Extract component features
    components_df = extract_component_features(graph_data)
    
    # Add similarity features
    components_df = create_similarity_features(similarity_data, components_df)
    
    # Perform clustering
    components_df, clustering_results, X_scaled, scaler = perform_clustering_analysis(components_df)
    
    # Create dimensional modeling
    components_df, dimensional_models = create_dimensional_modeling(components_df, X_scaled)
    
    # Identify product families
    product_families = identify_product_families(components_df, clustering_results)
    
    # Create visualizations
    create_visualizations(components_df, clustering_results, dimensional_models, product_families, output_folder)
    
    # Save results
    save_clustering_results(components_df, clustering_results, product_families, output_folder)
    
    print(f"\n🎉 Product Family Clustering Analysis Complete!")
    print("=" * 70)
    print(f"📊 Results Summary:")
    print(f"   • {len(components_df)} F-E-Line components analyzed")
    print(f"   • {len(product_families)} product families identified")
    print(f"   • Dimensional modeling in PCA and t-SNE space")
    print(f"\n📁 Files created:")
    print(f"   • product_family_components.xlsx - Component clustering data")
    print(f"   • product_families_summary.xlsx - Product families summary")
    print(f"   • product_family_clustering_results.json - Detailed results")
    print(f"   • product_family_clustering_analysis.png - Visualizations")

if __name__ == "__main__":
    main()
