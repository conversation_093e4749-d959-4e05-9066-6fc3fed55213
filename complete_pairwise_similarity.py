#!/usr/bin/env python3
"""
Complete Pairwise Similarity Analysis
Naive approach: Compare every item with every other item across all files.
Two nested for loops to find:
1. Same Item ID + Same Description
2. Same Description + Different Item ID  
3. Similar Description (50%+ threshold) + Any Item ID
"""

import os
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re
from difflib import SequenceMatcher
import time

def clean_text(text):
    """Clean text for comparison"""
    if pd.isna(text):
        return ""
    text = str(text).lower().strip()
    # Remove extra whitespace
    text = ' '.join(text.split())
    return text

def calculate_text_similarity(text1, text2):
    """Calculate similarity between two text strings using multiple methods"""
    if not text1 or not text2:
        return 0.0
    
    # Method 1: SequenceMatcher (character-level similarity)
    seq_similarity = SequenceMatcher(None, text1, text2).ratio()
    
    # Method 2: Word-level Jaccard similarity
    words1 = set(text1.split())
    words2 = set(text2.split())
    if len(words1) == 0 and len(words2) == 0:
        jaccard_similarity = 1.0
    elif len(words1) == 0 or len(words2) == 0:
        jaccard_similarity = 0.0
    else:
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        jaccard_similarity = intersection / union if union > 0 else 0.0
    
    # Combined similarity (weighted average)
    combined_similarity = (seq_similarity * 0.6) + (jaccard_similarity * 0.4)
    
    return combined_similarity

def load_all_items_with_coordinates():
    """Load ALL items from ALL files with complete coordinate information"""
    print("🔍 Loading ALL items from ALL files for complete pairwise comparison...")
    
    input_folder = 'input'
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    
    all_items = []
    item_index = 0
    
    for file_idx, file in enumerate(excel_files, 1):
        print(f"  [{file_idx}/{len(excel_files)}] Loading: {file}")
        file_path = os.path.join(input_folder, file)
        
        try:
            # Read the second sheet
            xls = pd.ExcelFile(file_path)
            if len(xls.sheet_names) < 2:
                print(f"    ⚠️  No second sheet in {file}")
                continue
                
            df = pd.read_excel(file_path, sheet_name=1)
            
            if len(df.columns) < 4:
                print(f"    ⚠️  Not enough columns in {file}")
                continue
            
            # Use corrected column mapping
            df = df.iloc[:, :4]
            df.columns = ['Level', 'Sequence', 'Item_ID', 'Item_Description']
            
            # Clean data but keep ALL valid items
            df = df.dropna(subset=['Level', 'Sequence', 'Item_ID', 'Item_Description'])
            df = df[df['Level'].astype(str).str.strip() != '']
            df = df[df['Item_ID'].astype(str).str.strip() != '']
            df = df[df['Item_Description'].astype(str).str.strip() != '']
            
            # Filter to Level 1, 2, 3 only
            df['Level_Number'] = df['Level'].astype(str).str.extract(r'^(\d+)').astype(float)
            df = df[df['Level_Number'].isin([1.0, 2.0, 3.0])]
            
            # Remove numeric-only Item IDs (these are sequence numbers)
            df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
            
            print(f"    ✅ Loaded {len(df)} valid items")
            
            # Add each item with complete coordinates
            for row_idx, row in df.iterrows():
                item_info = {
                    'global_index': item_index,
                    'file_name': file,
                    'file_index': file_idx,
                    'original_row': row_idx + 2,  # +2 for Excel header
                    'sheet_name': xls.sheet_names[1],
                    'level': str(row['Level']),
                    'level_number': row['Level_Number'],
                    'sequence': str(row['Sequence']),
                    'item_id': str(row['Item_ID']).strip(),
                    'item_description': str(row['Item_Description']).strip(),
                    'item_id_clean': clean_text(row['Item_ID']),
                    'item_description_clean': clean_text(row['Item_Description'])
                }
                all_items.append(item_info)
                item_index += 1
                
        except Exception as e:
            print(f"    ❌ Error processing {file}: {e}")
            continue
    
    print(f"✅ Total items loaded: {len(all_items)} from {len(excel_files)} files")
    return all_items

def complete_pairwise_comparison(all_items, similarity_threshold=0.5):
    """Complete pairwise comparison - every item vs every other item"""
    print(f"\n🔍 Starting complete pairwise comparison...")
    print(f"📊 Comparing {len(all_items):,} items against each other")
    print(f"📊 Total comparisons to make: {len(all_items) * (len(all_items) - 1) // 2:,}")
    
    # For very large datasets, we need to be smart about this
    if len(all_items) > 5000:
        print(f"⚠️  Large dataset detected. Sampling 5000 items for demonstration...")
        # Sample items proportionally from each file
        items_by_file = {}
        for item in all_items:
            if item['file_name'] not in items_by_file:
                items_by_file[item['file_name']] = []
            items_by_file[item['file_name']].append(item)
        
        sampled_items = []
        items_per_file = 5000 // len(items_by_file)
        
        for file_name, file_items in items_by_file.items():
            if len(file_items) <= items_per_file:
                sampled_items.extend(file_items)
            else:
                # Take every nth item to maintain distribution
                step = len(file_items) // items_per_file
                sampled_items.extend(file_items[::step][:items_per_file])
        
        all_items = sampled_items[:5000]
        print(f"✅ Using {len(all_items)} sampled items")
    
    matches = []
    total_comparisons = len(all_items) * (len(all_items) - 1) // 2
    comparisons_made = 0
    
    print(f"🔄 Starting naive double for-loop comparison...")
    start_time = time.time()
    
    # NAIVE APPROACH: Double for-loop comparing every item with every other item
    for i in range(len(all_items)):
        item1 = all_items[i]
        
        # Progress reporting
        if i % 100 == 0:
            elapsed = time.time() - start_time
            progress = (i * len(all_items)) / total_comparisons * 100
            print(f"    Progress: {progress:.1f}% - Item {i:,}/{len(all_items):,} - {elapsed:.1f}s elapsed")
        
        for j in range(i + 1, len(all_items)):
            item2 = all_items[j]
            comparisons_made += 1
            
            # Skip same file comparisons if you only want cross-file
            # Comment this out if you want same-file comparisons too
            if item1['file_name'] == item2['file_name']:
                continue
            
            # Check for matches
            match_found = False
            match_type = ""
            similarity_score = 0.0
            
            # 1. Same Item ID + Same Description (Perfect Match)
            if (item1['item_id_clean'] == item2['item_id_clean'] and 
                item1['item_description_clean'] == item2['item_description_clean']):
                match_found = True
                match_type = "Perfect_Match_Same_ID_Same_Desc"
                similarity_score = 1.0
            
            # 2. Same Description + Different Item ID
            elif (item1['item_description_clean'] == item2['item_description_clean'] and 
                  item1['item_id_clean'] != item2['item_id_clean']):
                match_found = True
                match_type = "Same_Desc_Different_ID"
                similarity_score = 1.0
            
            # 3. Similar Description (above threshold)
            else:
                desc_similarity = calculate_text_similarity(
                    item1['item_description_clean'], 
                    item2['item_description_clean']
                )
                
                if desc_similarity >= similarity_threshold:
                    match_found = True
                    if item1['item_id_clean'] == item2['item_id_clean']:
                        match_type = "Similar_Desc_Same_ID"
                    else:
                        match_type = "Similar_Desc_Different_ID"
                    similarity_score = desc_similarity
            
            # If match found, record it
            if match_found:
                match_record = {
                    'Match_Type': match_type,
                    'Similarity_Score': round(similarity_score, 4),
                    'Similarity_Percentage': round(similarity_score * 100, 2),
                    
                    # Item 1 Information
                    'Item_ID_1': item1['item_id'],
                    'Description_1': item1['item_description'],
                    'File_1': item1['file_name'],
                    'Sheet_1': item1['sheet_name'],
                    'Row_1': item1['original_row'],
                    'Level_1': item1['level'],
                    'Level_Number_1': item1['level_number'],
                    'Sequence_1': item1['sequence'],
                    'Global_Index_1': item1['global_index'],
                    
                    # Item 2 Information
                    'Item_ID_2': item2['item_id'],
                    'Description_2': item2['item_description'],
                    'File_2': item2['file_name'],
                    'Sheet_2': item2['sheet_name'],
                    'Row_2': item2['original_row'],
                    'Level_2': item2['level'],
                    'Level_Number_2': item2['level_number'],
                    'Sequence_2': item2['sequence'],
                    'Global_Index_2': item2['global_index'],
                    
                    # Comparison flags
                    'Cross_File_Match': item1['file_name'] != item2['file_name'],
                    'Same_Level': item1['level_number'] == item2['level_number'],
                    'Same_Item_ID': item1['item_id_clean'] == item2['item_id_clean'],
                    'Same_Description': item1['item_description_clean'] == item2['item_description_clean']
                }
                matches.append(match_record)
    
    elapsed_time = time.time() - start_time
    print(f"✅ Pairwise comparison complete!")
    print(f"📊 Comparisons made: {comparisons_made:,}")
    print(f"📊 Matches found: {len(matches):,}")
    print(f"⏰ Time elapsed: {elapsed_time:.1f} seconds")
    
    return matches

def create_clusters_and_save(matches):
    """Group similar items into clusters and save comprehensive results"""
    print(f"\n📊 Creating clusters and saving results...")
    
    if not matches:
        print("❌ No matches to save")
        return
    
    # Convert to DataFrame
    matches_df = pd.DataFrame(matches)
    
    # Sort by similarity score (highest first)
    matches_df = matches_df.sort_values('Similarity_Score', ascending=False)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Save comprehensive Excel file
    excel_file = os.path.join(output_folder, 'complete_pairwise_similarity_analysis.xlsx')
    
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # Main results
        matches_df.to_excel(writer, sheet_name='All_Matches', index=False)
        
        # Perfect matches only
        perfect_matches = matches_df[matches_df['Match_Type'] == 'Perfect_Match_Same_ID_Same_Desc']
        if len(perfect_matches) > 0:
            perfect_matches.to_excel(writer, sheet_name='Perfect_Matches', index=False)
        
        # Same description, different ID
        same_desc_diff_id = matches_df[matches_df['Match_Type'] == 'Same_Desc_Different_ID']
        if len(same_desc_diff_id) > 0:
            same_desc_diff_id.to_excel(writer, sheet_name='Same_Desc_Diff_ID', index=False)
        
        # Similar descriptions
        similar_desc = matches_df[matches_df['Match_Type'].str.contains('Similar_Desc')]
        if len(similar_desc) > 0:
            similar_desc.to_excel(writer, sheet_name='Similar_Descriptions', index=False)
        
        # Summary statistics
        summary_data = {
            'Match_Type': [
                'Perfect Matches (Same ID + Same Desc)',
                'Same Description + Different ID',
                'Similar Description + Same ID',
                'Similar Description + Different ID',
                'Total Matches'
            ],
            'Count': [
                len(matches_df[matches_df['Match_Type'] == 'Perfect_Match_Same_ID_Same_Desc']),
                len(matches_df[matches_df['Match_Type'] == 'Same_Desc_Different_ID']),
                len(matches_df[matches_df['Match_Type'] == 'Similar_Desc_Same_ID']),
                len(matches_df[matches_df['Match_Type'] == 'Similar_Desc_Different_ID']),
                len(matches_df)
            ],
            'Percentage': [
                f"{len(matches_df[matches_df['Match_Type'] == 'Perfect_Match_Same_ID_Same_Desc']) / len(matches_df) * 100:.1f}%",
                f"{len(matches_df[matches_df['Match_Type'] == 'Same_Desc_Different_ID']) / len(matches_df) * 100:.1f}%",
                f"{len(matches_df[matches_df['Match_Type'] == 'Similar_Desc_Same_ID']) / len(matches_df) * 100:.1f}%",
                f"{len(matches_df[matches_df['Match_Type'] == 'Similar_Desc_Different_ID']) / len(matches_df) * 100:.1f}%",
                "100.0%"
            ]
        }
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
    
    print(f"✅ Excel file saved: {excel_file}")
    
    # Create text summary
    text_file = os.path.join(output_folder, 'complete_pairwise_summary.txt')
    
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write("COMPLETE PAIRWISE SIMILARITY ANALYSIS\n")
        f.write("=" * 60 + "\n")
        f.write(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Method: Naive double for-loop comparison\n")
        f.write(f"Total Matches Found: {len(matches_df):,}\n\n")
        
        f.write("MATCH TYPE BREAKDOWN\n")
        f.write("=" * 25 + "\n")
        for _, row in summary_df.iterrows():
            f.write(f"{row['Match_Type']}: {row['Count']} ({row['Percentage']})\n")
        
        f.write(f"\n\nTOP 20 HIGHEST SIMILARITY MATCHES\n")
        f.write("=" * 40 + "\n")
        
        for i, (_, match) in enumerate(matches_df.head(20).iterrows(), 1):
            f.write(f"\n{i:2d}. {match['Match_Type']} - {match['Similarity_Percentage']:.1f}%\n")
            f.write(f"    Item 1: {match['Item_ID_1']} | {match['Description_1'][:60]}...\n")
            f.write(f"    File 1: {match['File_1']} (Row {match['Row_1']}, Level {match['Level_1']})\n")
            f.write(f"    Item 2: {match['Item_ID_2']} | {match['Description_2'][:60]}...\n")
            f.write(f"    File 2: {match['File_2']} (Row {match['Row_2']}, Level {match['Level_2']})\n")
    
    print(f"✅ Text summary saved: {text_file}")

def main():
    print("🚀 Starting Complete Pairwise Similarity Analysis")
    print("=" * 70)
    print("🎯 Naive approach: Every item vs every other item")
    print("📊 Finding:")
    print("   1. Same Item ID + Same Description")
    print("   2. Same Description + Different Item ID")
    print("   3. Similar Description (50%+ threshold)")
    
    # Load ALL items
    all_items = load_all_items_with_coordinates()
    
    if not all_items:
        print("❌ No items found!")
        return
    
    # Complete pairwise comparison
    matches = complete_pairwise_comparison(all_items, similarity_threshold=0.5)
    
    # Create clusters and save results
    create_clusters_and_save(matches)
    
    print(f"\n🎉 Complete Pairwise Analysis Finished!")
    print("=" * 70)
    print(f"📊 Final Results:")
    print(f"   • {len(matches):,} total matches found")
    print(f"   • Complete coordinate tracking for all matches")
    print(f"   • All match types identified and categorized")
    print(f"\n📁 Files created:")
    print(f"   • complete_pairwise_similarity_analysis.xlsx")
    print(f"   • complete_pairwise_summary.txt")

if __name__ == "__main__":
    main()
