#!/usr/bin/env python3
"""
Install PDF + ColPali Dependencies
Setup environment for PDF processing with ColPali
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install all required dependencies"""
    print("📦 Installing PDF + ColPali dependencies...")
    
    # Core packages
    packages = [
        "torch",
        "torchvision", 
        "transformers>=4.30.0",
        "pillow",
        "opencv-python",
        "numpy",
        "pandas",
        "matplotlib",
        "PyMuPDF",  # For PDF processing
        "pytesseract",  # For OCR
    ]
    
    # Install packages
    for package in packages:
        print(f"📥 Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installed")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ Failed to install {package}: {e}")
    
    # Try to install ColPali
    print("🤖 Installing ColPali...")
    try:
        # Try official package first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "colpali-engine"])
        print("✅ ColPali installed from PyPI")
    except:
        try:
            # Try from GitHub
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "git+https://github.com/illuin-tech/colpali.git"
            ])
            print("✅ ColPali installed from GitHub")
        except:
            print("⚠️ ColPali installation failed - will use alternative models")

def test_installation():
    """Test if everything is installed correctly"""
    print("\n🧪 Testing installation...")
    
    try:
        import torch
        print("✅ PyTorch available")
        
        import cv2
        print("✅ OpenCV available")
        
        import fitz  # PyMuPDF
        print("✅ PyMuPDF available")
        
        from transformers import AutoProcessor, AutoModel
        print("✅ Transformers available")
        
        from PIL import Image
        print("✅ PIL available")
        
        # Test ColPali
        try:
            processor = AutoProcessor.from_pretrained("vidore/colpali", trust_remote_code=True)
            print("✅ ColPali processor available")
        except:
            print("⚠️ ColPali not available - will use CLIP fallback")
        
        return True
        
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False

def create_test_pdf():
    """Create a test PDF for verification"""
    print("📄 Creating test PDF...")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.lib.colors import blue, purple, red
        
        # Create test PDF
        test_pdf = "test_architecture.pdf"
        c = canvas.Canvas(test_pdf, pagesize=letter)
        
        # Add colored rectangles with text
        c.setFillColor(blue)
        c.rect(100, 600, 150, 50, fill=1)
        c.setFillColor("white")
        c.drawString(110, 620, "Main Unit - 4516256")
        
        c.setFillColor(purple)
        c.rect(300, 600, 150, 50, fill=1)
        c.setFillColor("white")
        c.drawString(310, 620, "Display - DISP-15")
        
        c.setFillColor(red)
        c.rect(200, 500, 150, 50, fill=1)
        c.setFillColor("white")
        c.drawString(210, 520, "Memory - MEM-512")
        
        c.save()
        print(f"✅ Test PDF created: {test_pdf}")
        
    except ImportError:
        print("⚠️ ReportLab not available - skipping test PDF creation")
        print("💡 Install with: pip install reportlab")

def main():
    """Main installation function"""
    print("🚀 PDF + COLPALI SETUP")
    print("=" * 40)
    
    # Install dependencies
    install_dependencies()
    
    # Test installation
    success = test_installation()
    
    # Create test PDF
    create_test_pdf()
    
    print(f"\n🎉 Setup Complete!")
    print("=" * 40)
    
    if success:
        print("✅ All dependencies installed successfully!")
        print("📄 Ready for PDF processing with ColPali")
    else:
        print("⚠️ Some issues detected - check error messages above")
    
    print(f"\n📋 Next Steps:")
    print("1. Convert your PowerPoint to PDF")
    print("2. Place PDF in input/ folder as: Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pdf")
    print("3. Run: python pdf_colpali_extractor.py")
    
    print(f"\n📁 Expected file structure:")
    print("   input/")
    print("   ├── Voyager_30.1_IPM_Global_PCM-21-MAR-2025.pdf")
    print("   output/")
    print("   ├── pdf_powerpoint_components.csv")
    print("   └── pdf_extraction_detailed.xlsx")

if __name__ == "__main__":
    main()
