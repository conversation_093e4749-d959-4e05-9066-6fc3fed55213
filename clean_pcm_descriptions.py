#!/usr/bin/env python3
"""
Nettoyer les descriptions PCM pour enlever les autres codes parasites
"""

import pandas as pd
import re
from collections import defaultdict

class PCMDescriptionCleaner:
    def __init__(self):
        """Initialize le nettoyeur de descriptions"""
        self.pcm_codes_df = None
        self.all_codes_pattern = None
        
    def load_extracted_codes(self):
        """Charger les codes extraits"""
        try:
            self.pcm_codes_df = pd.read_csv('output/real_voyager_pcm_codes.csv')
            print(f"✅ Codes chargés: {len(self.pcm_codes_df)} entrées")
            
            # Créer un pattern avec tous les codes pour les détecter
            all_codes = set(self.pcm_codes_df['code'].unique())
            # Ajouter le pattern général pour détecter d'autres codes
            self.all_codes_pattern = r'[A-Z]\d{4,5}[A-Z]{2}'
            
            return True
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def clean_single_description(self, description, main_code):
        """Nettoyer une description en enlevant les codes parasites"""
        if not description:
            return ""
        
        # 1. Enlever le code principal
        clean_desc = description.replace(main_code, '').strip()
        
        # 2. Enlever tous les autres codes au format [Lettre][4-5 chiffres][2 lettres]
        clean_desc = re.sub(self.all_codes_pattern, '', clean_desc)
        
        # 3. Enlever les codes courts comme "FLO", "TJ", etc.
        clean_desc = re.sub(r'\b[A-Z]{2,4}\b', '', clean_desc)
        
        # 4. Enlever les caractères spéciaux et nettoyer
        clean_desc = re.sub(r'[,，•\-–—]+', ' ', clean_desc)
        clean_desc = re.sub(r'\s+', ' ', clean_desc)
        clean_desc = clean_desc.strip()
        
        # 5. Enlever les mots très courts ou numériques
        words = clean_desc.split()
        clean_words = []
        for word in words:
            # Garder les mots de plus de 2 caractères qui ne sont pas que des chiffres
            if len(word) > 2 and not word.isdigit() and not re.match(r'^[A-Z0-9\-_]+$', word):
                clean_words.append(word)
        
        final_description = ' '.join(clean_words)
        
        return final_description
    
    def clean_all_descriptions(self):
        """Nettoyer toutes les descriptions"""
        print("\n🧹 NETTOYAGE DES DESCRIPTIONS")
        print("=" * 50)
        print("Objectif: Enlever les codes parasites des descriptions")
        print("")
        
        cleaned_data = []
        
        for idx, row in self.pcm_codes_df.iterrows():
            code = row['code']
            original_desc = row['description']
            
            # Nettoyer la description
            clean_desc = self.clean_single_description(original_desc, code)
            
            cleaned_entry = {
                'page': row['page'],
                'code': code,
                'original_description': original_desc,
                'clean_description': clean_desc,
                'description_length': len(clean_desc)
            }
            
            cleaned_data.append(cleaned_entry)
        
        self.cleaned_df = pd.DataFrame(cleaned_data)
        
        # Enlever les entrées avec des descriptions trop courtes
        self.cleaned_df = self.cleaned_df[self.cleaned_df['description_length'] > 5]
        
        print(f"✅ {len(self.cleaned_df)} descriptions nettoyées")
        return self.cleaned_df
    
    def show_cleaning_examples(self):
        """Montrer des exemples de nettoyage"""
        print("\n📋 EXEMPLES DE NETTOYAGE")
        print("=" * 50)
        
        # Prendre quelques exemples
        examples = self.cleaned_df.head(10)
        
        for idx, row in examples.iterrows():
            print(f"\n🔧 Code: {row['code']}")
            print(f"   Avant: {row['original_description'][:80]}...")
            print(f"   Après: {row['clean_description']}")
            print("")
    
    def analyze_cleaned_descriptions(self):
        """Analyser les descriptions nettoyées"""
        print("\n📊 ANALYSE DES DESCRIPTIONS NETTOYÉES")
        print("=" * 50)
        
        # Statistiques générales
        total_cleaned = len(self.cleaned_df)
        avg_length = self.cleaned_df['description_length'].mean()
        
        print(f"Total descriptions nettoyées: {total_cleaned}")
        print(f"Longueur moyenne: {avg_length:.1f} caractères")
        
        # Mots les plus fréquents
        all_words = []
        for desc in self.cleaned_df['clean_description']:
            words = desc.lower().split()
            all_words.extend(words)
        
        word_counts = defaultdict(int)
        for word in all_words:
            if len(word) > 3:  # Mots de plus de 3 caractères
                word_counts[word] += 1
        
        print(f"\nMots les plus fréquents:")
        top_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        for word, count in top_words:
            print(f"  {word}: {count} fois")
        
        # Grouper par types de descriptions
        self.group_by_description_type()
    
    def group_by_description_type(self):
        """Grouper par types de descriptions"""
        print(f"\n🏷️ GROUPEMENT PAR TYPES")
        print("=" * 30)
        
        # Définir des catégories basées sur les mots clés
        categories = {
            'Table/Config': ['table', 'config', 'configuration'],
            'Interface': ['interface'],
            'Controller': ['controller', 'control'],
            'System': ['system'],
            'Board': ['board'],
            'Module': ['module'],
            'Coil': ['coil'],
            'Cable': ['cable'],
            'Kit': ['kit'],
            'Software': ['software'],
            'Upgrade': ['upgrade'],
            'Language': ['language'],
            'Label': ['label'],
            'Room': ['room'],
            'Gating': ['gating'],
            'Voyager': ['voyager']
        }
        
        categorized = defaultdict(list)
        
        for idx, row in self.cleaned_df.iterrows():
            desc_lower = row['clean_description'].lower()
            code = row['code']
            
            # Trouver la catégorie
            found_category = False
            for category, keywords in categories.items():
                if any(keyword in desc_lower for keyword in keywords):
                    categorized[category].append({
                        'code': code,
                        'description': row['clean_description']
                    })
                    found_category = True
                    break
            
            if not found_category:
                categorized['Other'].append({
                    'code': code,
                    'description': row['clean_description']
                })
        
        # Afficher les catégories
        for category, items in categorized.items():
            if len(items) > 0:
                print(f"\n📂 {category} ({len(items)} codes):")
                for item in items[:3]:  # Montrer 3 exemples
                    print(f"   {item['code']}: {item['description'][:50]}...")
                if len(items) > 3:
                    print(f"   ... et {len(items) - 3} autres")
    
    def save_cleaned_descriptions(self):
        """Sauvegarder les descriptions nettoyées"""
        print(f"\n💾 SAUVEGARDE DES DESCRIPTIONS NETTOYÉES")
        print("=" * 50)
        
        # Sauvegarder le fichier complet
        output_path = "output/cleaned_voyager_pcm_descriptions.csv"
        self.cleaned_df.to_csv(output_path, index=False)
        print(f"✅ Descriptions nettoyées sauvegardées: {output_path}")
        
        # Créer un fichier simplifié pour la similarité BOM
        simple_df = self.cleaned_df[['code', 'clean_description', 'page']].copy()
        simple_df = simple_df.rename(columns={'clean_description': 'description'})
        
        simple_path = "output/pcm_for_bom_similarity.csv"
        simple_df.to_csv(simple_path, index=False)
        print(f"✅ Fichier pour similarité BOM: {simple_path}")
        
        return simple_df

def main():
    """Fonction principale"""
    print("🧹 NETTOYAGE DES DESCRIPTIONS PCM VOYAGER")
    print("=" * 70)
    print("Objectif: Enlever les codes parasites des descriptions")
    print("")
    
    cleaner = PCMDescriptionCleaner()
    
    if not cleaner.load_extracted_codes():
        return None
    
    # Nettoyer les descriptions
    cleaned_df = cleaner.clean_all_descriptions()
    
    # Montrer des exemples
    cleaner.show_cleaning_examples()
    
    # Analyser les résultats
    cleaner.analyze_cleaned_descriptions()
    
    # Sauvegarder
    simple_df = cleaner.save_cleaned_descriptions()
    
    print(f"\n✅ NETTOYAGE TERMINÉ")
    print(f"📊 {len(simple_df)} descriptions nettoyées prêtes pour la BOM")
    print(f"🎯 Fichier prêt: output/pcm_for_bom_similarity.csv")
    
    return cleaner, simple_df

if __name__ == "__main__":
    cleaner, simple_df = main()
