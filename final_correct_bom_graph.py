#!/usr/bin/env python3
"""
Final Correct BOM Graph Analysis
- Each unique F-E-Line gets its own subgraph (Level 0 root)
- Within each F-E-Line, hierarchy based on Level numbers
- Levels can jump (start at 2, 3, etc.)
- Levels can return to ANY previous level, creating new branches
"""

import os
import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from collections import defaultdict
import json
import re

def extract_level_number(level_str):
    """Extract numeric level from level string"""
    try:
        # Look for first number in the string
        match = re.search(r'(\d+)', str(level_str))
        if match:
            return int(match.group(1))
        return 1  # Default to 1 if no number found
    except:
        return 1

def load_bom_for_fe_line_analysis(file_path):
    """Load BOM file for F-E-Line analysis"""
    try:
        xls = pd.ExcelFile(file_path)
        if len(xls.sheet_names) < 2:
            return None
            
        df = pd.read_excel(file_path, sheet_name=1)
        
        if len(df.columns) < 4:
            return None
        
        # Column mapping: F-E-Line, Sequence, Item_ID, Description
        df = df.iloc[:, :4]
        df.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
        
        # Clean data
        df = df.dropna(subset=['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description'])
        df = df[df['F_E_Line'].astype(str).str.strip() != '']
        df = df[df['Item_ID'].astype(str).str.strip() != '']
        df = df[df['Item_Description'].astype(str).str.strip() != '']
        
        # Remove numeric-only Item IDs
        df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
        
        # Add processing info
        df['Row_Index'] = range(len(df))
        df['F_E_Line_Clean'] = df['F_E_Line'].astype(str).str.strip()
        
        # Extract level numbers for hierarchy
        df['Level_Number'] = df['Sequence'].apply(extract_level_number)  # Level from Sequence column
        
        return df
        
    except Exception as e:
        print(f"Error loading file: {e}")
        return None

def create_fe_line_subgraphs(df, file_name):
    """Create subgraphs for each unique F-E-Line with proper level hierarchy"""
    print(f"🔍 Creating F-E-Line subgraphs for: {file_name}")
    
    file_graph = {
        'file_name': file_name,
        'file_root': f"FILE_ROOT",
        'total_items': len(df),
        'nodes': {},
        'edges': [],
        'fe_line_subgraphs': {}
    }
    
    # Add file root (grand father of all)
    file_root = file_graph['file_root']
    file_graph['nodes'][file_root] = {
        'id': file_root,
        'level': -1,
        'fe_line': 'FILE_ROOT',
        'description': f"File: {file_name[:30]}...",
        'node_type': 'file_root',
        'row_index': -1
    }
    
    # Group by F-E-Line
    fe_line_groups = df.groupby('F_E_Line_Clean')
    print(f"  📊 Found {len(fe_line_groups)} unique F-E-Lines")
    
    for fe_line, group in fe_line_groups:
        print(f"    🔄 Processing F-E-Line: {fe_line} ({len(group)} items)")
        
        # Create F-E-Line root (Level 0)
        fe_line_root_id = f"FE_ROOT_{fe_line}"
        file_graph['nodes'][fe_line_root_id] = {
            'id': fe_line_root_id,
            'level': 0,
            'fe_line': fe_line,
            'description': f"F-E-Line: {fe_line}",
            'node_type': 'fe_line_root',
            'row_index': -1
        }
        
        # Connect F-E-Line root to file root
        file_graph['edges'].append({
            'parent': file_root,
            'child': fe_line_root_id,
            'parent_level': -1,
            'child_level': 0
        })
        
        # Initialize subgraph tracking
        subgraph = {
            'root': fe_line_root_id,
            'items': [],
            'level_stack': {0: fe_line_root_id},  # Track current parent for each level
            'level_history': []  # Track level progression
        }
        
        # Process items in this F-E-Line sequentially
        group_sorted = group.sort_values('Row_Index')
        
        for idx, (_, row) in enumerate(group_sorted.iterrows()):
            level_number = row['Level_Number']
            
            # Create item node
            item_node_id = f"ITEM_{row['Row_Index']}_{fe_line}_{level_number}"
            
            file_graph['nodes'][item_node_id] = {
                'id': item_node_id,
                'level': level_number,
                'fe_line': fe_line,
                'sequence': str(row['Sequence']),
                'item_id': str(row['Item_ID']),
                'description': str(row['Item_Description'])[:40],
                'full_description': str(row['Item_Description']),
                'node_type': 'component',
                'row_index': row['Row_Index']
            }
            
            subgraph['items'].append(item_node_id)
            subgraph['level_history'].append(level_number)
            
            # Determine parent based on level hierarchy and branching rules
            if level_number in subgraph['level_stack']:
                # If we return to a previous level, we're creating a new branch
                # Parent is the parent of the previous node at this level
                previous_node_at_level = subgraph['level_stack'][level_number]
                previous_node_level = file_graph['nodes'][previous_node_at_level]['level']
                
                # Find parent of previous node at this level
                parent_level = level_number - 1
                while parent_level >= 0:
                    if parent_level in subgraph['level_stack']:
                        parent_id = subgraph['level_stack'][parent_level]
                        break
                    parent_level -= 1
                else:
                    parent_id = fe_line_root_id  # Fallback to F-E-Line root
            else:
                # New level - find appropriate parent from previous levels
                parent_level = level_number - 1
                while parent_level >= 0:
                    if parent_level in subgraph['level_stack']:
                        parent_id = subgraph['level_stack'][parent_level]
                        break
                    parent_level -= 1
                else:
                    parent_id = fe_line_root_id  # Fallback to F-E-Line root
            
            # Create edge
            file_graph['edges'].append({
                'parent': parent_id,
                'child': item_node_id,
                'parent_level': file_graph['nodes'][parent_id]['level'],
                'child_level': level_number
            })
            
            # Update level stack - this item becomes the current node for this level
            subgraph['level_stack'][level_number] = item_node_id
            
            # Clean up level stack - remove deeper levels when we return to a shallower level
            levels_to_remove = [l for l in subgraph['level_stack'].keys() if l > level_number]
            for l in levels_to_remove:
                del subgraph['level_stack'][l]
        
        file_graph['fe_line_subgraphs'][fe_line] = subgraph
        
        # Show level progression for this F-E-Line
        level_progression = " → ".join(map(str, subgraph['level_history'][:10]))
        if len(subgraph['level_history']) > 10:
            level_progression += "..."
        print(f"      📈 Level progression: {level_progression}")
    
    print(f"  ✅ Created {len(file_graph['nodes'])} nodes and {len(file_graph['edges'])} edges")
    return file_graph

def create_networkx_from_fe_line_graph(file_graph):
    """Convert F-E-Line graph to NetworkX"""
    G = nx.DiGraph()
    
    # Add all nodes
    for node_id, node_data in file_graph['nodes'].items():
        G.add_node(node_id,
                   level=node_data['level'],
                   node_type=node_data['node_type'],
                   label=node_data.get('description', ''),
                   fe_line=node_data.get('fe_line', ''),
                   item_id=node_data.get('item_id', ''))
    
    # Add all edges
    for edge in file_graph['edges']:
        G.add_edge(edge['parent'], edge['child'])
    
    return G

def visualize_fe_line_graph(G, file_name, output_folder):
    """Create visualization of F-E-Line graph"""
    print(f"📊 Creating F-E-Line visualization for: {file_name}")
    
    plt.figure(figsize=(20, 16))
    
    # Create layout
    pos = create_fe_line_layout(G)
    
    # Define colors by node type and level
    node_colors = []
    node_sizes = []
    
    for node in G.nodes():
        node_type = G.nodes[node].get('node_type', 'component')
        level = G.nodes[node].get('level', 0)
        
        if node_type == 'file_root':
            node_colors.append('red')
            node_sizes.append(800)
        elif node_type == 'fe_line_root':
            node_colors.append('orange')
            node_sizes.append(400)
        else:  # component
            # Color by level
            level_colors = {
                1: 'lightblue',
                2: 'lightgreen', 
                3: 'lightyellow',
                4: 'lightpink',
                5: 'lightcyan'
            }
            node_colors.append(level_colors.get(level, 'lightgray'))
            node_sizes.append(max(150 - level * 10, 50))  # Smaller for deeper levels
    
    # Draw graph
    nx.draw(G, pos, 
            node_color=node_colors,
            node_size=node_sizes,
            with_labels=False,
            arrows=True,
            arrowsize=8,
            edge_color='gray',
            alpha=0.7,
            arrowstyle='->')
    
    # Add labels for important nodes
    important_nodes = {}
    for node in G.nodes():
        node_type = G.nodes[node].get('node_type', 'component')
        if node_type in ['file_root', 'fe_line_root']:
            label = G.nodes[node].get('label', '')[:15]
            important_nodes[node] = label
    
    nx.draw_networkx_labels(G, pos, important_nodes, font_size=6, font_weight='bold')
    
    # Legend
    legend_elements = [
        mpatches.Patch(color='red', label='File Root'),
        mpatches.Patch(color='orange', label='F-E-Line Roots (Level 0)'),
        mpatches.Patch(color='lightblue', label='Level 1'),
        mpatches.Patch(color='lightgreen', label='Level 2'),
        mpatches.Patch(color='lightyellow', label='Level 3'),
        mpatches.Patch(color='lightpink', label='Level 4'),
        mpatches.Patch(color='lightcyan', label='Level 5'),
        mpatches.Patch(color='lightgray', label='Other Levels')
    ]
    
    plt.legend(handles=legend_elements, loc='upper right', fontsize=8)
    plt.title(f'F-E-Line BOM Graph (Branching Hierarchy): {file_name}', 
              fontsize=14, fontweight='bold')
    
    # Save
    safe_filename = re.sub(r'[^\w\-_\.]', '_', file_name)
    viz_file = os.path.join(output_folder, f'fe_line_bom_graph_{safe_filename}.png')
    plt.savefig(viz_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ F-E-Line visualization saved: {viz_file}")
    return viz_file

def create_fe_line_layout(G):
    """Create layout for F-E-Line graph"""
    pos = {}
    
    # Group by level
    levels = defaultdict(list)
    for node in G.nodes():
        level = G.nodes[node].get('level', 0)
        levels[level].append(node)
    
    # Position nodes level by level
    y_spacing = 2.5
    
    for level, nodes in levels.items():
        y = -level * y_spacing
        
        # Distribute horizontally with adaptive spacing
        if len(nodes) == 1:
            x_positions = [0]
        else:
            x_spacing = min(25.0 / len(nodes), 2.5)
            total_width = (len(nodes) - 1) * x_spacing
            x_positions = [i * x_spacing - total_width/2 for i in range(len(nodes))]
        
        for i, node in enumerate(nodes):
            pos[node] = (x_positions[i], y)
    
    return pos

def main():
    print("🚀 Starting Final Correct BOM Graph Analysis")
    print("=" * 70)
    print("🎯 Each unique F-E-Line = own subgraph (Level 0)")
    print("📊 Within F-E-Line: hierarchy by Level numbers")
    print("🔄 Levels can jump & return to ANY level (branching)")
    
    input_folder = 'input'
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    print(f"📁 Found {len(excel_files)} Excel files")
    
    # Process first 2 files
    for i, file in enumerate(excel_files[:2], 1):
        print(f"\n[{i}/2] Processing: {file}")
        file_path = os.path.join(input_folder, file)
        
        # Load data
        df = load_bom_for_fe_line_analysis(file_path)
        if df is None:
            print(f"❌ Failed to load {file}")
            continue
        
        print(f"✅ Loaded {len(df)} items")
        
        # Show F-E-Line distribution
        fe_line_counts = df['F_E_Line_Clean'].value_counts()
        print(f"📊 Top F-E-Lines: {dict(fe_line_counts.head())}")
        
        # Create F-E-Line subgraphs
        file_graph = create_fe_line_subgraphs(df, file)
        
        # Convert to NetworkX
        G = create_networkx_from_fe_line_graph(file_graph)
        print(f"📊 F-E-Line graph: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
        
        # Visualize
        visualize_fe_line_graph(G, file, output_folder)
        
        # Save data
        safe_filename = re.sub(r'[^\w\-_\.]', '_', file)
        json_file = os.path.join(output_folder, f'fe_line_graph_data_{safe_filename}.json')
        
        json_data = {
            'file_name': file_graph['file_name'],
            'total_items': file_graph['total_items'],
            'total_nodes': len(file_graph['nodes']),
            'total_edges': len(file_graph['edges']),
            'fe_line_count': len(file_graph['fe_line_subgraphs']),
            'fe_line_summary': {
                fe_line: {
                    'item_count': len(data['items']),
                    'level_progression': data['level_history'][:20]  # First 20 levels
                }
                for fe_line, data in file_graph['fe_line_subgraphs'].items()
            }
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ F-E-Line graph data saved: {json_file}")
    
    print(f"\n🎉 Final Correct BOM Graph Analysis Complete!")
    print("=" * 70)
    print(f"📁 Files created in: {os.path.abspath(output_folder)}")
    print(f"   • fe_line_bom_graph_*.png - F-E-Line visualizations")
    print(f"   • fe_line_graph_data_*.json - F-E-Line graph data")

if __name__ == "__main__":
    main()
