from flask import Flask, render_template
import pandas as pd
from data_loader import load_bom_data
from sbert_encoder import encode_item_descriptions
from graph_visualization import create_bom_graph

app = Flask(__name__)

@app.route('/')
def index():
    # Load the bill of materials data
    df = load_bom_data('path_to_your_bom_file.xlsx')  # Update with your BOM file path

    # Encode item descriptions using SBERT
    encoded_descriptions = encode_item_descriptions(df['Item Description'].tolist())

    # Create the BOM graph
    graph = create_bom_graph(df, encoded_descriptions)

    # Render the graph in a template (you'll need to create this template)
    return render_template('graph.html', graph=graph)

if __name__ == '__main__':
    app.run(debug=True)