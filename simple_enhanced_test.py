#!/usr/bin/env python3
"""
Simple Enhanced Algorithm Test - Real Results without Heavy Processing
Focus on core improvements: preprocessing, lexical similarity, code matching
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import re
from difflib import SequenceMatcher
import time
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load data"""
    print("📊 LOADING DATA")
    print("=" * 20)
    
    try:
        # Try CSV first, then Excel
        try:
            bom_data = pd.read_csv('output/voyager_5408509_bom_pcm_aggregated.csv')
        except:
            bom_data = pd.read_excel('output/voyager_5408509_bom_pcm_aggregated.xlsx')

        pcm_data = pd.read_csv('output/real_voyager_pcm_codes.csv')
        
        print(f"✅ BOM: {len(bom_data)} items")
        print(f"✅ PCM: {len(pcm_data)} codes")
        
        return bom_data, pcm_data
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, None

def enhanced_preprocess(text):
    """Enhanced preprocessing with domain knowledge"""
    if pd.isna(text) or text == '':
        return ''
    
    text = str(text).lower()
    
    # Medical device mappings
    mappings = {
        'tdi': 'time domain imaging',
        '1.5t': '1.5 tesla',
        '3.0t': '3.0 tesla',
        'mr': 'magnetic resonance',
        'mri': 'magnetic resonance imaging',
        'rf': 'radio frequency',
        'coil': 'magnetic coil',
        'array': 'coil array',
        'phantom': 'test phantom',
        'gating': 'respiratory gating',
        'voyager': 'voyager system'
    }
    
    # Apply mappings
    for abbrev, full in mappings.items():
        text = re.sub(r'\b' + abbrev + r'\b', full, text)
    
    # Clean text
    text = re.sub(r'[^\w\s\-\.]', ' ', text)
    text = re.sub(r'\s+', ' ', text)
    
    return text.strip()

def calculate_lightweight_similarity(bom_desc, pcm_desc, bom_id, pcm_code):
    """Calculate similarity without heavy SBERT processing"""
    
    # Preprocess
    bom_clean = enhanced_preprocess(bom_desc)
    pcm_clean = enhanced_preprocess(pcm_desc)
    
    similarities = {}
    
    # 1. Lexical similarity (Jaccard)
    bom_words = set(bom_clean.split()) if bom_clean else set()
    pcm_words = set(pcm_clean.split()) if pcm_clean else set()
    
    if len(bom_words) > 0 and len(pcm_words) > 0:
        intersection = len(bom_words.intersection(pcm_words))
        union = len(bom_words.union(pcm_words))
        jaccard_sim = intersection / union if union > 0 else 0.0
    else:
        jaccard_sim = 0.0
    similarities['lexical'] = jaccard_sim
    
    # 2. String similarity
    if bom_clean and pcm_clean:
        string_sim = SequenceMatcher(None, bom_clean, pcm_clean).ratio()
    else:
        string_sim = 0.0
    similarities['string'] = string_sim
    
    # 3. Code pattern matching
    bom_codes = re.findall(r'[A-Z]\d{4,6}[A-Z]{0,2}', str(bom_id))
    pcm_codes = re.findall(r'[A-Z]\d{4,6}[A-Z]{0,2}', str(pcm_code))
    
    code_sim = 0.0
    if bom_codes and pcm_codes:
        for bc in bom_codes:
            for pc in pcm_codes:
                if len(bc) >= 2 and len(pc) >= 2:
                    if bc[:2] == pc[:2]:
                        code_sim = max(code_sim, 0.8)
                    elif bc[:1] == pc[:1]:
                        code_sim = max(code_sim, 0.4)
    similarities['code'] = code_sim
    
    # 4. Key term matching (domain-specific)
    key_terms = ['coil', 'array', 'head', 'neck', 'table', 'system', 'controller', 
                 'interface', 'board', 'module', 'cable', 'phantom', 'upgrade',
                 'magnetic', 'resonance', 'tesla', 'imaging']
    
    bom_key_terms = set([word for word in bom_words if word in key_terms])
    pcm_key_terms = set([word for word in pcm_words if word in key_terms])
    
    if len(bom_key_terms) > 0 and len(pcm_key_terms) > 0:
        key_term_sim = len(bom_key_terms.intersection(pcm_key_terms)) / len(bom_key_terms.union(pcm_key_terms))
    else:
        key_term_sim = 0.0
    similarities['key_terms'] = key_term_sim
    
    # 5. Enhanced ensemble score
    weights = {
        'lexical': 0.35,
        'string': 0.25,
        'code': 0.25,
        'key_terms': 0.15
    }
    
    ensemble_score = sum(similarities[metric] * weights[metric] for metric in weights.keys())
    similarities['ensemble'] = ensemble_score
    
    return similarities

def test_enhanced_algorithm():
    """Test enhanced algorithm"""
    print("\n🚀 TESTING ENHANCED ALGORITHM (LIGHTWEIGHT)")
    print("=" * 60)
    
    # Load data
    bom_data, pcm_data = load_data()
    if bom_data is None:
        return None
    
    # Test on 100 random items
    test_bom = bom_data.sample(n=100, random_state=42)
    pcm_list = pcm_data.to_dict('records')
    
    print(f"📊 Testing: {len(test_bom)} BOM items vs {len(pcm_list)} PCM codes")
    
    # Test multiple thresholds
    thresholds = [0.5, 0.6, 0.7, 0.8]
    results = {}
    
    start_time = time.time()
    
    for threshold in thresholds:
        print(f"\n🎯 Testing threshold: {threshold}")
        
        matches = []
        
        for idx, (_, bom_row) in enumerate(test_bom.iterrows()):
            bom_desc = str(bom_row['Item_Description'])
            bom_id = str(bom_row['Item_ID'])
            
            best_score = 0.0
            best_match = None
            best_similarities = {}
            
            # Test against all PCM codes
            for pcm_item in pcm_list:
                pcm_desc = str(pcm_item.get('description', ''))
                pcm_code = str(pcm_item.get('code', ''))
                
                similarities = calculate_lightweight_similarity(
                    bom_desc, pcm_desc, bom_id, pcm_code
                )
                
                ensemble_score = similarities['ensemble']
                
                if ensemble_score > best_score:
                    best_score = ensemble_score
                    best_match = pcm_item
                    best_similarities = similarities
            
            # Apply threshold
            if best_score >= threshold and best_match is not None:
                matches.append({
                    'bom_id': bom_id,
                    'bom_desc': bom_desc[:50] + '...',
                    'pcm_code': best_match['code'],
                    'pcm_desc': best_match['description'][:50] + '...',
                    'ensemble_score': best_score,
                    'lexical': best_similarities['lexical'],
                    'string': best_similarities['string'],
                    'code': best_similarities['code'],
                    'key_terms': best_similarities['key_terms']
                })
            
            if (idx + 1) % 25 == 0:
                print(f"   Processed {idx + 1}/{len(test_bom)} items...")
        
        # Calculate metrics
        total_items = len(test_bom)
        matched_items = len(matches)
        coverage = (matched_items / total_items) * 100
        
        if matches:
            avg_ensemble = np.mean([m['ensemble_score'] for m in matches])
            avg_lexical = np.mean([m['lexical'] for m in matches])
            avg_string = np.mean([m['string'] for m in matches])
            avg_code = np.mean([m['code'] for m in matches])
            avg_key_terms = np.mean([m['key_terms'] for m in matches])
            
            high_quality = len([m for m in matches if m['ensemble_score'] > 0.7])
            excellent_quality = len([m for m in matches if m['ensemble_score'] > 0.8])
        else:
            avg_ensemble = avg_lexical = avg_string = avg_code = avg_key_terms = 0.0
            high_quality = excellent_quality = 0
        
        results[threshold] = {
            'total_items': total_items,
            'matched_items': matched_items,
            'coverage_percent': coverage,
            'avg_ensemble_score': avg_ensemble,
            'avg_lexical_score': avg_lexical,
            'avg_string_score': avg_string,
            'avg_code_score': avg_code,
            'avg_key_terms_score': avg_key_terms,
            'high_quality_matches': high_quality,
            'excellent_matches': excellent_quality,
            'matches': matches
        }
        
        print(f"   ✅ Results:")
        print(f"      Matched: {matched_items}/{total_items} ({coverage:.1f}%)")
        print(f"      Avg Ensemble Score: {avg_ensemble:.3f}")
        print(f"      High Quality (>0.7): {high_quality}")
        print(f"      Excellent (>0.8): {excellent_quality}")
    
    total_time = time.time() - start_time
    print(f"\n⏱️ Total test time: {total_time:.1f} seconds")
    
    return results

def compare_with_original(enhanced_results):
    """Compare with original algorithm"""
    print(f"\n📊 COMPARISON WITH ORIGINAL ALGORITHM")
    print("=" * 60)
    
    # Original algorithm metrics (from actual previous analysis)
    original_metrics = {
        'precision': 0.002,
        'coverage': 100.0,
        'avg_similarity': 0.010,
        'high_quality_matches': 1
    }
    
    print("REAL ENHANCED ALGORITHM RESULTS:")
    print("=" * 40)
    print(f"{'Threshold':<10} {'Coverage':<10} {'Avg Score':<12} {'High Qual':<10} {'Excellent':<10}")
    print("-" * 60)
    
    for threshold, results in enhanced_results.items():
        print(f"{threshold:<10.1f} {results['coverage_percent']:<10.1f} {results['avg_ensemble_score']:<12.3f} {results['high_quality_matches']:<10d} {results['excellent_matches']:<10d}")
    
    print(f"\nOriginal:  {original_metrics['coverage']:<10.1f} {original_metrics['avg_similarity']:<12.3f} {original_metrics['high_quality_matches']:<10d} {'0':<10}")
    
    # Calculate improvements
    if 0.7 in enhanced_results:
        best_result = enhanced_results[0.7]
        
        print(f"\n🎯 KEY IMPROVEMENTS (Threshold 0.7):")
        print(f"   Score Improvement: {original_metrics['avg_similarity']:.3f} → {best_result['avg_ensemble_score']:.3f}")
        improvement_factor = best_result['avg_ensemble_score'] / max(original_metrics['avg_similarity'], 0.001)
        print(f"   Improvement Factor: {improvement_factor:.1f}x")
        print(f"   High Quality Matches: {original_metrics['high_quality_matches']} → {best_result['high_quality_matches']}")
        print(f"   Coverage Trade-off: {original_metrics['coverage']:.1f}% → {best_result['coverage_percent']:.1f}%")

def create_visualization(enhanced_results):
    """Create visualization"""
    print(f"\n📊 CREATING VISUALIZATION")
    print("=" * 30)
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Enhanced Algorithm: REAL Empirical Results', fontsize=16, fontweight='bold')
    
    thresholds = list(enhanced_results.keys())
    
    # 1. Coverage vs Threshold
    coverages = [enhanced_results[t]['coverage_percent'] for t in thresholds]
    ax1.plot(thresholds, coverages, 'bo-', linewidth=3, markersize=10)
    ax1.axhline(y=100, color='r', linestyle='--', linewidth=2, label='Original (100%)')
    ax1.set_title('Coverage Rate vs Threshold (REAL)', fontweight='bold')
    ax1.set_xlabel('Threshold')
    ax1.set_ylabel('Coverage (%)')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 2. Average Score vs Threshold
    avg_scores = [enhanced_results[t]['avg_ensemble_score'] for t in thresholds]
    ax2.plot(thresholds, avg_scores, 'go-', linewidth=3, markersize=10)
    ax2.axhline(y=0.010, color='r', linestyle='--', linewidth=2, label='Original (0.010)')
    ax2.set_title('Average Ensemble Score vs Threshold (REAL)', fontweight='bold')
    ax2.set_xlabel('Threshold')
    ax2.set_ylabel('Average Score')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 3. High Quality Matches
    high_quality = [enhanced_results[t]['high_quality_matches'] for t in thresholds]
    ax3.bar(thresholds, high_quality, color=['lightblue', 'blue', 'darkblue', 'navy'], alpha=0.7)
    ax3.set_title('High Quality Matches (>0.7) - REAL', fontweight='bold')
    ax3.set_xlabel('Threshold')
    ax3.set_ylabel('Number of High Quality Matches')
    ax3.grid(True, alpha=0.3)
    
    # 4. Component Breakdown (Threshold 0.7)
    if 0.7 in enhanced_results:
        components = ['Lexical', 'String', 'Code', 'Key Terms']
        scores = [
            enhanced_results[0.7]['avg_lexical_score'],
            enhanced_results[0.7]['avg_string_score'],
            enhanced_results[0.7]['avg_code_score'],
            enhanced_results[0.7]['avg_key_terms_score']
        ]
        
        bars = ax4.bar(components, scores, color=['lightgreen', 'orange', 'pink', 'lightcoral'])
        ax4.set_title('Component Scores (Threshold 0.7) - REAL', fontweight='bold')
        ax4.set_ylabel('Average Component Score')
        ax4.grid(True, alpha=0.3)
        
        # Add values on bars
        for bar, score in zip(bars, scores):
            ax4.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                    f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('output/real_enhanced_algorithm_results.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ Visualization saved: real_enhanced_algorithm_results.png")

def export_results(enhanced_results):
    """Export results"""
    print(f"\n💾 EXPORTING RESULTS")
    print("=" * 25)
    
    # Create summary
    summary_data = []
    for threshold, results in enhanced_results.items():
        summary_data.append({
            'Threshold': threshold,
            'Total_Items': results['total_items'],
            'Matched_Items': results['matched_items'],
            'Coverage_Percent': results['coverage_percent'],
            'Avg_Ensemble_Score': results['avg_ensemble_score'],
            'Avg_Lexical_Score': results['avg_lexical_score'],
            'Avg_String_Score': results['avg_string_score'],
            'Avg_Code_Score': results['avg_code_score'],
            'Avg_Key_Terms_Score': results['avg_key_terms_score'],
            'High_Quality_Matches': results['high_quality_matches'],
            'Excellent_Matches': results['excellent_matches']
        })
    
    summary_df = pd.DataFrame(summary_data)
    
    # Export to Excel
    with pd.ExcelWriter('output/real_enhanced_results.xlsx', engine='openpyxl') as writer:
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        # Export matches for best threshold
        if 0.7 in enhanced_results and enhanced_results[0.7]['matches']:
            matches_df = pd.DataFrame(enhanced_results[0.7]['matches'])
            matches_df.to_excel(writer, sheet_name='Best_Matches_0_7', index=False)
    
    print(f"✅ Results exported: output/real_enhanced_results.xlsx")

def main():
    """Main execution"""
    print("🧪 ENHANCED ALGORITHM: REAL EMPIRICAL TEST")
    print("=" * 70)
    print("LIGHTWEIGHT VERSION - NO MORE ESTIMATES!")
    print("")
    
    # Test enhanced algorithm
    results = test_enhanced_algorithm()
    
    if results:
        # Compare with original
        compare_with_original(results)
        
        # Create visualization
        create_visualization(results)
        
        # Export results
        export_results(results)
        
        print(f"\n✅ REAL ENHANCED ALGORITHM TEST COMPLETE")
        print(f"📊 ACTUAL EMPIRICAL RESULTS OBTAINED")
        print(f"📈 Files: real_enhanced_results.xlsx, real_enhanced_algorithm_results.png")
    
    return results

if __name__ == "__main__":
    results = main()
