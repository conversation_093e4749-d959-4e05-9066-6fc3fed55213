#!/usr/bin/env python3
"""
Comprehensive Match Analysis
Creates separate files for:
1. Perfect matches (same ID + same description, different documents)
2. Description matches (same description + different ID, potential alternatives)
Tracks all coordinates (file, row, level, etc.)
"""

import os
import pandas as pd
import pickle
from collections import defaultdict
import re

def clean_description(text):
    if pd.isna(text):
        return ""
    return str(text).lower().strip()

def load_all_bom_data_with_coordinates():
    """Load all BOM data with full coordinate tracking"""
    print("🔍 Loading all BOM data with coordinate tracking...")
    
    input_folder = 'input'
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    
    all_items = []
    
    for file_idx, file in enumerate(excel_files, 1):
        print(f"  [{file_idx}/{len(excel_files)}] Processing: {file}")
        file_path = os.path.join(input_folder, file)
        
        try:
            # Read the second sheet
            xls = pd.ExcelFile(file_path)
            if len(xls.sheet_names) < 2:
                print(f"    ⚠️  No second sheet in {file}")
                continue
                
            df = pd.read_excel(file_path, sheet_name=1)
            
            if len(df.columns) < 4:
                print(f"    ⚠️  Not enough columns in {file}")
                continue
            
            # Use corrected column mapping
            df = df.iloc[:, :4]
            df.columns = ['Level', 'Sequence', 'Item_ID', 'Item_Description']
            
            # Clean data
            df = df.dropna(subset=['Level', 'Sequence', 'Item_ID', 'Item_Description'])
            df = df[df['Level'].astype(str).str.strip() != '']
            df = df[df['Sequence'].astype(str).str.strip() != '']
            df = df[df['Item_ID'].astype(str).str.strip() != '']
            df = df[df['Item_Description'].astype(str).str.strip() != '']
            
            # Filter to Level 1, 2, 3
            df['Level_Numeric'] = df['Level'].astype(str).str.extract(r'^(\d+)').astype(float)
            df = df[df['Level_Numeric'].isin([1.0, 2.0, 3.0])]
            
            # Remove numeric-only Item IDs (sequence numbers)
            df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
            
            # Clean descriptions
            df['Item_Description_Clean'] = df['Item_Description'].apply(clean_description)
            df = df[df['Item_Description_Clean'].str.len() > 0]
            
            print(f"    ✅ Processed {len(df)} valid items")
            
            # Add coordinate information for each item
            for row_idx, row in df.iterrows():
                item_info = {
                    'file_name': file,
                    'file_index': file_idx,
                    'original_row': row_idx + 2,  # +2 because Excel starts at 1 and has header
                    'sheet_name': xls.sheet_names[1],
                    'level': str(row['Level']),
                    'level_numeric': row['Level_Numeric'],
                    'sequence': str(row['Sequence']),
                    'item_id': str(row['Item_ID']).strip(),
                    'item_description': str(row['Item_Description']).strip(),
                    'item_description_clean': row['Item_Description_Clean'],
                    'item_id_clean': str(row['Item_ID']).strip().lower()
                }
                all_items.append(item_info)
                
        except Exception as e:
            print(f"    ❌ Error processing {file}: {e}")
            continue
    
    print(f"✅ Total items loaded: {len(all_items)} from {len(excel_files)} files")
    return all_items

def find_perfect_matches(all_items):
    """Find items with same ID and same description across different documents"""
    print("\n🎯 Finding perfect matches (same ID + same description, different documents)...")
    
    # Group by Item ID and Description
    groups = defaultdict(list)
    
    for item in all_items:
        key = (item['item_id_clean'], item['item_description_clean'])
        groups[key].append(item)
    
    perfect_matches = []
    
    for (item_id, description), items in groups.items():
        if len(items) > 1:
            # Check if items are from different files
            files = set(item['file_name'] for item in items)
            if len(files) > 1:  # Cross-file matches
                # Create all pairs
                for i in range(len(items)):
                    for j in range(i + 1, len(items)):
                        item1, item2 = items[i], items[j]
                        
                        if item1['file_name'] != item2['file_name']:  # Different files
                            match = {
                                'match_type': 'perfect_cross_file',
                                'item_id_1': item1['item_id'],
                                'item_id_2': item2['item_id'],
                                'description_1': item1['item_description'],
                                'description_2': item2['item_description'],
                                'file_1': item1['file_name'],
                                'file_2': item2['file_name'],
                                'sheet_1': item1['sheet_name'],
                                'sheet_2': item2['sheet_name'],
                                'row_1': item1['original_row'],
                                'row_2': item2['original_row'],
                                'level_1': item1['level'],
                                'level_2': item2['level'],
                                'sequence_1': item1['sequence'],
                                'sequence_2': item2['sequence'],
                                'level_numeric_1': item1['level_numeric'],
                                'level_numeric_2': item2['level_numeric'],
                                'id_match': True,
                                'description_match': True,
                                'cross_file': True
                            }
                            perfect_matches.append(match)
    
    print(f"✅ Found {len(perfect_matches)} perfect cross-file matches")
    return perfect_matches

def find_description_matches(all_items):
    """Find items with same description but different IDs (potential alternatives)"""
    print("\n🔍 Finding description matches (same description + different ID)...")
    
    # Group by description only
    desc_groups = defaultdict(list)
    
    for item in all_items:
        # Only consider descriptions with reasonable length (avoid too generic ones)
        if len(item['item_description_clean']) > 10:
            desc_groups[item['item_description_clean']].append(item)
    
    description_matches = []
    
    for description, items in desc_groups.items():
        if len(items) > 1:
            # Get unique Item IDs for this description
            unique_ids = {}
            for item in items:
                if item['item_id_clean'] not in unique_ids:
                    unique_ids[item['item_id_clean']] = []
                unique_ids[item['item_id_clean']].append(item)
            
            # If we have multiple different IDs for same description
            if len(unique_ids) > 1:
                id_list = list(unique_ids.keys())
                
                # Create pairs of different IDs with same description
                for i in range(len(id_list)):
                    for j in range(i + 1, len(id_list)):
                        id1, id2 = id_list[i], id_list[j]
                        
                        # Take first occurrence of each ID
                        items1 = unique_ids[id1]
                        items2 = unique_ids[id2]
                        
                        # Create match for each combination
                        for item1 in items1[:2]:  # Limit to first 2 occurrences
                            for item2 in items2[:2]:
                                match = {
                                    'match_type': 'description_different_id',
                                    'item_id_1': item1['item_id'],
                                    'item_id_2': item2['item_id'],
                                    'description_1': item1['item_description'],
                                    'description_2': item2['item_description'],
                                    'file_1': item1['file_name'],
                                    'file_2': item2['file_name'],
                                    'sheet_1': item1['sheet_name'],
                                    'sheet_2': item2['sheet_name'],
                                    'row_1': item1['original_row'],
                                    'row_2': item2['original_row'],
                                    'level_1': item1['level'],
                                    'level_2': item2['level'],
                                    'sequence_1': item1['sequence'],
                                    'sequence_2': item2['sequence'],
                                    'level_numeric_1': item1['level_numeric'],
                                    'level_numeric_2': item2['level_numeric'],
                                    'id_match': False,
                                    'description_match': True,
                                    'cross_file': item1['file_name'] != item2['file_name'],
                                    'description_length': len(item1['item_description_clean'])
                                }
                                description_matches.append(match)
    
    # Sort by description length (longer descriptions are more meaningful)
    description_matches.sort(key=lambda x: x['description_length'], reverse=True)
    
    print(f"✅ Found {len(description_matches)} description matches with different IDs")
    return description_matches

def save_results(perfect_matches, description_matches):
    """Save results to separate Excel files with full coordinate tracking"""
    print("\n💾 Saving results to Excel files...")
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Save perfect matches
    if perfect_matches:
        perfect_df = pd.DataFrame(perfect_matches)
        perfect_file = os.path.join(output_folder, 'perfect_matches_same_id_description.xlsx')
        
        with pd.ExcelWriter(perfect_file, engine='openpyxl') as writer:
            perfect_df.to_excel(writer, sheet_name='Perfect_Matches', index=False)
            
            # Create summary sheet
            summary_data = {
                'Metric': [
                    'Total Perfect Matches',
                    'Cross-File Matches',
                    'Unique Item IDs',
                    'Files Involved'
                ],
                'Count': [
                    len(perfect_df),
                    perfect_df['cross_file'].sum(),
                    len(set(perfect_df['item_id_1'].tolist() + perfect_df['item_id_2'].tolist())),
                    len(set(perfect_df['file_1'].tolist() + perfect_df['file_2'].tolist()))
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        print(f"✅ Perfect matches saved: {perfect_file}")
        print(f"   📊 {len(perfect_matches)} perfect matches found")
    
    # Save description matches
    if description_matches:
        desc_df = pd.DataFrame(description_matches)
        desc_file = os.path.join(output_folder, 'description_matches_different_ids.xlsx')
        
        with pd.ExcelWriter(desc_file, engine='openpyxl') as writer:
            desc_df.to_excel(writer, sheet_name='Description_Matches', index=False)
            
            # Create summary sheet
            summary_data = {
                'Metric': [
                    'Total Description Matches',
                    'Cross-File Matches',
                    'Same-File Matches',
                    'Unique Descriptions',
                    'Files Involved'
                ],
                'Count': [
                    len(desc_df),
                    desc_df['cross_file'].sum(),
                    len(desc_df) - desc_df['cross_file'].sum(),
                    len(set(desc_df['description_1'])),
                    len(set(desc_df['file_1'].tolist() + desc_df['file_2'].tolist()))
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
            # Top descriptions by frequency
            desc_counts = {}
            for _, row in desc_df.iterrows():
                desc = row['description_1'][:100]  # Truncate for display
                desc_counts[desc] = desc_counts.get(desc, 0) + 1
            
            top_descriptions = sorted(desc_counts.items(), key=lambda x: x[1], reverse=True)[:20]
            top_desc_df = pd.DataFrame(top_descriptions, columns=['Description', 'Match_Count'])
            top_desc_df.to_excel(writer, sheet_name='Top_Descriptions', index=False)
        
        print(f"✅ Description matches saved: {desc_file}")
        print(f"   📊 {len(description_matches)} description matches found")

def create_summary_report(perfect_matches, description_matches, all_items):
    """Create a comprehensive text summary report"""
    print("\n📝 Creating summary report...")
    
    output_folder = 'output'
    summary_file = os.path.join(output_folder, 'comprehensive_match_summary.txt')
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("COMPREHENSIVE BOM MATCH ANALYSIS SUMMARY\n")
        f.write("=" * 60 + "\n")
        f.write(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Items Analyzed: {len(all_items):,}\n")
        f.write(f"Files Processed: {len(set(item['file_name'] for item in all_items))}\n\n")
        
        # Perfect matches section
        f.write("1. PERFECT MATCHES (Same ID + Same Description, Different Documents)\n")
        f.write("=" * 70 + "\n")
        f.write(f"Total Perfect Matches: {len(perfect_matches):,}\n")
        
        if perfect_matches:
            f.write("\nTop 10 Perfect Matches:\n")
            for i, match in enumerate(perfect_matches[:10], 1):
                f.write(f"\n{i:2d}. Item ID: {match['item_id_1']}\n")
                f.write(f"    Description: {match['description_1'][:80]}...\n")
                f.write(f"    File 1: {match['file_1']} (Row {match['row_1']}, Level {match['level_1']})\n")
                f.write(f"    File 2: {match['file_2']} (Row {match['row_2']}, Level {match['level_2']})\n")
        
        # Description matches section
        f.write(f"\n\n2. DESCRIPTION MATCHES (Same Description + Different IDs)\n")
        f.write("=" * 60 + "\n")
        f.write(f"Total Description Matches: {len(description_matches):,}\n")
        
        if description_matches:
            f.write("\nTop 10 Description Matches (by description length):\n")
            for i, match in enumerate(description_matches[:10], 1):
                f.write(f"\n{i:2d}. Description: {match['description_1'][:80]}...\n")
                f.write(f"    ID 1: {match['item_id_1']} | File: {match['file_1']} (Row {match['row_1']})\n")
                f.write(f"    ID 2: {match['item_id_2']} | File: {match['file_2']} (Row {match['row_2']})\n")
                f.write(f"    Cross-file: {match['cross_file']}\n")
    
    print(f"✅ Summary report saved: {summary_file}")

def main():
    print("🚀 Starting Comprehensive Match Analysis")
    print("=" * 60)
    print("🎯 Creating separate analyses for:")
    print("   1. Perfect matches (same ID + same description, different documents)")
    print("   2. Description matches (same description + different ID)")
    print("   📍 Full coordinate tracking included")
    
    # Load all BOM data with coordinates
    all_items = load_all_bom_data_with_coordinates()
    
    if not all_items:
        print("❌ No valid items found!")
        return
    
    # Find perfect matches
    perfect_matches = find_perfect_matches(all_items)
    
    # Find description matches
    description_matches = find_description_matches(all_items)
    
    # Save results
    save_results(perfect_matches, description_matches)
    
    # Create summary report
    create_summary_report(perfect_matches, description_matches, all_items)
    
    print(f"\n🎉 Comprehensive Match Analysis Complete!")
    print("=" * 60)
    print(f"📊 Results Summary:")
    print(f"   Perfect matches: {len(perfect_matches):,}")
    print(f"   Description matches: {len(description_matches):,}")
    print(f"   Total items analyzed: {len(all_items):,}")
    print(f"\n📁 Files created:")
    print(f"   • perfect_matches_same_id_description.xlsx")
    print(f"   • description_matches_different_ids.xlsx") 
    print(f"   • comprehensive_match_summary.txt")

if __name__ == "__main__":
    main()
