#!/usr/bin/env python3
"""
Quick test to verify required packages are available
"""

print("🧪 Testing required packages...")

try:
    import pandas as pd
    print("✅ pandas:", pd.__version__)
except ImportError as e:
    print("❌ pandas not available:", e)

try:
    import numpy as np
    print("✅ numpy:", np.__version__)
except ImportError as e:
    print("❌ numpy not available:", e)

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    print("✅ scikit-learn: Available")
except ImportError as e:
    print("❌ scikit-learn not available:", e)

try:
    import openpyxl
    print("✅ openpyxl:", openpyxl.__version__)
except ImportError as e:
    print("❌ openpyxl not available:", e)

print("\n🔍 Testing Excel file reading...")
try:
    # Test reading one of your Excel files
    import os
    input_folder = 'input'
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    
    if excel_files:
        test_file = excel_files[0]
        print(f"📄 Testing with: {test_file}")
        
        file_path = os.path.join(input_folder, test_file)
        xls = pd.ExcelFile(file_path)
        print(f"📊 Sheets available: {xls.sheet_names}")
        
        if len(xls.sheet_names) >= 2:
            df = pd.read_excel(file_path, sheet_name=1)
            print(f"📋 Second sheet shape: {df.shape}")
            print(f"📋 Columns: {list(df.columns)}")
            
            # Show first few rows of first 4 columns
            if len(df.columns) >= 4:
                df_sample = df.iloc[:5, :4]
                print(f"📋 Sample data (first 5 rows, first 4 columns):")
                print(df_sample.to_string())
            else:
                print(f"⚠️  Only {len(df.columns)} columns available")
        else:
            print("❌ File doesn't have a second sheet")
    else:
        print("❌ No Excel files found in input folder")
        
except Exception as e:
    print(f"❌ Error testing Excel file: {e}")

print("\n🎯 Package test complete!")
