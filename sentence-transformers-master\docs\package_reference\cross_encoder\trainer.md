
# Trainer

## CrossEncoderTrainer

```{eval-rst}
.. autoclass:: sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer
    :members:
    :inherited-members:
    :exclude-members: autocast_smart_context_manager, collect_features, compute_loss_context_manager, evaluation_loop, floating_point_ops, get_decay_parameter_names, get_optimizer_cls_and_kwargs, init_hf_repo, log_metrics, metrics_format, num_examples, num_tokens, predict, prediction_loop, prediction_step, save_metrics, save_state, training_step
```