# CrossEncoder

## CrossEncoder
For an introduction to Cross-Encoders, see [Cross-Encoders](../../cross_encoder/usage/usage.rst).
```{eval-rst}
.. autoclass:: sentence_transformers.cross_encoder.CrossEncoder
   :members:
   :inherited-members: fit, old_fit
   :exclude-members: save, add_module, apply, buffers, children, extra_repr, forward, get_buffer, get_extra_state, get_parameter, get_submodule, ipu, load_state_dict, modules, named_buffers, named_children, named_modules, named_parameters, parameters, register_backward_hook, register_buffer, register_forward_hook, register_forward_pre_hook, register_full_backward_hook, register_full_backward_pre_hook, register_load_state_dict_post_hook, register_module, register_parameter, register_state_dict_pre_hook, requires_grad_, set_extra_state, share_memory, state_dict, to_empty, type, xpu, zero_grad
```

## CrossEncoderModelCardData
```{eval-rst}
.. autoclass:: sentence_transformers.cross_encoder.model_card.CrossEncoderModelCardData
```
