#!/usr/bin/env python3
"""
Restore Original BOM
Remove PowerPoint enrichment and restore original BOM clustering
"""

import pandas as pd
import numpy as np
import os
import shutil
from datetime import datetime

def backup_current_files():
    """Backup current files with PowerPoint integration"""
    print("💾 Creating backup of PowerPoint-enriched files...")
    
    output_folder = 'output'
    backup_folder = f'backup_powerpoint_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    os.makedirs(backup_folder, exist_ok=True)
    
    files_to_backup = [
        'bom_with_powerpoint_integration.xlsx',
        'ppt_enhanced_cluster_analysis.xlsx',
        'pdf_powerpoint_components.csv',
        'pdf_extraction_detailed.xlsx',
        'colpali_extraction_analysis.png'
    ]
    
    backed_up = []
    for file in files_to_backup:
        source = os.path.join(output_folder, file)
        if os.path.exists(source):
            dest = os.path.join(backup_folder, file)
            shutil.copy2(source, dest)
            backed_up.append(file)
    
    print(f"✅ Backed up {len(backed_up)} files to: {backup_folder}")
    return backup_folder

def load_original_bom():
    """Load original BOM data without PowerPoint enrichment"""
    print("📊 Loading original Artist BOM data...")
    
    artist_file = r"C:\Users\<USER>\OneDrive - GE HealthCare\Documents\BOM_Cluster\input\4516256_0d1582e3-6687-4d88-94c5-4c98b07e76cb_1747378299412.xlsx"
    
    try:
        # Load exactly as in the original integration script
        df = pd.read_excel(artist_file, sheet_name=1)
        df = df.iloc[:, :4]
        df.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
        df = df.dropna(subset=['F_E_Line', 'Item_ID', 'Item_Description'])
        df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
        
        print(f"✅ Loaded {len(df)} original Artist BOM items")
        return df
        
    except Exception as e:
        print(f"❌ Error loading original BOM data: {e}")
        return None

def create_original_features(bom_df):
    """Create original BOM features without PowerPoint enrichment"""
    print("🔧 Creating original BOM features...")
    
    # Original BOM features only (no PowerPoint columns)
    bom_df['FE_Level_1'] = bom_df['F_E_Line'].astype(str).str.split('-').str[0].astype(float)
    bom_df['FE_Level_2'] = bom_df['F_E_Line'].astype(str).str.split('-').str[1].astype(float)
    bom_df['FE_Level_3'] = bom_df['F_E_Line'].astype(str).str.split('-').str[2].astype(float)
    
    bom_df['Item_Length'] = bom_df['Item_ID'].astype(str).str.len()
    bom_df['Desc_Length'] = bom_df['Item_Description'].astype(str).str.len()
    bom_df['Desc_Word_Count'] = bom_df['Item_Description'].astype(str).str.split().str.len()
    
    print("✅ Original features created (no PowerPoint enrichment)")
    return bom_df

def perform_original_clustering(bom_df):
    """Perform original clustering without PowerPoint features"""
    print("🎯 Performing original clustering (no PowerPoint)...")
    
    from sklearn.cluster import KMeans, DBSCAN
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import silhouette_score
    
    # Select ONLY original features (no PowerPoint)
    feature_columns = [
        'FE_Level_1', 'FE_Level_2', 'FE_Level_3',
        'Item_Length', 'Desc_Length', 'Desc_Word_Count'
    ]
    
    # Prepare data
    X = bom_df[feature_columns].fillna(0)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Test multiple algorithms (original approach)
    algorithms = {
        'original_kmeans_8': KMeans(n_clusters=8, random_state=42, n_init=10),
        'original_kmeans_12': KMeans(n_clusters=12, random_state=42, n_init=10),
        'original_kmeans_33': KMeans(n_clusters=33, random_state=42, n_init=10),
        'original_dbscan_05': DBSCAN(eps=0.5, min_samples=5),
        'original_dbscan_08': DBSCAN(eps=0.8, min_samples=8),
    }
    
    results = {}
    
    for algo_name, algorithm in algorithms.items():
        try:
            labels = algorithm.fit_predict(X_scaled)
            n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
            
            if n_clusters > 1:
                silhouette = silhouette_score(X_scaled, labels)
                
                results[algo_name] = {
                    'labels': labels,
                    'n_clusters': n_clusters,
                    'silhouette_score': silhouette,
                    'noise_points': sum(labels == -1) if -1 in labels else 0
                }
                
                print(f"  {algo_name}: {n_clusters} clusters, silhouette={silhouette:.3f}")
        
        except Exception as e:
            print(f"  ⚠️  {algo_name} failed: {e}")
    
    # Select best result
    if results:
        best_algo = max(results.keys(), key=lambda k: results[k]['silhouette_score'])
        best_result = results[best_algo]
        
        bom_df['Original_Cluster'] = best_result['labels']
        
        print(f"\n🏆 Best original algorithm: {best_algo}")
        print(f"📊 Clusters: {best_result['n_clusters']}")
        print(f"🎯 Silhouette score: {best_result['silhouette_score']:.3f}")
        
        return bom_df, best_result
    
    return bom_df, None

def save_original_results(bom_df, cluster_result, output_folder):
    """Save original BOM results without PowerPoint enrichment"""
    print("💾 Saving original BOM results...")
    
    # Save clean BOM data (no PowerPoint columns)
    original_file = os.path.join(output_folder, 'bom_original_clustering.xlsx')
    bom_df.to_excel(original_file, index=False)
    
    # Save simple CSV
    csv_file = os.path.join(output_folder, 'bom_original_clustering.csv')
    bom_df[['F_E_Line', 'Item_ID', 'Item_Description', 'Original_Cluster']].to_csv(csv_file, index=False)
    
    # Create summary
    if cluster_result:
        summary = {
            'Total_Items': len(bom_df),
            'Clusters': cluster_result['n_clusters'],
            'Silhouette_Score': cluster_result['silhouette_score'],
            'Noise_Points': cluster_result['noise_points'],
            'Features_Used': ['F_E_Line levels', 'Item_Length', 'Desc_Length', 'Desc_Word_Count'],
            'PowerPoint_Integration': 'REMOVED'
        }
        
        summary_file = os.path.join(output_folder, 'original_clustering_summary.json')
        import json
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
    
    print(f"✅ Original results saved:")
    print(f"   📊 Excel: {os.path.basename(original_file)}")
    print(f"   📄 CSV: {os.path.basename(csv_file)}")

def remove_powerpoint_files():
    """Remove PowerPoint-related files"""
    print("🗑️ Removing PowerPoint-related files...")
    
    output_folder = 'output'
    powerpoint_files = [
        'pdf_powerpoint_components.csv',
        'pdf_extraction_detailed.xlsx',
        'bom_with_powerpoint_integration.xlsx',
        'ppt_enhanced_cluster_analysis.xlsx',
        'colpali_extraction_analysis.png',
        'colpali_extraction_detailed.xlsx',
        'colpali_component_mapping.json',
        'colpali_extraction_report.txt'
    ]
    
    # Also remove page images
    for i in range(1, 20):
        powerpoint_files.append(f'page_{i}.png')
    
    removed = []
    for file in powerpoint_files:
        file_path = os.path.join(output_folder, file)
        if os.path.exists(file_path):
            os.remove(file_path)
            removed.append(file)
    
    print(f"✅ Removed {len(removed)} PowerPoint-related files")

def create_restoration_report(backup_folder, original_result, total_items):
    """Create restoration report"""
    print("📋 Creating restoration report...")

    silhouette_score = original_result['silhouette_score'] if original_result else 0
    n_clusters = original_result['n_clusters'] if original_result else 0

    report = f"""
BOM RESTORATION REPORT
=====================

Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

ACTIONS PERFORMED:
- ✅ Backed up PowerPoint-enriched files to: {backup_folder}
- ✅ Removed PowerPoint integration from BOM
- ✅ Restored original BOM clustering
- ✅ Cleaned PowerPoint-related files

ORIGINAL BOM STATUS:
- Total items: {total_items}
- Clusters: {n_clusters}
- Clustering algorithm: Original features only
- Silhouette score: {silhouette_score:.3f}
- Features used: F_E_Line levels, Item_Length, Desc_Length, Desc_Word_Count

POWERPOINT INTEGRATION:
- Status: REMOVED
- Reason: Wrong PCM document used
- Backup location: {backup_folder}

NEXT STEPS:
1. Upload correct PDF: Voyager_30.1_IPM_Global_PCM-21-MAR-2025
2. Run: python pdf_colpali_extractor.py
3. Run: python integrate_powerpoint_data.py

BOM is now ready for correct PowerPoint integration.
"""
    
    report_file = os.path.join('output', 'restoration_report.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ Restoration report saved: {report_file}")

def main():
    """Main restoration function"""
    print("🔄 BOM RESTORATION - REMOVING POWERPOINT INTEGRATION")
    print("=" * 70)
    
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    # Step 1: Backup current PowerPoint-enriched files
    backup_folder = backup_current_files()
    
    # Step 2: Load original BOM data
    bom_df = load_original_bom()
    if bom_df is None:
        print("❌ Failed to load original BOM data")
        return
    
    # Step 3: Create original features (no PowerPoint)
    bom_df = create_original_features(bom_df)
    
    # Step 4: Perform original clustering
    bom_df, cluster_result = perform_original_clustering(bom_df)
    
    # Step 5: Save original results
    save_original_results(bom_df, cluster_result, output_folder)
    
    # Step 6: Remove PowerPoint files
    remove_powerpoint_files()
    
    # Step 7: Create restoration report
    create_restoration_report(backup_folder, cluster_result, len(bom_df))
    
    print(f"\n🎉 BOM Restoration Complete!")
    print("=" * 70)
    print(f"✅ PowerPoint integration REMOVED")
    print(f"✅ Original BOM clustering RESTORED")
    print(f"✅ Files backed up to: {backup_folder}")
    print(f"📁 Clean results in: {os.path.abspath(output_folder)}")
    
    if cluster_result:
        print(f"\n📊 Original BOM Status:")
        print(f"   Items: {len(bom_df)}")
        print(f"   Clusters: {cluster_result['n_clusters']}")
        print(f"   Silhouette: {cluster_result['silhouette_score']:.3f}")
    
    print(f"\n📋 Ready for correct PowerPoint integration!")
    print("   1. Upload correct PDF to input/ folder")
    print("   2. Run: python pdf_colpali_extractor.py")
    print("   3. Run: python integrate_powerpoint_data.py")

if __name__ == "__main__":
    main()
