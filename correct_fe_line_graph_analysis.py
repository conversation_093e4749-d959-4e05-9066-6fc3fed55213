#!/usr/bin/env python3
"""
Correct F-E-Line Graph Analysis
- F-E-Line = Level column value (1-1-2, 2-3-1, etc.)
- Each unique F-E-Line starts a new subgraph
- Order matters - process document sequentially
- When F-E-Line changes, start new branch/subgraph
"""

import os
import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from collections import defaultdict
import json
import re

def extract_level_number(level_str):
    """Extract numeric level from level string"""
    try:
        # Look for first number in the string
        match = re.search(r'(\d+)', str(level_str))
        if match:
            return int(match.group(1))
        return 0
    except:
        return 0

def load_bom_file_sequential(file_path):
    """Load BOM file and process sequentially to track F-E-Line changes"""
    try:
        xls = pd.ExcelFile(file_path)
        if len(xls.sheet_names) < 2:
            return None
            
        df = pd.read_excel(file_path, sheet_name=1)
        
        if len(df.columns) < 4:
            return None
        
        # Column mapping: Level (F-E-Line), Sequence, Item_ID, Description
        df = df.iloc[:, :4]
        df.columns = ['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description']
        
        # Clean data
        df = df.dropna(subset=['F_E_Line', 'Sequence', 'Item_ID', 'Item_Description'])
        df = df[df['F_E_Line'].astype(str).str.strip() != '']
        df = df[df['Item_ID'].astype(str).str.strip() != '']
        df = df[df['Item_Description'].astype(str).str.strip() != '']
        
        # Remove numeric-only Item IDs
        df = df[~df['Item_ID'].astype(str).str.match(r'^\d+$')]
        
        # Add sequential processing info
        df['Row_Index'] = range(len(df))
        df['F_E_Line_Clean'] = df['F_E_Line'].astype(str).str.strip()
        
        # Extract level numbers for hierarchy within each F-E-Line
        df['Level_Number'] = df['F_E_Line'].apply(extract_level_number)
        
        return df
        
    except Exception as e:
        print(f"Error loading file: {e}")
        return None

def create_sequential_bom_graph(df, file_name):
    """Create graph by processing document sequentially and tracking F-E-Line changes"""
    print(f"🔍 Creating sequential graph for: {file_name}")
    
    file_graph = {
        'file_name': file_name,
        'file_root': f"FILE_ROOT_{file_name}",
        'total_items': len(df),
        'nodes': {},
        'edges': [],
        'fe_line_subgraphs': {},
        'fe_line_order': []
    }
    
    # Add file root (grand father of all)
    file_root = file_graph['file_root']
    file_graph['nodes'][file_root] = {
        'id': file_root,
        'level': -1,
        'fe_line': 'FILE_ROOT',
        'description': f"File: {file_name[:30]}...",
        'node_type': 'file_root',
        'row_index': -1
    }
    
    # Process document sequentially
    current_fe_line = None
    current_subgraph = None
    level_stack = {}  # Track parent for each level within current F-E-Line
    
    unique_fe_lines = df['F_E_Line_Clean'].unique()
    print(f"  📊 Found {len(unique_fe_lines)} unique F-E-Lines")
    print(f"  📋 Sample F-E-Lines: {list(unique_fe_lines[:10])}")
    
    for idx, (_, row) in enumerate(df.iterrows()):
        fe_line = row['F_E_Line_Clean']
        level_number = row['Level_Number']
        
        # Check if F-E-Line changed (start new subgraph)
        if fe_line != current_fe_line:
            print(f"    🔄 F-E-Line changed to: {fe_line}")
            current_fe_line = fe_line
            level_stack = {}  # Reset level stack for new F-E-Line
            
            # Track F-E-Line order
            if fe_line not in file_graph['fe_line_order']:
                file_graph['fe_line_order'].append(fe_line)
            
            # Create F-E-Line root node if not exists
            fe_line_root_id = f"FE_ROOT_{fe_line}"
            if fe_line_root_id not in file_graph['nodes']:
                file_graph['nodes'][fe_line_root_id] = {
                    'id': fe_line_root_id,
                    'level': 0,
                    'fe_line': fe_line,
                    'description': f"F-E-Line: {fe_line}",
                    'node_type': 'fe_line_root',
                    'row_index': -1
                }
                
                # Connect F-E-Line root to file root
                file_graph['edges'].append({
                    'parent': file_root,
                    'child': fe_line_root_id,
                    'parent_level': -1,
                    'child_level': 0
                })
                
                # Initialize subgraph tracking
                file_graph['fe_line_subgraphs'][fe_line] = {
                    'root': fe_line_root_id,
                    'items': [],
                    'levels': defaultdict(list)
                }
            
            current_subgraph = file_graph['fe_line_subgraphs'][fe_line]
            level_stack[0] = fe_line_root_id  # F-E-Line root is level 0
        
        # Create node for current item
        item_node_id = f"ITEM_{idx}_{fe_line}_{level_number}"
        
        file_graph['nodes'][item_node_id] = {
            'id': item_node_id,
            'level': level_number,
            'fe_line': fe_line,
            'sequence': str(row['Sequence']),
            'item_id': str(row['Item_ID']),
            'description': str(row['Item_Description'])[:40],
            'full_description': str(row['Item_Description']),
            'node_type': 'component',
            'row_index': row['Row_Index']
        }
        
        # Add to current subgraph
        current_subgraph['items'].append(item_node_id)
        current_subgraph['levels'][level_number].append(item_node_id)
        
        # Determine parent based on level hierarchy within this F-E-Line
        if level_number == 1:
            # Level 1 connects to F-E-Line root (level 0)
            parent_id = level_stack[0]
        else:
            # Find appropriate parent from previous levels
            parent_level = level_number - 1
            while parent_level >= 0:
                if parent_level in level_stack:
                    parent_id = level_stack[parent_level]
                    break
                parent_level -= 1
            else:
                parent_id = level_stack[0]  # Fallback to F-E-Line root
        
        # Create edge
        file_graph['edges'].append({
            'parent': parent_id,
            'child': item_node_id,
            'parent_level': file_graph['nodes'][parent_id]['level'],
            'child_level': level_number
        })
        
        # Update level stack for this level
        level_stack[level_number] = item_node_id
    
    print(f"  ✅ Created {len(file_graph['nodes'])} nodes and {len(file_graph['edges'])} edges")
    print(f"  📊 F-E-Line subgraphs: {len(file_graph['fe_line_subgraphs'])}")
    
    return file_graph

def create_networkx_from_sequential_graph(file_graph):
    """Convert sequential graph to NetworkX"""
    G = nx.DiGraph()
    
    # Add all nodes
    for node_id, node_data in file_graph['nodes'].items():
        G.add_node(node_id,
                   level=node_data['level'],
                   node_type=node_data['node_type'],
                   label=node_data.get('description', ''),
                   fe_line=node_data.get('fe_line', ''),
                   item_id=node_data.get('item_id', ''))
    
    # Add all edges
    for edge in file_graph['edges']:
        G.add_edge(edge['parent'], edge['child'])
    
    return G

def visualize_sequential_bom_graph(G, file_name, output_folder):
    """Create visualization of sequential BOM graph"""
    print(f"📊 Creating sequential visualization for: {file_name}")
    
    plt.figure(figsize=(24, 18))
    
    # Create layout
    pos = create_sequential_layout(G)
    
    # Define colors by node type and level
    node_colors = []
    node_sizes = []
    
    for node in G.nodes():
        node_type = G.nodes[node].get('node_type', 'component')
        level = G.nodes[node].get('level', 0)
        
        if node_type == 'file_root':
            node_colors.append('red')
            node_sizes.append(1000)
        elif node_type == 'fe_line_root':
            node_colors.append('orange')
            node_sizes.append(600)
        else:  # component
            if level == 1:
                node_colors.append('lightblue')
                node_sizes.append(200)
            elif level == 2:
                node_colors.append('lightgreen')
                node_sizes.append(150)
            elif level == 3:
                node_colors.append('lightyellow')
                node_sizes.append(100)
            else:
                node_colors.append('lightgray')
                node_sizes.append(80)
    
    # Draw graph
    nx.draw(G, pos, 
            node_color=node_colors,
            node_size=node_sizes,
            with_labels=False,
            arrows=True,
            arrowsize=10,
            edge_color='gray',
            alpha=0.7,
            arrowstyle='->')
    
    # Add labels for important nodes only
    important_nodes = {}
    for node in G.nodes():
        node_type = G.nodes[node].get('node_type', 'component')
        if node_type in ['file_root', 'fe_line_root']:
            label = G.nodes[node].get('label', '')[:20]
            important_nodes[node] = label
    
    nx.draw_networkx_labels(G, pos, important_nodes, font_size=6, font_weight='bold')
    
    # Legend
    legend_elements = [
        mpatches.Patch(color='red', label='File Root (Grand Father)'),
        mpatches.Patch(color='orange', label='F-E-Line Roots (Level 0)'),
        mpatches.Patch(color='lightblue', label='Level 1 Components'),
        mpatches.Patch(color='lightgreen', label='Level 2 Components'),
        mpatches.Patch(color='lightyellow', label='Level 3 Components'),
        mpatches.Patch(color='lightgray', label='Other Levels')
    ]
    
    plt.legend(handles=legend_elements, loc='upper right', fontsize=10)
    plt.title(f'Sequential BOM Graph (F-E-Line Subgraphs): {file_name}', 
              fontsize=16, fontweight='bold')
    
    # Save
    safe_filename = re.sub(r'[^\w\-_\.]', '_', file_name)
    viz_file = os.path.join(output_folder, f'sequential_bom_graph_{safe_filename}.png')
    plt.savefig(viz_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Sequential visualization saved: {viz_file}")
    return viz_file

def create_sequential_layout(G):
    """Create layout for sequential graph"""
    pos = {}
    
    # Group by level
    levels = defaultdict(list)
    for node in G.nodes():
        level = G.nodes[node].get('level', 0)
        levels[level].append(node)
    
    # Position nodes level by level
    y_spacing = 3.0
    
    for level, nodes in levels.items():
        y = -level * y_spacing
        
        # Distribute horizontally
        if len(nodes) == 1:
            x_positions = [0]
        else:
            # Adaptive spacing based on number of nodes
            x_spacing = min(30.0 / len(nodes), 3.0)
            total_width = (len(nodes) - 1) * x_spacing
            x_positions = [i * x_spacing - total_width/2 for i in range(len(nodes))]
        
        for i, node in enumerate(nodes):
            pos[node] = (x_positions[i], y)
    
    return pos

def main():
    print("🚀 Starting Correct F-E-Line Graph Analysis")
    print("=" * 70)
    print("🎯 F-E-Line = Level column value (any format)")
    print("📊 Each unique F-E-Line starts new subgraph")
    print("🔄 Processing sequentially - order matters")
    
    input_folder = 'input'
    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)
    
    excel_files = [f for f in os.listdir(input_folder) if f.endswith(('.xlsx', '.xls'))]
    print(f"📁 Found {len(excel_files)} Excel files")
    
    # Process first 2 files for demonstration
    for i, file in enumerate(excel_files[:2], 1):
        print(f"\n[{i}/2] Processing: {file}")
        file_path = os.path.join(input_folder, file)
        
        # Load data sequentially
        df = load_bom_file_sequential(file_path)
        if df is None:
            print(f"❌ Failed to load {file}")
            continue
        
        print(f"✅ Loaded {len(df)} items")
        
        # Create sequential graph
        file_graph = create_sequential_bom_graph(df, file)
        
        # Convert to NetworkX
        G = create_networkx_from_sequential_graph(file_graph)
        print(f"📊 Sequential graph: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
        
        # Visualize
        visualize_sequential_bom_graph(G, file, output_folder)
        
        # Save detailed data
        safe_filename = re.sub(r'[^\w\-_\.]', '_', file)
        json_file = os.path.join(output_folder, f'sequential_bom_graph_data_{safe_filename}.json')
        
        json_data = {
            'file_name': file_graph['file_name'],
            'total_items': file_graph['total_items'],
            'total_nodes': len(file_graph['nodes']),
            'total_edges': len(file_graph['edges']),
            'fe_line_count': len(file_graph['fe_line_subgraphs']),
            'fe_line_order': file_graph['fe_line_order'],
            'fe_line_summary': {
                fe_line: {
                    'item_count': len(data['items']),
                    'level_distribution': {str(k): len(v) for k, v in data['levels'].items()}
                }
                for fe_line, data in file_graph['fe_line_subgraphs'].items()
            }
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Sequential graph data saved: {json_file}")
    
    print(f"\n🎉 Correct F-E-Line Graph Analysis Complete!")
    print("=" * 70)
    print(f"📁 Files created in: {os.path.abspath(output_folder)}")
    print(f"   • sequential_bom_graph_*.png - Sequential visualizations")
    print(f"   • sequential_bom_graph_data_*.json - Sequential graph data")

if __name__ == "__main__":
    main()
