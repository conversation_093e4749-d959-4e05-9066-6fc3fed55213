# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
bom_analysis_env/
venv/
env/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# PyCharm
.idea/

# VS Code
.vscode/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Data files (be careful with sensitive data)
*.xlsx
*.xls
*.csv
!example_*.xlsx
!sample_*.xlsx

# Output files
output/
results/
reports/

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Model files (large files)
*.bin
*.safetensors
*.h5
*.onnx
*.msgpack
*.ot

# Exclude large model directories but keep structure
all-MiniLM-L6-v2/*.bin
all-MiniLM-L6-v2/*.safetensors
all-MiniLM-L6-v2/*.h5
all-MiniLM-L6-v2/onnx/
all-MiniLM-L6-v2/openvino/
flan-t5-base/*.bin
flan-t5-base/*.safetensors
flan-t5-base/*.h5
flan-t5-base/*.msgpack

# Keep config files for models
!*/config.json
!*/tokenizer.json
!*/tokenizer_config.json
!*/special_tokens_map.json
!*/vocab.txt
!*/README.md
