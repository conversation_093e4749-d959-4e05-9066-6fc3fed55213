import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
from matplotlib.gridspec import GridSpec
import warnings
warnings.filterwarnings('ignore')

class PerformanceSlideDashboard:
    def __init__(self):
        """Initialize the dashboard"""
        self.df = None
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#2ECC71',
            'warning': '#F39C12',
            'danger': '#E74C3C',
            'info': '#3498DB',
            'light': '#F8F9FA',
            'dark': '#2C3E50'
        }
        self.metrics = {}
        
    def load_data(self, filepath='output/voyager_complete_bom_pcm_table.xlsx'):
        """Load and prepare data"""
        try:
            self.df = pd.read_excel(filepath, sheet_name='Complete_Table')
            print(f"✅ Data loaded: {len(self.df)} rows")
            self.prepare_data()
            return True
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False
    
    def prepare_data(self):
        """Prepare data for analysis"""
        # Clean and prepare data
        self.df['has_pcm_match'] = (self.df['Item_ID_PCM'] != '') & (self.df['Item_ID_PCM'].notna())
        self.df['has_similarity_score'] = self.df['Similarity_Score'] > 0
        
        # Define match types
        self.df['match_type'] = 'No Match'
        self.df.loc[self.df['Similarity_Score'] == 1.0, 'match_type'] = 'Exact Match'
        self.df.loc[(self.df['Similarity_Score'] < 1.0) & (self.df['Similarity_Score'] >= 0.6), 'match_type'] = 'Similar Match'
        
        # Quality categories
        self.df['quality_category'] = 'None'
        self.df.loc[(self.df['Similarity_Score'] >= 0.6) & (self.df['Similarity_Score'] < 0.7), 'quality_category'] = 'Acceptable'
        self.df.loc[(self.df['Similarity_Score'] >= 0.7) & (self.df['Similarity_Score'] < 0.8), 'quality_category'] = 'Good'
        self.df.loc[(self.df['Similarity_Score'] >= 0.8) & (self.df['Similarity_Score'] < 0.9), 'quality_category'] = 'Very Good'
        self.df.loc[self.df['Similarity_Score'] >= 0.9, 'quality_category'] = 'Excellent'
        
        self.calculate_performance_metrics()
    
    def calculate_performance_metrics(self):
        """Calculate all performance metrics with proper formulas"""
        # Basic counts
        total_items = len(self.df)
        matched_items = len(self.df[self.df['has_pcm_match']])
        unmatched_items = total_items - matched_items
        
        # Match type counts
        exact_matches = len(self.df[self.df['match_type'] == 'Exact Match'])
        similar_matches = len(self.df[self.df['match_type'] == 'Similar Match'])
        no_matches = len(self.df[self.df['match_type'] == 'No Match'])
        
        # Quality distribution
        quality_counts = self.df['quality_category'].value_counts()
        
        # Advanced metrics calculation
        # For demonstration, we'll use similarity-based estimates
        # In production, you'd use manual validation data
        
        # Estimate True Positives (based on high-confidence matches)
        high_confidence_matches = len(self.df[self.df['Similarity_Score'] >= 0.8])
        medium_confidence_matches = len(self.df[(self.df['Similarity_Score'] >= 0.7) & (self.df['Similarity_Score'] < 0.8)])
        low_confidence_matches = len(self.df[(self.df['Similarity_Score'] >= 0.6) & (self.df['Similarity_Score'] < 0.7)])
        
        # Estimate error rates based on confidence levels
        estimated_tp_high = high_confidence_matches * 0.95  # 95% accuracy for high confidence
        estimated_tp_medium = medium_confidence_matches * 0.85  # 85% accuracy for medium confidence
        estimated_tp_low = low_confidence_matches * 0.70  # 70% accuracy for low confidence
        
        estimated_true_positives = estimated_tp_high + estimated_tp_medium + estimated_tp_low
        
        # Estimate False Positives
        estimated_fp_high = high_confidence_matches * 0.05
        estimated_fp_medium = medium_confidence_matches * 0.15
        estimated_fp_low = low_confidence_matches * 0.30
        
        estimated_false_positives = estimated_fp_high + estimated_fp_medium + estimated_fp_low
        
        # Estimate False Negatives (based on expected match rate)
        expected_match_rate = 0.85  # Assume 85% of items should have matches
        expected_matches = total_items * expected_match_rate
        estimated_false_negatives = max(0, expected_matches - matched_items)
        
        # Calculate performance metrics
        precision = estimated_true_positives / (estimated_true_positives + estimated_false_positives) if (estimated_true_positives + estimated_false_positives) > 0 else 0
        recall = estimated_true_positives / (estimated_true_positives + estimated_false_negatives) if (estimated_true_positives + estimated_false_negatives) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        # Coverage metrics
        coverage_rate = (matched_items / total_items) * 100
        
        # Quality metrics
        if matched_items > 0:
            avg_similarity = self.df[self.df['has_similarity_score']]['Similarity_Score'].mean()
            median_similarity = self.df[self.df['has_similarity_score']]['Similarity_Score'].median()
            std_similarity = self.df[self.df['has_similarity_score']]['Similarity_Score'].std()
        else:
            avg_similarity = median_similarity = std_similarity = 0
        
        # Store all metrics
        self.metrics = {
            # Basic counts
            'total_items': int(total_items),
            'matched_items': int(matched_items),
            'unmatched_items': int(unmatched_items),
            'exact_matches': int(exact_matches),
            'similar_matches': int(similar_matches),
            'no_matches': int(no_matches),
            
            # Performance metrics
            'coverage_rate': coverage_rate,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            
            # Quality metrics
            'avg_similarity': avg_similarity,
            'median_similarity': median_similarity,
            'std_similarity': std_similarity,
            
            # Error analysis
            'estimated_true_positives': estimated_true_positives,
            'estimated_false_positives': estimated_false_positives,
            'estimated_false_negatives': estimated_false_negatives,
            
            # Quality distribution
            'quality_excellent': quality_counts.get('Excellent', 0),
            'quality_very_good': quality_counts.get('Very Good', 0),
            'quality_good': quality_counts.get('Good', 0),
            'quality_acceptable': quality_counts.get('Acceptable', 0),
            
            # Confidence levels
            'high_confidence_matches': int(high_confidence_matches),
            'medium_confidence_matches': int(medium_confidence_matches),
            'low_confidence_matches': int(low_confidence_matches)
        }
    
    def create_performance_slide(self):
        """Create a comprehensive performance slide"""
        # Create figure with custom layout
        fig = plt.figure(figsize=(20, 12))
        fig.patch.set_facecolor('white')
        
        # Create grid layout
        gs = GridSpec(4, 6, figure=fig, hspace=0.3, wspace=0.3)
        
        # Title
        title_ax = fig.add_subplot(gs[0, :])
        title_ax.text(0.5, 0.5, 'Algorithm Performance Dashboard', 
                     ha='center', va='center', fontsize=28, fontweight='bold',
                     color=self.colors['dark'])
        title_ax.axis('off')
        
        # KPI Cards (Row 1)
        kpi_metrics = [
            ('Total Items', self.metrics['total_items'], 'items', self.colors['info']),
            ('Coverage Rate', f"{self.metrics['coverage_rate']:.1f}", '%', self.colors['success']),
            ('Precision', f"{self.metrics['precision']:.3f}", '', self.colors['primary']),
            ('Recall', f"{self.metrics['recall']:.3f}", '', self.colors['secondary']),
            ('F1-Score', f"{self.metrics['f1_score']:.3f}", '', self.colors['warning']),
            ('Avg Similarity', f"{self.metrics['avg_similarity']:.3f}", '', self.colors['info'])
        ]
        
        for i, (title, value, unit, color) in enumerate(kpi_metrics):
            ax = fig.add_subplot(gs[1, i])
            self.create_kpi_card(ax, title, value, unit, color)
        
        # Match Type Distribution (Row 2, Left)
        match_ax = fig.add_subplot(gs[2, :2])
        self.create_match_type_chart(match_ax)
        
        # Quality Distribution (Row 2, Center)
        quality_ax = fig.add_subplot(gs[2, 2:4])
        self.create_quality_chart(quality_ax)
        
        # Error Analysis (Row 2, Right)
        error_ax = fig.add_subplot(gs[2, 4:])
        self.create_error_analysis_chart(error_ax)
        
        # Performance Metrics Comparison (Row 3, Left)
        metrics_ax = fig.add_subplot(gs[3, :2])
        self.create_metrics_comparison(metrics_ax)
        
        # Confidence Distribution (Row 3, Center)
        conf_ax = fig.add_subplot(gs[3, 2:4])
        self.create_confidence_chart(conf_ax)
        
        # Summary Statistics (Row 3, Right)
        summary_ax = fig.add_subplot(gs[3, 4:])
        self.create_summary_table(summary_ax)
        
        plt.tight_layout()
        plt.savefig('output/performance_slide_dashboard.png', dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.show()
        
        return fig
    
    def create_kpi_card(self, ax, title, value, unit, color):
        """Create a KPI card"""
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        
        # Create card background
        card = Rectangle((0.05, 0.1), 0.9, 0.8, facecolor=color, alpha=0.1, edgecolor=color, linewidth=2)
        ax.add_patch(card)
        
        # Add title
        ax.text(0.5, 0.75, title, ha='center', va='center', fontsize=10, fontweight='bold', color=color)
        
        # Add value
        ax.text(0.5, 0.4, f"{value}{unit}", ha='center', va='center', fontsize=16, fontweight='bold', color=color)
        
        ax.axis('off')
    
    def create_match_type_chart(self, ax):
        """Create match type distribution chart"""
        categories = ['Exact Match', 'Similar Match', 'No Match']
        values = [self.metrics['exact_matches'], self.metrics['similar_matches'], self.metrics['no_matches']]
        colors = [self.colors['success'], self.colors['warning'], self.colors['danger']]
        
        wedges, texts, autotexts = ax.pie(values, labels=categories, autopct='%1.1f%%', 
                                         colors=colors, startangle=90)
        
        # Customize text
        for text in texts:
            text.set_fontsize(10)
            text.set_fontweight('bold')
        
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        ax.set_title('Match Type Distribution', fontsize=14, fontweight='bold', pad=20)
    
    def create_quality_chart(self, ax):
        """Create quality distribution chart"""
        categories = ['Excellent\n(≥0.9)', 'Very Good\n(0.8-0.9)', 'Good\n(0.7-0.8)', 'Acceptable\n(0.6-0.7)']
        values = [self.metrics['quality_excellent'], self.metrics['quality_very_good'], 
                 self.metrics['quality_good'], self.metrics['quality_acceptable']]
        colors = [self.colors['success'], self.colors['info'], self.colors['warning'], self.colors['danger']]
        
        bars = ax.bar(categories, values, color=colors, alpha=0.7)
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5, f'{int(value)}', 
                   ha='center', va='bottom', fontweight='bold')
        
        ax.set_title('Quality Distribution', fontsize=14, fontweight='bold')
        ax.set_ylabel('Number of Items')
        ax.grid(True, alpha=0.3)
    
    def create_error_analysis_chart(self, ax):
        """Create error analysis chart"""
        categories = ['True\nPositives', 'False\nPositives', 'False\nNegatives']
        values = [self.metrics['estimated_true_positives'], 
                 self.metrics['estimated_false_positives'], 
                 self.metrics['estimated_false_negatives']]
        colors = [self.colors['success'], self.colors['danger'], self.colors['warning']]
        
        bars = ax.bar(categories, values, color=colors, alpha=0.7)
        
        # Add value labels
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1, f'{int(value)}', 
                   ha='center', va='bottom', fontweight='bold')
        
        ax.set_title('Error Analysis (Estimated)', fontsize=14, fontweight='bold')
        ax.set_ylabel('Number of Items')
        ax.grid(True, alpha=0.3)
    
    def create_metrics_comparison(self, ax):
        """Create performance metrics comparison"""
        metrics = ['Precision', 'Recall', 'F1-Score']
        values = [self.metrics['precision'], self.metrics['recall'], self.metrics['f1_score']]
        colors = [self.colors['primary'], self.colors['secondary'], self.colors['warning']]
        
        bars = ax.bar(metrics, values, color=colors, alpha=0.7)
        
        # Add value labels
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01, f'{value:.3f}', 
                   ha='center', va='bottom', fontweight='bold')
        
        ax.set_title('Performance Metrics', fontsize=14, fontweight='bold')
        ax.set_ylabel('Score')
        ax.set_ylim(0, 1)
        ax.grid(True, alpha=0.3)
    
    def create_confidence_chart(self, ax):
        """Create confidence level distribution"""
        categories = ['High\n(≥0.8)', 'Medium\n(0.7-0.8)', 'Low\n(0.6-0.7)']
        values = [self.metrics['high_confidence_matches'], 
                 self.metrics['medium_confidence_matches'], 
                 self.metrics['low_confidence_matches']]
        colors = [self.colors['success'], self.colors['warning'], self.colors['danger']]
        
        bars = ax.bar(categories, values, color=colors, alpha=0.7)
        
        # Add value labels
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5, f'{int(value)}', 
                   ha='center', va='bottom', fontweight='bold')
        
        ax.set_title('Confidence Level Distribution', fontsize=14, fontweight='bold')
        ax.set_ylabel('Number of Matches')
        ax.grid(True, alpha=0.3)
    
    def create_summary_table(self, ax):
        """Create summary statistics table"""
        ax.axis('off')
        
        # Create table data
        table_data = [
            ['Metric', 'Value', 'Formula'],
            ['Coverage Rate', f"{self.metrics['coverage_rate']:.1f}%", 'Matched/Total × 100'],
            ['Precision', f"{self.metrics['precision']:.3f}", 'TP/(TP+FP)'],
            ['Recall', f"{self.metrics['recall']:.3f}", 'TP/(TP+FN)'],
            ['F1-Score', f"{self.metrics['f1_score']:.3f}", '2×(P×R)/(P+R)'],
            ['Avg Similarity', f"{self.metrics['avg_similarity']:.3f}", 'Mean(Similarity)'],
            ['Std Similarity', f"{self.metrics['std_similarity']:.3f}", 'Std(Similarity)']
        ]
        
        # Create table
        table = ax.table(cellText=table_data[1:], colLabels=table_data[0], 
                        cellLoc='center', loc='center', cellColours=None)
        
        # Style the table
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1.2, 2)
        
        # Style header
        for i in range(3):
            table[(0, i)].set_facecolor(self.colors['primary'])
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        # Style data rows
        for i in range(1, len(table_data)):
            for j in range(3):
                if i % 2 == 0:
                    table[(i, j)].set_facecolor('#F8F9FA')
                else:
                    table[(i, j)].set_facecolor('white')
        
        ax.set_title('Performance Summary', fontsize=14, fontweight='bold', pad=20)
    
    def print_performance_report(self):
        """Print comprehensive performance report"""
        print("\n" + "="*80)
        print("🎯 ALGORITHM PERFORMANCE REPORT")
        print("="*80)
        
        print(f"\n📊 BASIC METRICS:")
        print(f"   Total Items: {self.metrics['total_items']:,}")
        print(f"   Matched Items: {self.metrics['matched_items']:,}")
        print(f"   Coverage Rate: {self.metrics['coverage_rate']:.1f}%")
        
        print(f"\n🎯 PERFORMANCE METRICS:")
        print(f"   Precision: {self.metrics['precision']:.3f} (How many selected items are relevant)")
        print(f"   Recall: {self.metrics['recall']:.3f} (How many relevant items are selected)")
        print(f"   F1-Score: {self.metrics['f1_score']:.3f} (Harmonic mean of precision and recall)")
        
        print(f"\n🔍 MATCH ANALYSIS:")
        print(f"   Exact Matches: {self.metrics['exact_matches']:,} ({self.metrics['exact_matches']/self.metrics['total_items']*100:.1f}%)")
        print(f"   Similar Matches: {self.metrics['similar_matches']:,} ({self.metrics['similar_matches']/self.metrics['total_items']*100:.1f}%)")
        print(f"   No Matches: {self.metrics['no_matches']:,} ({self.metrics['no_matches']/self.metrics['total_items']*100:.1f}%)")
        
        print(f"\n📈 QUALITY METRICS:")
        print(f"   Average Similarity: {self.metrics['avg_similarity']:.3f}")
        print(f"   Median Similarity: {self.metrics['median_similarity']:.3f}")
        print(f"   Standard Deviation: {self.metrics['std_similarity']:.3f}")
        
        print(f"\n❌ ERROR ANALYSIS (Estimated):")
        print(f"   True Positives: {self.metrics['estimated_true_positives']:.0f}")
        print(f"   False Positives: {self.metrics['estimated_false_positives']:.0f}")
        print(f"   False Negatives: {self.metrics['estimated_false_negatives']:.0f}")
        
        print(f"\n🔒 CONFIDENCE LEVELS:")
        print(f"   High Confidence (≥0.8): {self.metrics['high_confidence_matches']:,}")
        print(f"   Medium Confidence (0.7-0.8): {self.metrics['medium_confidence_matches']:,}")
        print(f"   Low Confidence (0.6-0.7): {self.metrics['low_confidence_matches']:,}")
        
        print(f"\n📋 RECOMMENDATIONS:")
        if self.metrics['precision'] < 0.8:
            print("   - Consider increasing similarity threshold to reduce false positives")
        if self.metrics['recall'] < 0.8:
            print("   - Consider lowering similarity threshold to reduce false negatives")
        if self.metrics['f1_score'] < 0.8:
            print("   - Balance precision and recall by optimizing similarity threshold")
        
        print("="*80)
    
    def run_dashboard(self):
        """Run the complete dashboard"""
        if not self.load_data():
            return None
        
        # Create the slide
        fig = self.create_performance_slide()
        
        # Print the report
        self.print_performance_report()
        
        # Export metrics
        self.export_metrics()
        
        return fig
    
    def export_metrics(self):
        """Export metrics to JSON and Excel"""
        import json
        
        # Export to JSON
        with open('output/performance_metrics.json', 'w') as f:
            json.dump(self.metrics, f, indent=2, default=str)
        
        # Export to Excel
        metrics_df = pd.DataFrame([self.metrics])
        metrics_df.to_excel('output/performance_metrics.xlsx', index=False)
        
        print(f"✅ Metrics exported to output/performance_metrics.json and .xlsx")

def main():
    """Main function to run the dashboard"""
    print("🚀 ALGORITHM PERFORMANCE SLIDE DASHBOARD")
    print("="*50)
    
    dashboard = PerformanceSlideDashboard()
    fig = dashboard.run_dashboard()
    
    return dashboard

if __name__ == "__main__":
    dashboard = main()